// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_export_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AIGCExportHistoryModel _$AIGCExportHistoryModelFromJson(
    Map<String, dynamic> json) {
  return _AIGCExportHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$AIGCExportHistoryModel {
  List<AIGCExportHistoryItem> get consumptions =>
      throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AIGCExportHistoryModelCopyWith<AIGCExportHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIGCExportHistoryModelCopyWith<$Res> {
  factory $AIGCExportHistoryModelCopyWith(AIGCExportHistoryModel value,
          $Res Function(AIGCExportHistoryModel) then) =
      _$AIGCExportHistoryModelCopyWithImpl<$Res, AIGCExportHistoryModel>;
  @useResult
  $Res call(
      {List<AIGCExportHistoryItem> consumptions,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AIGCExportHistoryModelCopyWithImpl<$Res,
        $Val extends AIGCExportHistoryModel>
    implements $AIGCExportHistoryModelCopyWith<$Res> {
  _$AIGCExportHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? consumptions = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      consumptions: null == consumptions
          ? _value.consumptions
          : consumptions // ignore: cast_nullable_to_non_nullable
              as List<AIGCExportHistoryItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIGCExportHistoryModelImplCopyWith<$Res>
    implements $AIGCExportHistoryModelCopyWith<$Res> {
  factory _$$AIGCExportHistoryModelImplCopyWith(
          _$AIGCExportHistoryModelImpl value,
          $Res Function(_$AIGCExportHistoryModelImpl) then) =
      __$$AIGCExportHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AIGCExportHistoryItem> consumptions,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AIGCExportHistoryModelImplCopyWithImpl<$Res>
    extends _$AIGCExportHistoryModelCopyWithImpl<$Res,
        _$AIGCExportHistoryModelImpl>
    implements _$$AIGCExportHistoryModelImplCopyWith<$Res> {
  __$$AIGCExportHistoryModelImplCopyWithImpl(
      _$AIGCExportHistoryModelImpl _value,
      $Res Function(_$AIGCExportHistoryModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? consumptions = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AIGCExportHistoryModelImpl(
      consumptions: null == consumptions
          ? _value._consumptions
          : consumptions // ignore: cast_nullable_to_non_nullable
              as List<AIGCExportHistoryItem>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIGCExportHistoryModelImpl implements _AIGCExportHistoryModel {
  const _$AIGCExportHistoryModelImpl(
      {required final List<AIGCExportHistoryItem> consumptions,
      required this.total,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize})
      : _consumptions = consumptions;

  factory _$AIGCExportHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIGCExportHistoryModelImplFromJson(json);

  final List<AIGCExportHistoryItem> _consumptions;
  @override
  List<AIGCExportHistoryItem> get consumptions {
    if (_consumptions is EqualUnmodifiableListView) return _consumptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consumptions);
  }

  @override
  final int total;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AIGCExportHistoryModel(consumptions: $consumptions, total: $total, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIGCExportHistoryModelImpl &&
            const DeepCollectionEquality()
                .equals(other._consumptions, _consumptions) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_consumptions),
      total,
      page,
      pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AIGCExportHistoryModelImplCopyWith<_$AIGCExportHistoryModelImpl>
      get copyWith => __$$AIGCExportHistoryModelImplCopyWithImpl<
          _$AIGCExportHistoryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIGCExportHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _AIGCExportHistoryModel implements AIGCExportHistoryModel {
  const factory _AIGCExportHistoryModel(
          {required final List<AIGCExportHistoryItem> consumptions,
          required final int total,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AIGCExportHistoryModelImpl;

  factory _AIGCExportHistoryModel.fromJson(Map<String, dynamic> json) =
      _$AIGCExportHistoryModelImpl.fromJson;

  @override
  List<AIGCExportHistoryItem> get consumptions;
  @override
  int get total;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AIGCExportHistoryModelImplCopyWith<_$AIGCExportHistoryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AIGCExportHistoryItem _$AIGCExportHistoryItemFromJson(
    Map<String, dynamic> json) {
  return _AIGCExportHistoryItem.fromJson(json);
}

/// @nodoc
mixin _$AIGCExportHistoryItem {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'account_role')
  String get accountRole => throw _privateConstructorUsedError;
  String get mobile => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  @JsonKey(name: 'photo_name')
  String get photoName => throw _privateConstructorUsedError;
  String? get hostname => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_id')
  String get deviceId => throw _privateConstructorUsedError;
  @JsonKey(name: 'cost_type')
  String get costType => throw _privateConstructorUsedError;
  @JsonKey(name: 'cost_value')
  int get costValue => throw _privateConstructorUsedError;
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AIGCExportHistoryItemCopyWith<AIGCExportHistoryItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIGCExportHistoryItemCopyWith<$Res> {
  factory $AIGCExportHistoryItemCopyWith(AIGCExportHistoryItem value,
          $Res Function(AIGCExportHistoryItem) then) =
      _$AIGCExportHistoryItemCopyWithImpl<$Res, AIGCExportHistoryItem>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'account_role') String accountRole,
      String mobile,
      String? nickname,
      @JsonKey(name: 'photo_name') String photoName,
      String? hostname,
      @JsonKey(name: 'device_id') String deviceId,
      @JsonKey(name: 'cost_type') String costType,
      @JsonKey(name: 'cost_value') int costValue,
      @JsonKey(name: 'create_at') int createAt});
}

/// @nodoc
class _$AIGCExportHistoryItemCopyWithImpl<$Res,
        $Val extends AIGCExportHistoryItem>
    implements $AIGCExportHistoryItemCopyWith<$Res> {
  _$AIGCExportHistoryItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountRole = null,
    Object? mobile = null,
    Object? nickname = freezed,
    Object? photoName = null,
    Object? hostname = freezed,
    Object? deviceId = null,
    Object? costType = null,
    Object? costValue = null,
    Object? createAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountRole: null == accountRole
          ? _value.accountRole
          : accountRole // ignore: cast_nullable_to_non_nullable
              as String,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      photoName: null == photoName
          ? _value.photoName
          : photoName // ignore: cast_nullable_to_non_nullable
              as String,
      hostname: freezed == hostname
          ? _value.hostname
          : hostname // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      costType: null == costType
          ? _value.costType
          : costType // ignore: cast_nullable_to_non_nullable
              as String,
      costValue: null == costValue
          ? _value.costValue
          : costValue // ignore: cast_nullable_to_non_nullable
              as int,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIGCExportHistoryItemImplCopyWith<$Res>
    implements $AIGCExportHistoryItemCopyWith<$Res> {
  factory _$$AIGCExportHistoryItemImplCopyWith(
          _$AIGCExportHistoryItemImpl value,
          $Res Function(_$AIGCExportHistoryItemImpl) then) =
      __$$AIGCExportHistoryItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'account_role') String accountRole,
      String mobile,
      String? nickname,
      @JsonKey(name: 'photo_name') String photoName,
      String? hostname,
      @JsonKey(name: 'device_id') String deviceId,
      @JsonKey(name: 'cost_type') String costType,
      @JsonKey(name: 'cost_value') int costValue,
      @JsonKey(name: 'create_at') int createAt});
}

/// @nodoc
class __$$AIGCExportHistoryItemImplCopyWithImpl<$Res>
    extends _$AIGCExportHistoryItemCopyWithImpl<$Res,
        _$AIGCExportHistoryItemImpl>
    implements _$$AIGCExportHistoryItemImplCopyWith<$Res> {
  __$$AIGCExportHistoryItemImplCopyWithImpl(_$AIGCExportHistoryItemImpl _value,
      $Res Function(_$AIGCExportHistoryItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountRole = null,
    Object? mobile = null,
    Object? nickname = freezed,
    Object? photoName = null,
    Object? hostname = freezed,
    Object? deviceId = null,
    Object? costType = null,
    Object? costValue = null,
    Object? createAt = null,
  }) {
    return _then(_$AIGCExportHistoryItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountRole: null == accountRole
          ? _value.accountRole
          : accountRole // ignore: cast_nullable_to_non_nullable
              as String,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      photoName: null == photoName
          ? _value.photoName
          : photoName // ignore: cast_nullable_to_non_nullable
              as String,
      hostname: freezed == hostname
          ? _value.hostname
          : hostname // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      costType: null == costType
          ? _value.costType
          : costType // ignore: cast_nullable_to_non_nullable
              as String,
      costValue: null == costValue
          ? _value.costValue
          : costValue // ignore: cast_nullable_to_non_nullable
              as int,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIGCExportHistoryItemImpl implements _AIGCExportHistoryItem {
  const _$AIGCExportHistoryItemImpl(
      {required this.id,
      @JsonKey(name: 'account_role') required this.accountRole,
      required this.mobile,
      required this.nickname,
      @JsonKey(name: 'photo_name') required this.photoName,
      required this.hostname,
      @JsonKey(name: 'device_id') required this.deviceId,
      @JsonKey(name: 'cost_type') required this.costType,
      @JsonKey(name: 'cost_value') required this.costValue,
      @JsonKey(name: 'create_at') required this.createAt});

  factory _$AIGCExportHistoryItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIGCExportHistoryItemImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'account_role')
  final String accountRole;
  @override
  final String mobile;
  @override
  final String? nickname;
  @override
  @JsonKey(name: 'photo_name')
  final String photoName;
  @override
  final String? hostname;
  @override
  @JsonKey(name: 'device_id')
  final String deviceId;
  @override
  @JsonKey(name: 'cost_type')
  final String costType;
  @override
  @JsonKey(name: 'cost_value')
  final int costValue;
  @override
  @JsonKey(name: 'create_at')
  final int createAt;

  @override
  String toString() {
    return 'AIGCExportHistoryItem(id: $id, accountRole: $accountRole, mobile: $mobile, nickname: $nickname, photoName: $photoName, hostname: $hostname, deviceId: $deviceId, costType: $costType, costValue: $costValue, createAt: $createAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIGCExportHistoryItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountRole, accountRole) ||
                other.accountRole == accountRole) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.photoName, photoName) ||
                other.photoName == photoName) &&
            (identical(other.hostname, hostname) ||
                other.hostname == hostname) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.costType, costType) ||
                other.costType == costType) &&
            (identical(other.costValue, costValue) ||
                other.costValue == costValue) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, accountRole, mobile,
      nickname, photoName, hostname, deviceId, costType, costValue, createAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AIGCExportHistoryItemImplCopyWith<_$AIGCExportHistoryItemImpl>
      get copyWith => __$$AIGCExportHistoryItemImplCopyWithImpl<
          _$AIGCExportHistoryItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIGCExportHistoryItemImplToJson(
      this,
    );
  }
}

abstract class _AIGCExportHistoryItem implements AIGCExportHistoryItem {
  const factory _AIGCExportHistoryItem(
          {required final String id,
          @JsonKey(name: 'account_role') required final String accountRole,
          required final String mobile,
          required final String? nickname,
          @JsonKey(name: 'photo_name') required final String photoName,
          required final String? hostname,
          @JsonKey(name: 'device_id') required final String deviceId,
          @JsonKey(name: 'cost_type') required final String costType,
          @JsonKey(name: 'cost_value') required final int costValue,
          @JsonKey(name: 'create_at') required final int createAt}) =
      _$AIGCExportHistoryItemImpl;

  factory _AIGCExportHistoryItem.fromJson(Map<String, dynamic> json) =
      _$AIGCExportHistoryItemImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'account_role')
  String get accountRole;
  @override
  String get mobile;
  @override
  String? get nickname;
  @override
  @JsonKey(name: 'photo_name')
  String get photoName;
  @override
  String? get hostname;
  @override
  @JsonKey(name: 'device_id')
  String get deviceId;
  @override
  @JsonKey(name: 'cost_type')
  String get costType;
  @override
  @JsonKey(name: 'cost_value')
  int get costValue;
  @override
  @JsonKey(name: 'create_at')
  int get createAt;
  @override
  @JsonKey(ignore: true)
  _$$AIGCExportHistoryItemImplCopyWith<_$AIGCExportHistoryItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}
