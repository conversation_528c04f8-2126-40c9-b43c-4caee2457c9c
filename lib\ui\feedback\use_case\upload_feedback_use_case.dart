import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/core/manager/device_rating_manager.dart';
import 'package:turing_art/core/service/disk_info_service/disk_info.dart';
import 'package:turing_art/datalayer/domain/models/feedback/feedback_client_info.dart';
import 'package:turing_art/datalayer/domain/models/feedback/feedback_content.dart';
import 'package:turing_art/datalayer/service/feedback/user_feedback_service.dart';
import 'package:turing_art/utils/device_info_util.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/platform.dart';

class UploadFeedbackUseCase {
  final UserFeedbackService service;

  UploadFeedbackUseCase(this.service);

  /// 处理用户反馈文件上传
  /// [category] 反馈类别
  /// [content] 反馈内容
  /// [filepath] 附件文件路径，可为空
  Future<void> invoke(String category, String content, String filepath) async {
    try {
      List<String>? attachments;
      if (filepath.isNotEmpty && File(filepath).existsSync()) {
        PGLog.d("开始获取凭证");
        var certify =
            await service.requestUploadCertify(path.basename(filepath));
        PGLog.d("凭证获取成功，开始上传...");
        await service.uploadFile(certify.uploadCertify, filepath);
        PGLog.d("上传成功");
        attachments = [
          _buildAttachments(certify.uploadCertify, certify.scheme)
        ];
      }
      await service.postFeedback(FeedbackContent(
          category: category,
          content: content,
          attachments: attachments,
          clientInfo: await _buildClientInfo()));
      PGDialog.showToastOnUnity("反馈提交成功");
    } catch (e) {
      PGLog.e(e.toString());
      PGDialog.showToastOnUnity("反馈提交失败 请检查网络连接");
    }
  }

  _buildAttachments(String uploadCertify, String scheme) {
    final uploadCertifyJson = json.decode(uploadCertify);
    final policy = utf8.decode(base64Decode(uploadCertifyJson["policy"]));
    final policyJson = json.decode(policy);
    final conditions = policyJson["conditions"];
    var bucketName = "";
    for (var item in conditions) {
      if (item is Map && item.containsKey("bucket")) {
        bucketName = item["bucket"];
        break;
      }
    }
    return Uri(scheme: scheme, host: bucketName, path: uploadCertifyJson["key"])
        .toString();
  }

  _buildClientInfo() async {
    final appDir = await FileManager().appDir;
    final spaceInfo = await DiskInfo.getDiskSpace(appDir.path);
    return FeedbackClientInfo(
      appVersion: DeviceInfoUtil().appVersion,
      // systemVersion: DeviceInfoUtil().osVersion,
      cpuInfo:
          "processorType:${DeviceRatingManager.deviceInfo?.processorType}\n"
          "processorFrequency:${DeviceRatingManager.deviceInfo?.processorFrequency}\n"
          "processorCount:${DeviceRatingManager.deviceInfo?.processorCount}",
      memoryInfo: "${DeviceRatingManager.deviceInfo?.systemMemorySize}",
      diskInfo: "totalSpace:${spaceInfo?.formattedTotalSpace}\n"
          "freeSpace:${spaceInfo?.formattedFreeSpace}\n"
          "usedSpace:${spaceInfo?.formattedUsedSpace}",
      gpuInfo:
          "graphicsDeviceName:${DeviceRatingManager.deviceInfo?.graphicsDeviceName}\n"
          "graphicsDeviceType:${DeviceRatingManager.deviceInfo?.graphicsDeviceType}\n"
          "graphicsDeviceVendor:${DeviceRatingManager.deviceInfo?.graphicsDeviceVendor}\n"
          "graphicsDeviceVersion:${DeviceRatingManager.deviceInfo?.graphicsDeviceVersion}\n"
          "graphicsMemorySize:${DeviceRatingManager.deviceInfo?.graphicsMemorySize}",
      networkInfo: DeviceInfoUtil().network,
      screenInfo: DeviceInfoUtil().screenSize,
      platform: await _getSystemType(),
    );
  }

  /// 获取系统类型
  Future<String> _getSystemType() async {
    if (Platform.isMacOS) {
      return 'mac';
    } else if (Platform.isWindows) {
      // 使用更准确的 Windows 版本检测
      return await getWindowsVersionName();
    } else if (Platform.isLinux) {
      return 'linux';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else {
      return 'other';
    }
  }
}
