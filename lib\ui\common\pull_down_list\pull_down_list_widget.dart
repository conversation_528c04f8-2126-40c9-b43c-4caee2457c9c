import 'package:flutter/material.dart';

class PullDownListWidget extends StatefulWidget {
  final double width;
  final double height;
  final double? listHeight;
  final List<String> items;
  final String? selectedItem;
  final Color collapsedTextColor;
  final double collapsedTextSize;
  final Color listTextColor;
  final double listTextSize;
  final Color listTextHoverColor;
  final Function(String item, int index)? onItemSelected;

  const PullDownListWidget({
    super.key,
    required this.width,
    required this.height,
    required this.items,
    this.listHeight,
    this.selectedItem,
    this.collapsedTextColor = Colors.white,
    this.collapsedTextSize = 12,
    this.listTextColor = const Color(0xFFEBEDF5),
    this.listTextSize = 12,
    this.listTextHoverColor = Colors.white,
    this.onItemSelected,
  });

  @override
  State<PullDownListWidget> createState() => _PullDownListWidgetState();
}

class _PullDownListWidgetState extends State<PullDownListWidget>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  String? _currentSelectedItem;
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  OverlayEntry? _overlayEntry;
  int? _hoveredIndex;

  @override
  void initState() {
    super.initState();
    _currentSelectedItem = widget.selectedItem ?? widget.items.first;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 3.14159,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _removeOverlay();
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
        _showOverlay();
      } else {
        _animationController.reverse();
        _removeOverlay();
      }
    });
  }

  void _showOverlay() {
    _removeOverlay(); // 确保没有重复的 overlay

    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 透明背景，用于捕获外部点击
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isExpanded = false;
                  _animationController.reverse();
                });
                _removeOverlay();
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // 下拉列表
          Positioned(
            left: position.dx,
            top: position.dy + widget.height + 4, // 添加4px间距
            child: Material(
              color: Colors.transparent,
              child: StatefulBuilder(
                builder: (context, setState) =>
                    _buildExpandedListWithState(setState),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _selectItem(String item, int index) {
    setState(() {
      _currentSelectedItem = item;
      _isExpanded = false;
      _animationController.reverse();
    });
    _removeOverlay();
    widget.onItemSelected?.call(item, index);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: _buildCollapsedContainer(),
    );
  }

  Widget _buildCollapsedContainer() {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: Colors.white.withOpacity(0.06),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          height: widget.height,
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧文本
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 12, right: 4),
                  child: Text(
                    _currentSelectedItem ?? '',
                    style: TextStyle(
                      color: widget.collapsedTextColor,
                      fontSize: widget.collapsedTextSize,
                      fontWeight: FontWeight.w400,
                      height: 12 / 14, // 设置为1.0确保文本垂直居中
                    ),
                  ),
                ),
              ),
              // 右侧箭头图标
              Padding(
                padding: const EdgeInsets.only(right: 12),
                child: AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value,
                      child: Image.asset(
                        'assets/icons/icon_arrows_down.png',
                        width: 16,
                        height: 16,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedListWithState(StateSetter overlaySetState) {
    // 计算实际需要的列表高度
    final actualListHeight = _calculateActualListHeight();

    return Container(
      width: widget.width,
      height: actualListHeight,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: widget.items.length,
        itemBuilder: (context, index) {
          final item = widget.items[index];
          final isSelected = item == _currentSelectedItem;
          return _buildListItemWithState(
              item, index, isSelected, overlaySetState);
        },
      ),
    );
  }

  // 计算实际需要的列表高度
  double _calculateActualListHeight() {
    final itemCount = widget.items.length;
    final totalItemsHeight = itemCount * widget.height + itemCount * 8;

    // 如果没有设置最大高度，使用实际高度
    if (widget.listHeight == null) {
      return totalItemsHeight;
    }

    // 如果实际高度小于最大高度，使用实际高度；否则使用最大高度
    return totalItemsHeight <= widget.listHeight!
        ? totalItemsHeight
        : widget.listHeight!;
  }

  Widget _buildListItemWithState(
      String item, int index, bool isSelected, StateSetter overlaySetState) {
    final isHovered = _hoveredIndex == index;

    return MouseRegion(
      onEnter: (_) => overlaySetState(() => _hoveredIndex = index),
      onExit: (_) => overlaySetState(() => _hoveredIndex = null),
      child: GestureDetector(
        onTap: () => _selectItem(item, index),
        child: Container(
          width: widget.width,
          height: widget.height + 8,
          alignment: Alignment.center,
          child: Stack(
            children: [
              // 悬停背景视图
              if (isHovered)
                Positioned.fill(
                  child: Container(
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              // 内容行
              Container(
                height: widget.height + 8,
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 左侧文本
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 12, right: 12),
                        child: Text(
                          item,
                          style: TextStyle(
                            color: isHovered
                                ? widget.listTextHoverColor
                                : widget.listTextColor.withOpacity(0.65),
                            fontSize: widget.listTextSize,
                            fontWeight: FontWeight.w400,
                            height: 1.0, // 设置为1.0确保文本垂直居中
                          ),
                        ),
                      ),
                    ),
                    // 右侧选中图标
                    if (isSelected)
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: Image.asset(
                          'assets/icons/icon_list_select.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
