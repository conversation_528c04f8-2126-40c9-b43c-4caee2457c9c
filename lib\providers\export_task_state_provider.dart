import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/utils/pg_log.dart';

enum ExportOperationState {
  add(0),
  update(1),
  delete(2);

  const ExportOperationState(this.value);
  final int value;

  static ExportOperationState? fromValue(int value) {
    for (final state in ExportOperationState.values) {
      if (state.value == value) {
        return state;
      }
    }
    return null;
  }
}

/// 导出任务状态事件
class ExportTaskStateEvent {
  final List<String> taskIds;
  final ExportOperationState operationState;

  const ExportTaskStateEvent({
    required this.taskIds,
    required this.operationState,
  });
}

/// 导出任务状态提供者 - 无状态分发模式
class ExportTaskStateProvider extends ChangeNotifier {
  // 事件流控制器，用于分发事件
  final StreamController<ExportTaskStateEvent> _eventController =
      StreamController<ExportTaskStateEvent>.broadcast();

  /// 导出任务状态事件流
  Stream<ExportTaskStateEvent> get eventStream => _eventController.stream;

  /// 处理导出任务状态更新
  /// 解析JSON数据并分发事件，不持有任何状态
  Future<void> updateExportTaskState(String jsonString) async {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // 解析taskIds - 支持单个字符串或字符串数组
      List<String> taskIds = [];
      final taskIdData = data['tasks'] as List<dynamic>?;
      taskIds = taskIdData?.cast<String>() ?? [];

      // 解析operationState - 支持数字或字符串
      ExportOperationState? operationState;
      final operationStateData = data['state'];
      if (operationStateData != null) {
        final intValue = operationStateData is int
            ? operationStateData
            : int.tryParse(operationStateData.toString());
        operationState =
            intValue != null ? ExportOperationState.fromValue(intValue) : null;
      }

      if (operationState != null) {
        final event = ExportTaskStateEvent(
          taskIds: taskIds,
          operationState: operationState,
        );

        // 分发事件
        _eventController.add(event);
        PGLog.d(
            'ExportTaskStateProvider: 分发导出任务状态事件 - taskIds: $taskIds, operationState: ${operationState.name}');
      } else {
        PGLog.w(
            'ExportTaskStateProvider: 无效的数据格式 - taskIds: $taskIds, operationState: $operationState');
      }
    } catch (e) {
      PGLog.e('ExportTaskStateProvider: 更新导出记录失败: $e');
    }
  }

  @override
  void dispose() {
    _eventController.close();
    super.dispose();
  }
}
