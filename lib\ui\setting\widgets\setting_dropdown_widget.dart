import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_choice_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class SettingDropdownWidget extends StatefulWidget {
  final List<SettingChoiceModel> choices;
  final int selectedIndex;
  final ValueChanged<int> onSelectionChanged;
  final double width;
  final double height;

  const SettingDropdownWidget({
    super.key,
    required this.choices,
    required this.selectedIndex,
    required this.onSelectionChanged,
    this.width = 302,
    this.height = 32,
  });

  @override
  State<SettingDropdownWidget> createState() => _SettingDropdownWidgetState();
}

class _SettingDropdownWidgetState extends State<SettingDropdownWidget> {
  final ValueNotifier<bool> _isDropdownOpenNotifier = ValueNotifier(false);
  late final ValueNotifier<int> _selectedIndexNotifier;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _selectedIndexNotifier = ValueNotifier(widget.selectedIndex);
  }

  @override
  void didUpdateWidget(SettingDropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedIndex != widget.selectedIndex) {
      _selectedIndexNotifier.value = widget.selectedIndex;
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _isDropdownOpenNotifier.dispose();
    _selectedIndexNotifier.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isDropdownOpenNotifier.value) {
      _closeDropdown();
    } else {
      _showDropdown();
    }
  }

  void _showDropdown() {
    _isDropdownOpenNotifier.value = true;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _closeDropdown() {
    if (_isDropdownOpenNotifier.value) {
      _isDropdownOpenNotifier.value = false;
      _removeOverlay();
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _selectOption(int index) {
    _selectedIndexNotifier.value = index;
    _closeDropdown();
    widget.onSelectionChanged(index);
  }

  String _getChoiceDisplayText(SettingChoiceModel choice) {
    if (choice.isRecommended) {
      return '${choice.title}（默认）';
    }
    return choice.title;
  }

  String _getCurrentDisplayText(int selectedIndex) {
    if (selectedIndex >= 0 && selectedIndex < widget.choices.length) {
      return _getChoiceDisplayText(widget.choices[selectedIndex]);
    }
    return '';
  }

  OverlayEntry _createOverlayEntry() {
    // 计算下拉列表高度，每个选项40px(暂时不限制)+ 上下各2px
    final dropdownHeight = (widget.choices.length * 40.0) + 4;

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 全屏透明遮罩，点击关闭下拉列表
          Positioned.fill(
            child: GestureDetector(
              onTap: _closeDropdown,
              behavior: HitTestBehavior.translucent,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // 下拉列表内容
          Positioned(
            width: widget.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, widget.height + 4),
              child: Material(
                color: Colors.transparent,
                child: GestureDetector(
                  onTap: () {}, // 阻止事件冒泡，防止点击下拉列表时触发外层的关闭
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    width: widget.width,
                    height: dropdownHeight,
                    decoration: BoxDecoration(
                      color: const Color(0xFF2B2B2B),
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: widget.choices.length,
                      itemBuilder: (context, index) {
                        return ValueListenableBuilder<int>(
                          valueListenable: _selectedIndexNotifier,
                          builder: (context, selectedIndex, child) {
                            final bool isSelected = index == selectedIndex;
                            return GestureDetector(
                              onTap: () => _selectOption(index),
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 2),
                                height: 36,
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? const Color(0x0DFFFFFF) // 白色透明度为5%
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Row(
                                  children: [
                                    const SizedBox(width: 14),
                                    if (isSelected)
                                      Image.asset(
                                        "assets/icons/setting_list_select.png",
                                        width: 16,
                                        height: 16,
                                      ),
                                    SizedBox(width: isSelected ? 0 : 16),
                                    Expanded(
                                      child: Text(
                                        _getChoiceDisplayText(
                                            widget.choices[index]),
                                        style: TextStyle(
                                          fontFamily: Fonts.defaultFontFamily,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: isSelected
                                              ? const Color(0xFFF72561)
                                              : Colors.white.withOpacity(0.85),
                                          height: 18 / 14,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleDropdown,
        behavior: HitTestBehavior.opaque,
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: const Color(0xFF2B2B2B),
            borderRadius: BorderRadius.circular(5),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: ValueListenableBuilder<int>(
                    valueListenable: _selectedIndexNotifier,
                    builder: (context, selectedIndex, child) {
                      return Text(
                        _getCurrentDisplayText(selectedIndex),
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withOpacity(0.85),
                          height: 18 / 14,
                        ),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.left,
                      );
                    },
                  ),
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: _isDropdownOpenNotifier,
                  builder: (context, isDropdownOpen, child) {
                    return Transform.rotate(
                      angle: isDropdownOpen
                          ? 1.5708 // 展开时朝上
                          : -1.5708, // 未展开时朝下
                      child: Image.asset(
                        "assets/icons/home_pc_top_back.png", // 这个图标默认朝左
                        width: 16,
                        height: 16,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
