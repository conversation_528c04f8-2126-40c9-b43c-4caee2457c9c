import 'package:flutter/foundation.dart';
import 'package:turing_art/ffi/ffi.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘信息后台加载器
/// 使用compute在独立的Isolate中执行磁盘信息获取，避免阻塞UI线程
class DiskInfoBackgroundLoader {
  /// 在后台获取所有磁盘信息
  /// 使用compute在独立的Isolate中执行
  static Future<List<DiskInfoEntry>?> loadAllDisksInfo() async {
    try {
      PGLog.d('DiskInfoBackgroundLoader: 开始后台加载磁盘信息...');

      final diskInfos = await compute(_getAllDisksInfoInIsolate, null);

      if (diskInfos != null && diskInfos.isNotEmpty) {
        PGLog.i('DiskInfoBackgroundLoader: 后台加载完成，获取到${diskInfos.length}个磁盘信息');
      } else {
        PGLog.w('DiskInfoBackgroundLoader: 后台加载完成，但未获取到磁盘信息');
      }

      return diskInfos;
    } catch (e) {
      PGLog.e('DiskInfoBackgroundLoader: 后台加载失败: $e');
      return null;
    }
  }

  /// 在后台获取指定路径的磁盘类型
  /// 使用compute在独立的Isolate中执行
  static Future<DiskType?> loadDiskTypeForPath(String path) async {
    try {
      PGLog.d('DiskInfoBackgroundLoader: 开始后台加载路径磁盘类型: $path');

      final diskType = await compute(_getDiskTypeForPathInIsolate, path);

      if (diskType != null) {
        PGLog.d('DiskInfoBackgroundLoader: 后台加载完成: $path -> $diskType');
      } else {
        PGLog.w('DiskInfoBackgroundLoader: 后台加载失败: $path');
      }

      return diskType;
    } catch (e) {
      PGLog.e('DiskInfoBackgroundLoader: 后台加载失败: $e');
      return null;
    }
  }

  /// 检查是否应该使用后台加载
  /// 在某些情况下（如测试环境）可能需要禁用compute
  static bool shouldUseBackgroundLoading() {
    // 在测试环境中，kDebugMode可能不可靠，可以根据需要调整这个逻辑
    try {
      return !kIsWeb; // Web环境不支持Isolate
    } catch (e) {
      // 如果出现异常，保守地返回false
      return false;
    }
  }
}

/// Isolate中执行的磁盘信息获取函数
/// 这个函数会在独立的Isolate中运行，避免阻塞UI线程
List<DiskInfoEntry>? _getAllDisksInfoInIsolate(void _) {
  try {
    // 在Isolate中初始化DiskInfoBindings
    if (!DiskInfoBindings.initialize()) {
      return null;
    }

    // 获取所有磁盘信息
    final diskInfos = DiskInfoBindings.getAllDisksInfo();
    return diskInfos;
  } catch (e) {
    // 在Isolate中无法使用PGLog，所以直接返回null
    return null;
  }
}

/// Isolate中执行的单个路径磁盘类型获取函数
DiskType? _getDiskTypeForPathInIsolate(String path) {
  try {
    // 在Isolate中初始化DiskInfoBindings
    if (!DiskInfoBindings.initialize()) {
      return null;
    }

    // 获取指定路径的磁盘类型
    final diskType = DiskInfoBindings.getDiskTypeFromPath(path);
    return diskType;
  } catch (e) {
    // 在Isolate中无法使用PGLog，所以直接返回null
    return null;
  }
}
