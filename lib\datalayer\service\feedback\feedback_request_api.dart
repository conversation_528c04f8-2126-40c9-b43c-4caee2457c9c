import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';
import 'package:turing_art/datalayer/domain/models/feedback/feedback_result.dart';
import 'package:turing_art/datalayer/domain/models/feedback/upload_file_certify.dart';

part 'feedback_request_api.g.dart';

@RestApi()
abstract class FeedbackRequestApi {
  factory FeedbackRequestApi(Dio dio) = _FeedbackRequestApi;

  @GET("/v1/feedback/upload-certify")
  Future<UploadFileCertify> createFeedbackUpload(
      {@Query("filename") required String filename});

  @POST("/v1/feedback")
  Future<FeedbackResult> postFeedback(
      {@Body() required Map<String, dynamic> content});
}
