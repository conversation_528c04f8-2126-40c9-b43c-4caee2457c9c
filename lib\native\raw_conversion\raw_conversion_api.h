#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// 导出宏定义
#ifdef RAW_CONVERSION_EXPORTS
    #ifdef _WIN32
        #define RAW_CONVERSION_API __declspec(dllexport)
    #else
        #define RAW_CONVERSION_API __attribute__((visibility("default")))
    #endif
#else
    #ifdef _WIN32
        #define RAW_CONVERSION_API __declspec(dllimport)
    #else
        #define RAW_CONVERSION_API
    #endif
#endif

// 错误码定义
typedef enum {
    RAW_CONVERSION_SUCCESS = 0,
    RAW_CONVERSION_ERROR_INVALID_PARAMS = -1,
    RAW_CONVERSION_ERROR_INIT_FAILED = -2,
    RAW_CONVERSION_ERROR_PROCESSING_FAILED = -3,
    RAW_CONVERSION_ERROR_UNSUPPORTED_FORMAT = -4,
    RAW_CONVERSION_ERROR_FILE_NOT_FOUND = -5,
    RAW_CONVERSION_ERROR_OUT_OF_MEMORY = -6,
    RAW_CONVERSION_ERROR_UNKNOWN = -999
} RAW_CONVERSION_RESULT;

// 调色模式枚举
typedef enum {
    RAW_CONVERSION_ADJUST_AUTO = 0,
    RAW_CONVERSION_ADJUST_NORMAL = 1,
    RAW_CONVERSION_ADJUST_STYLE = 2
} RAW_CONVERSION_ADJUST_TYPE;

// 配置结构体
typedef struct {
    int enable_raw_lut;      // 是否启用RAW LUT
    int enable_denoise;      // 是否启用去噪
    int enable_std_lut;      // 是否启用标准LUT
    RAW_CONVERSION_ADJUST_TYPE adjust_type;  // 调色模式
    int threads;             // 线程数 (-1为自动)
    float strength;          // 效果强度 (0.0-1.0)，0.0为原图，1.0为完全处理效果
} RAW_CONVERSION_CONFIG;

// 句柄类型
typedef void* RAW_CONVERSION_HANDLE;

/**
 * @brief 获取版本信息
 * @return 版本字符串
 */
RAW_CONVERSION_API const char* raw_conversion_get_version(void);

/**
 * @brief 创建RAW转换句柄
 * @return 句柄指针，失败返回NULL
 */
RAW_CONVERSION_API RAW_CONVERSION_HANDLE raw_conversion_create(void);

/**
 * @brief 初始化RAW转换器
 * @param handle 句柄
 * @param key 授权密钥
 * @param user_code 用户代码
 * @param prod_code 产品代码
 * @return 错误码
 */
RAW_CONVERSION_API RAW_CONVERSION_RESULT raw_conversion_init(
    RAW_CONVERSION_HANDLE handle,
    const char* key,
    const char* user_code,
    const char* prod_code
);

/**
 * @brief 处理图像
 * @param handle 句柄
 * @param config 配置参数
 * @param input_data 输入图像数据
 * @param input_width 输入图像宽度
 * @param input_height 输入图像高度
 * @param input_channels 输入图像通道数
 * @param output_data 输出图像数据（需要预分配）
 * @param adjust_mode 返回的调色模式
 * @param adjust_custom 是否使用自定义调色
 * @return 错误码
 */
RAW_CONVERSION_API RAW_CONVERSION_RESULT raw_conversion_process(
    RAW_CONVERSION_HANDLE handle,
    const RAW_CONVERSION_CONFIG* config,
    const unsigned char* input_data,
    int input_width,
    int input_height,
    int input_channels,
    unsigned char* output_data,
    int* adjust_mode,
    int adjust_custom
);

/**
 * @brief 处理图像文件
 * @param handle 句柄
 * @param config 配置参数
 * @param input_path 输入文件路径
 * @param output_path 输出文件路径
 * @param adjust_mode 返回的调色模式
 * @param adjust_custom 是否使用自定义调色
 * @return 错误码
 */
RAW_CONVERSION_API RAW_CONVERSION_RESULT raw_conversion_process_file(
    RAW_CONVERSION_HANDLE handle,
    const RAW_CONVERSION_CONFIG* config,
    const char* input_path,
    const char* output_path,
    int* adjust_mode,
    int adjust_custom
);

/**
 * @brief 清理资源
 * @param handle 句柄
 * @return 错误码
 */
RAW_CONVERSION_API RAW_CONVERSION_RESULT raw_conversion_clear(RAW_CONVERSION_HANDLE handle);

// /**
//  * @brief 销毁句柄
//  * @param handle 句柄
//  */
// RAW_CONVERSION_API void raw_conversion_destroy(RAW_CONVERSION_HANDLE handle);

/**
 * @brief 获取错误信息
 * @param result 错误码
 * @return 错误信息字符串
 */
RAW_CONVERSION_API const char* raw_conversion_get_error_string(RAW_CONVERSION_RESULT result);

/**
 * @brief 检查文件格式是否支持
 * @param file_path 文件路径
 * @return 1支持，0不支持
 */
RAW_CONVERSION_API int raw_conversion_is_supported_format(const char* file_path);

#ifdef __cplusplus
}
#endif 