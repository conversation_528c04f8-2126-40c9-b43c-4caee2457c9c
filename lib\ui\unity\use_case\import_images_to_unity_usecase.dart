import 'dart:convert';
import 'dart:io';

import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 导入图片到Unity的UseCase
///
/// 负责从文件列表中提取图片文件，并创建Unity消息
class ImportImagesToUnityUseCase {
  ImportImagesToUnityUseCase();

  /// 处理文件列表，生成导入图片到Unity的消息
  ///
  /// [DealImageFilesResult] 要处理的文件列表
  /// [projectId] 项目ID
  ///
  /// 返回MessageToUnity消息，若无有效图片则返回null
  Future<MessageToUnity?> invoke(
      DealImageFilesResult fileResult, String projectId) async {
    try {
      PGLog.d('ImportImagesToUnityUseCase: 接收到 ${fileResult.fileCount()} 个文件');
      final nowTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d('ImportImagesToUnityUseCase: 开始提取图片文件, 时间: $nowTime');
      // 提取图片文件
      final List<File> imageFiles =
          await _extractImageFiles(fileResult.validFiles, true);
      final endTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d('ImportImagesToUnityUseCase: 提取图片文件时间: ${endTime - nowTime}ms');

      if (imageFiles.isEmpty) {
        PGLog.d('ImportImagesToUnityUseCase: 未找到有效的图片文件');
        return null;
      }

      PGLog.d('ImportImagesToUnityUseCase: 提取了 ${imageFiles.length} 个图片文件');

      // 将所有图片路径整合成一个列表
      final List<String> imagePaths =
          imageFiles.map((file) => file.path).toList();

      // 创建发送给Unity的消息
      final message = _createImportImageMessage(
          imagePaths, projectId, fileResult.projectName);
      final endTime1 = DateTime.now().millisecondsSinceEpoch;
      PGLog.d(
          'ImportImagesToUnityUseCase: 组装好发送unity消息耗时: ${endTime1 - endTime}ms');

      return message;
    } catch (e) {
      PGLog.d('ImportImagesToUnityUseCase: 处理图片文件出错: $e');
      return null;
    }
  }

  /// 从文件列表中提取图片文件
  ///
  /// [files] 要处理的文件列表
  /// [shouldRecurse] 是否递归处理文件夹
  Future<List<File>> _extractImageFiles(
      List<File> files, bool shouldRecurse) async {
    List<File> result = [];

    for (final file in files) {
      final FileSystemEntityType type = FileSystemEntity.typeSync(file.path);

      // notFound未找到的文件也要加入文件列表，可重链接
      if (type == FileSystemEntityType.file ||
          type == FileSystemEntityType.notFound) {
        // 如果是文件，判断是否为图片
        if (_isImageFile(file.path)) {
          result.add(file);
        }
      } else if (type == FileSystemEntityType.directory) {
        // 如果是文件夹，并且允许递归，则递归处理
        try {
          final Directory dir = Directory(file.path);
          final List<FileSystemEntity> entities =
              dir.listSync(recursive: shouldRecurse).toList();

          // 过滤出文件并检查是否为图片
          final dirImageFiles = entities
              .whereType<File>()
              .where((fileEntity) => _isImageFile(fileEntity.path))
              .map((fileEntity) => File(fileEntity.path))
              .toList();

          result.addAll(dirImageFiles);
        } catch (e) {
          PGLog.d('ImportImagesToUnityUseCase: 处理文件夹出错: $e');
        }
      }
    }

    return result;
  }

  /// 检查文件是否为图片
  bool _isImageFile(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    return [
      'jpg',
      'jpeg',
      'png',
      'bmp',
      'gif',
      'webp',
      'arw',
      'cr2',
      'cr3',
      'dng',
      'nef',
      'raf',
      'rw2',
      '3fr'
    ].contains(extension);
  }

  /// 创建导入图片的Unity消息
  MessageToUnity _createImportImageMessage(
      List<String> imagePaths, String projectId, String name) {
    final jsonParams = jsonEncode({
      'paths': imagePaths,
      'projectId': projectId,
      'dirName': name,
      'count': imagePaths.length,
    });

    return MessageToUnity(
      completed: const Uuid().v4(),
      method: UnityMessage.importImage.value,
      args: jsonParams,
    );
  }
}
