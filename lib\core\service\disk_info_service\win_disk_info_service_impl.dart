import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:turing_art/core/service/disk_info_service/disk_info_service.dart';
import 'package:turing_art/core/service/disk_info_service/internal/disk_info_background_loader.dart';
import 'package:turing_art/core/service/disk_info_service/internal/disk_info_cache.dart';
import 'package:turing_art/core/service/disk_info_service/internal/disk_info_native_client.dart';
import 'package:turing_art/core/service/disk_info_service/models/disk_info_models.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘信息服务的默认实现
class WinDiskInfoServiceImpl implements DiskInfoService {
  /// 单例实例
  static WinDiskInfoServiceImpl? _instance;
  static WinDiskInfoServiceImpl get instance =>
      _instance ??= WinDiskInfoServiceImpl._();

  WinDiskInfoServiceImpl._();

  /// 内部缓存
  final DiskInfoCache _cache = DiskInfoCache();

  /// 初始化状态
  bool _initialized = false;
  bool _initializing = false;

  /// 初始化完成的回调
  final List<VoidCallback> _initializationCallbacks = [];

  @override
  bool get isInitialized => _initialized;

  @override
  bool get isInitializing => _initializing;

  @override
  Future<bool> initialize() async {
    if (_initialized) {
      return true;
    }

    if (_initializing) {
      // 如果正在初始化，等待完成
      final completer = Completer<bool>();
      _initializationCallbacks.add(() {
        completer.complete(_initialized);
      });
      return completer.future;
    }

    _initializing = true;

    PGLog.i('DiskInfoService: 开始初始化磁盘信息服务...');

    try {
      // 先检查平台支持
      if (!DiskInfoNativeClient.isPlatformSupported) {
        PGLog.w('DiskInfoService: 当前平台不支持磁盘信息查询');
        _finalizeInitialization(false);
        return false;
      }

      // 初始化原生客户端
      if (!await DiskInfoNativeClient.initialize()) {
        PGLog.w('DiskInfoService: 原生客户端初始化失败');
        _finalizeInitialization(false);
        return false;
      }

      // 根据配置选择加载方式
      bool loadSuccess = await _loadDiskInfoInBackground();
      _finalizeInitialization(loadSuccess);
      return loadSuccess;
    } catch (e) {
      PGLog.e('DiskInfoService: 初始化失败: $e');
      _finalizeInitialization(false);
      return false;
    }
  }

  /// 在后台加载磁盘信息
  Future<bool> _loadDiskInfoInBackground() async {
    try {
      final diskInfos = await DiskInfoBackgroundLoader.loadAllDisksInfo();

      if (diskInfos != null && diskInfos.isNotEmpty) {
        // 更新缓存
        final entries = <String, DiskType>{};
        for (final diskInfo in diskInfos) {
          final driveRoot =
              DiskInfoNativeClient.extractDriveRoot(diskInfo.path);
          entries[driveRoot] = diskInfo.type;
        }
        _cache.setBatch(entries);

        PGLog.i('DiskInfoService: 后台加载成功，缓存了${entries.length}个磁盘信息');
        return true;
      } else {
        PGLog.w('DiskInfoService: 后台加载未获取到磁盘信息');
        return false;
      }
    } catch (e) {
      PGLog.e('DiskInfoService: 后台加载失败: $e');
      return false;
    }
  }

  /// 完成初始化流程
  void _finalizeInitialization(bool success) {
    _initialized = success;
    _initializing = false;

    // 通知等待初始化完成的回调
    for (final callback in _initializationCallbacks) {
      try {
        callback();
      } catch (e) {
        PGLog.e('DiskInfoService: 初始化回调执行失败: $e');
      }
    }
    _initializationCallbacks.clear();

    if (success) {
      PGLog.i('DiskInfoService: 初始化完成，共缓存${_cache.size}个磁盘信息');
    } else {
      _reportToSentry(
        drive: '',
        errorMessage: 'Initialize disk info service failed',
      );
      PGLog.w('DiskInfoService: 初始化失败');
    }
  }

  Future<DiskInfoResult?> getDiskInfo(String path) async {
    try {
      // 提取磁盘根路径
      final driveRoot = DiskInfoNativeClient.extractDriveRoot(path);

      final cached = _cache.get(driveRoot);
      if (cached == null) {
        if (_initializing) {
          _reportToSentry(
            drive: driveRoot,
            errorMessage:
                'Get disk info too early, DiskInfoService is initializing',
          );
        }
        return null;
      }

      return DiskInfoResult(
        path: path,
        driveRoot: driveRoot,
        type: cached.type,
        fromCache: true,
        queriedAt: DateTime.now(),
      );
    } catch (e) {
      PGLog.e('DiskInfoService: 获取磁盘信息失败: $e');
      return null;
    }
  }

  @override
  Future<DiskType?> getDiskType(String path) async {
    final result = await getDiskInfo(path);
    return result?.type;
  }

  @override
  Future<DiskSpaceInfo?> getDiskSpace(String path) async {
    try {
      // 使用DiskInfoNativeClient获取磁盘空间信息
      return await DiskInfoNativeClient.getDiskSpace(path);
    } catch (e) {
      PGLog.e('DiskInfoService: 获取磁盘空间失败: $e');
      return null;
    }
  }

  @override
  void reset() {
    _cache.clear();
    _initialized = false;
    _initializing = false;
    _initializationCallbacks.clear();
    PGLog.i('DiskInfoService: 服务已重置');
  }

  @override
  void dispose() {
    try {
      _cache.clear();
      _initializationCallbacks.clear();
      DiskInfoNativeClient.dispose();
      _initialized = false;
      _initializing = false;
      PGLog.i('DiskInfoService: 服务已释放');
    } catch (e) {
      PGLog.e('DiskInfoService: 释放服务失败: $e');
    }
  }

  /// 上报单个策略失败到Sentry
  void _reportToSentry({
    required String drive,
    required String errorMessage,
  }) {
    try {
      Sentry.captureMessage(
        'Get disk info failed',
        level: SentryLevel.warning,
        withScope: (scope) {
          scope.setContexts('error', {'message': errorMessage});
        },
      );
    } catch (e) {
      PGLog.e('Sentry上报失败: $e');
    }
  }
}

// 为DiskInfoCacheStats添加copyWith方法的扩展
extension DiskInfoCacheStatsExtension on DiskInfoCacheStats {
  DiskInfoCacheStats copyWith({
    bool? initialized,
    bool? initializing,
    int? cacheSize,
    Map<DiskType, int>? typeDistribution,
    List<String>? cachedDrives,
    DateTime? lastUpdated,
  }) {
    return DiskInfoCacheStats(
      initialized: initialized ?? this.initialized,
      initializing: initializing ?? this.initializing,
      cacheSize: cacheSize ?? this.cacheSize,
      typeDistribution: typeDistribution ?? this.typeDistribution,
      cachedDrives: cachedDrives ?? this.cachedDrives,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
