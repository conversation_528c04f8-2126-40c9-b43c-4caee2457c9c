import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_history_repository.dart';
import 'package:turing_art/ui/common/data_list/data_list_widget.dart';
import 'package:turing_art/ui/common/date_range/input_date/widget/input_date_range_widget.dart';
import 'package:turing_art/ui/common/pagination/widgets/pagination_widget.dart';
import 'package:turing_art/ui/common/pull_down_list/pull_down_list_widget.dart';
import 'package:turing_art/ui/common/title_bar_with_help_tip/title_bar_with_help_bubble_widget.dart';
import 'package:turing_art/ui/export_history/viewModel/export_history_view_model.dart';
import 'package:turing_art/ui/export_history/widgets/aigc_export_history_item_widget.dart';
import 'package:turing_art/ui/export_history/widgets/export_history_header_widget.dart';
import 'package:turing_art/ui/export_history/widgets/export_history_item_widget.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class ExportHistoryDialog extends StatefulWidget {
  final ExportHistoryType initialExportType;

  const ExportHistoryDialog({
    super.key,
    this.initialExportType = ExportHistoryType.export,
  });

  /// 显示导出历史弹窗
  static void show({
    ExportHistoryType exportType = ExportHistoryType.export,
  }) {
    if (PGDialog.isDialogVisible(DialogTags.exportHistory)) {
      PGLog.d('ExportHistoryDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 1162,
      height: 680,
      needBlur: false,
      tag: DialogTags.exportHistory,
      child: ExportHistoryDialog(initialExportType: exportType),
    );
  }

  /// 在Unity上显示导出历史弹窗
  static Future<void> showOnUnity({
    ExportHistoryType exportType = ExportHistoryType.export,
  }) async {
    if (PGDialog.isDialogVisible(DialogTags.exportHistory)) {
      PGLog.d(
          'ExportHistoryDialog showOnUnity, but dialog already exist, return');
      return;
    }
    await PGDialog.showCustomDialogOnUnity(
      width: 1162,
      height: 680,
      needBlur: false,
      tag: DialogTags.exportHistory,
      child: ExportHistoryDialog(initialExportType: exportType),
    );
  }

  @override
  State<ExportHistoryDialog> createState() => _ExportHistoryDialogState();
}

class _ExportHistoryDialogState extends State<ExportHistoryDialog> {
  // 分页组件Key（后续此类也需要时间筛选）
  final PaginationWidgetKey _paginationKey =
      const PaginationWidgetKey('exportHistoryPagination');
  // 用于强制重建日期控件的key
  Key _dateRangeWidgetKey = UniqueKey();

  // 处理导出历史类型切换
  void _handleExportHistoryTypeChange(
      ExportHistoryViewModel viewModel, ExportHistoryType newType) {
    // 检查分类是否真正改变
    if (viewModel.exportHistoryType == newType) {
      // 分类没有改变，不执行任何操作
      return;
    }

    setState(() {
      // 重新生成key以强制重建日期控件，从而清空日期数据
      _dateRangeWidgetKey = UniqueKey();
    });

    // 设置新的导出历史类型（这会自动调用resetFilter重置页码）
    viewModel.exportHistoryType = newType;

    // 同步页码控件显示为第1页
    _syncCurrentPage(1);
  }

  // 同步当前页码到分页控件
  void _syncCurrentPage(int currentPage) {
    _paginationKey.currentState?.updateCurrentPage(currentPage);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ExportHistoryViewModel(
        context.read<ExportHistoryRepository>(),
        context.read<AigcEntranceManager>(),
        context.read<CurrentUserRepository>(),
        initialExportType: widget.initialExportType,
      ),
      child: Container(
        width: 1162,
        height: 680,
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1F),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Column(
            children: [
              // 标题栏 - 使用通用组件
              const TitleBarWithHelpBubbleWidget(
                title: '导出计费明细',
                tooltipTitle: '导出计费规则:',
                tooltipContentItems: [
                  '1.按导出成功的张数扣除片量（导出失败时请重试）',
                  '2.同一照片多次导出只算一次（源文件被修改、替换或重新插入会重新计算）',
                  '3.原图导出不扣片量',
                ],
                dialogTag: DialogTags.exportHistory,
              ),

              // 筛选条件视图
              _buildFilterSection(),

              // 列表头和内容
              Expanded(
                child: _buildListSection(),
              ),

              // 分页
              _buildPaginationArea(),
            ],
          ),
        ),
      ),
    );
  }

  // 构建列表区域（包含列表头和内容）
  Widget _buildListSection() {
    return Consumer<ExportHistoryViewModel>(
      builder: (context, viewModel, _) {
        return Column(
          children: [
            // 列表头
            ExportHistoryHeaderWidget(
              exportHistoryType: viewModel.exportHistoryType,
            ),

            // 列表内容
            Expanded(
              child: _buildListContent(viewModel),
            ),
          ],
        );
      },
    );
  }

  // 构建列表内容
  Widget _buildListContent(ExportHistoryViewModel viewModel) {
    if (viewModel.exportHistoryType == ExportHistoryType.aigc) {
      return DataListWidget(
        isLoading: viewModel.isLoading,
        items: viewModel.aigcExportHistoryModel?.consumptions ?? [],
        height: 359,
        padding: EdgeInsets.zero,
        itemBuilder: (context, item, index) {
          return AIGCExportHistoryItemWidget(
            item: item,
            index: index,
          );
        },
      );
    } else {
      return DataListWidget(
        isLoading: viewModel.isLoading,
        items: viewModel.exportHistoryModel?.items ?? [],
        height: 359,
        padding: EdgeInsets.zero,
        itemBuilder: (context, item, index) {
          return ExportHistoryItemWidget(
            item: item,
            index: index,
            users: viewModel.exportHistoryModel?.users,
            exportHistoryType: viewModel.exportHistoryType,
          );
        },
      );
    }
  }

  // 构建筛选条件区域
  Widget _buildFilterSection() {
    return Consumer<ExportHistoryViewModel>(
      builder: (context, viewModel, _) {
        return Container(
          margin:
              const EdgeInsets.only(top: 16, left: 21, right: 21, bottom: 16),
          child: Row(
            children: [
              // 左侧下拉列表
              PullDownListWidget(
                width: 160,
                height: 40,
                items: viewModel.exportHistoryTypeInfos
                    .map((e) => e.title)
                    .toList(),
                selectedItem: viewModel.exportHistoryTypeInfos
                    .firstWhere((e) =>
                        e.exportHistoryType == viewModel.exportHistoryType)
                    .title,
                collapsedTextColor: const Color(0xFFEBEDF5).withOpacity(0.65),
                collapsedTextSize: 12,
                listTextColor: const Color(0xFFEBEDF5).withOpacity(0.65),
                listTextSize: 14,
                listTextHoverColor: const Color(0xFFEBEDF5),
                onItemSelected: (item, index) {
                  _handleExportHistoryTypeChange(
                    viewModel,
                    viewModel.exportHistoryTypeInfos[index].exportHistoryType,
                  );
                },
              ),

              const SizedBox(width: 16),

              // 右侧日期选择器
              SizedBox(
                width: 314,
                height: 40,
                child: InputDateRangeWidget(
                  key: _dateRangeWidgetKey,
                  onDateRangeChanged: (startDate, endDate) {
                    PGLog.d('startDate: $startDate, endDate: $endDate');

                    // 如果日期被清空（两个都为null），重置页码为1并重新请求数据
                    if (startDate == null && endDate == null) {
                      viewModel.resetPageAndDate();
                    } else {
                      viewModel.startedAt = startDate;
                      viewModel.endedAt = endDate;
                      viewModel.executeFetchExportHistory();
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建分页区域
  Widget _buildPaginationArea() {
    return Selector<ExportHistoryViewModel, (int, ExportHistoryType)>(
      // 监听totalPage和exportHistoryType的变化
      selector: (_, viewModel) {
        int totalPage = 1;
        if (viewModel.exportHistoryType == ExportHistoryType.aigc) {
          final aigcModel = viewModel.aigcExportHistoryModel;
          if (aigcModel != null) {
            totalPage = (aigcModel.total / aigcModel.pageSize).ceil();
          }
        } else {
          totalPage = viewModel.exportHistoryModel?.pagination.totalPage ?? 1;
        }
        return (totalPage, viewModel.exportHistoryType);
      },
      builder: (context, data, _) {
        final totalPage = data.$1;
        final viewModel =
            Provider.of<ExportHistoryViewModel>(context, listen: false);

        return PaginationWidget(
          key: _paginationKey,
          initialPage: 1,
          initialTotalPage: totalPage,
          onPageChanged: (page) async {
            // await viewModel.fetchExportHistory(page, null, null, null);
            viewModel.page = page;
          },
        );
      },
    );
  }
}
