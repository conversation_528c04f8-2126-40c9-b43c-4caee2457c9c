import 'package:flutter/material.dart';
import 'package:turing_art/ui/profile/widgets/profile_dialog.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../../ui/core/themes/fonts.dart';
import '../../../utils/screen_util.dart';
import '../../export_result/widgets/export_dialog.dart';
import '../../setting/widgets/general_setting_dialog.dart';

class ProjectHomeTopBar extends StatelessWidget {
  const ProjectHomeTopBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          const SizedBox(height: 24),
          Row(
            children: [
              const Text(
                'TulingArt',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              Container(
                height: 20,
                width: 75,
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 3),
                decoration: BoxDecoration(
                  color: const Color(0xFF222526),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  AppInfoExtension.getVersionString(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: const Color(0xFFEBF2F5).withAlpha(180),
                    fontFamily: 'SFProText',
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              // 导出进度按钮
              Container(
                width: 110,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withAlpha(25),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      PGLog.e(
                          "screenWidth: ${ScreenUtil().screenWidth} screenHeight: ${ScreenUtil().screenHeight}");
                      ExportDialog.show();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/icons/home_top_bar_export.png',
                          width: 24,
                          height: 24,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '导出进度',
                          style: TextStyle(
                            color: const Color(0xFFEBF2F5).withAlpha(100),
                            fontSize: 14,
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.semiBold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // 个人中心按钮
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF121415),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Image.asset(
                    'assets/icons/home_mine_entrance.png',
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () => ProfileDialog.show(context),
                  constraints: const BoxConstraints(),
                  splashRadius: 24,
                ),
              ),
              // 设置按钮
              const SizedBox(width: 16),
              SizedBox(
                width: 40,
                height: 40,
                child: IconButton(
                  icon: const Icon(Icons.settings),
                  onPressed: () => GeneralSettingDialog.show(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
