// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feedback_content.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FeedbackContent _$FeedbackContentFromJson(Map<String, dynamic> json) {
  return _FeedbackContent.fromJson(json);
}

/// @nodoc
mixin _$FeedbackContent {
  String get category => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  List<String>? get attachments => throw _privateConstructorUsedError;
  FeedbackClientInfo? get clientInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FeedbackContentCopyWith<FeedbackContent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedbackContentCopyWith<$Res> {
  factory $FeedbackContentCopyWith(
          FeedbackContent value, $Res Function(FeedbackContent) then) =
      _$FeedbackContentCopyWithImpl<$Res, FeedbackContent>;
  @useResult
  $Res call(
      {String category,
      String content,
      List<String>? attachments,
      FeedbackClientInfo? clientInfo});

  $FeedbackClientInfoCopyWith<$Res>? get clientInfo;
}

/// @nodoc
class _$FeedbackContentCopyWithImpl<$Res, $Val extends FeedbackContent>
    implements $FeedbackContentCopyWith<$Res> {
  _$FeedbackContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = null,
    Object? content = null,
    Object? attachments = freezed,
    Object? clientInfo = freezed,
  }) {
    return _then(_value.copyWith(
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      attachments: freezed == attachments
          ? _value.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      clientInfo: freezed == clientInfo
          ? _value.clientInfo
          : clientInfo // ignore: cast_nullable_to_non_nullable
              as FeedbackClientInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FeedbackClientInfoCopyWith<$Res>? get clientInfo {
    if (_value.clientInfo == null) {
      return null;
    }

    return $FeedbackClientInfoCopyWith<$Res>(_value.clientInfo!, (value) {
      return _then(_value.copyWith(clientInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FeedbackContentImplCopyWith<$Res>
    implements $FeedbackContentCopyWith<$Res> {
  factory _$$FeedbackContentImplCopyWith(_$FeedbackContentImpl value,
          $Res Function(_$FeedbackContentImpl) then) =
      __$$FeedbackContentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String category,
      String content,
      List<String>? attachments,
      FeedbackClientInfo? clientInfo});

  @override
  $FeedbackClientInfoCopyWith<$Res>? get clientInfo;
}

/// @nodoc
class __$$FeedbackContentImplCopyWithImpl<$Res>
    extends _$FeedbackContentCopyWithImpl<$Res, _$FeedbackContentImpl>
    implements _$$FeedbackContentImplCopyWith<$Res> {
  __$$FeedbackContentImplCopyWithImpl(
      _$FeedbackContentImpl _value, $Res Function(_$FeedbackContentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = null,
    Object? content = null,
    Object? attachments = freezed,
    Object? clientInfo = freezed,
  }) {
    return _then(_$FeedbackContentImpl(
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      attachments: freezed == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      clientInfo: freezed == clientInfo
          ? _value.clientInfo
          : clientInfo // ignore: cast_nullable_to_non_nullable
              as FeedbackClientInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedbackContentImpl implements _FeedbackContent {
  const _$FeedbackContentImpl(
      {required this.category,
      required this.content,
      final List<String>? attachments,
      this.clientInfo})
      : _attachments = attachments;

  factory _$FeedbackContentImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedbackContentImplFromJson(json);

  @override
  final String category;
  @override
  final String content;
  final List<String>? _attachments;
  @override
  List<String>? get attachments {
    final value = _attachments;
    if (value == null) return null;
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final FeedbackClientInfo? clientInfo;

  @override
  String toString() {
    return 'FeedbackContent(category: $category, content: $content, attachments: $attachments, clientInfo: $clientInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedbackContentImpl &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            (identical(other.clientInfo, clientInfo) ||
                other.clientInfo == clientInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, category, content,
      const DeepCollectionEquality().hash(_attachments), clientInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedbackContentImplCopyWith<_$FeedbackContentImpl> get copyWith =>
      __$$FeedbackContentImplCopyWithImpl<_$FeedbackContentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedbackContentImplToJson(
      this,
    );
  }
}

abstract class _FeedbackContent implements FeedbackContent {
  const factory _FeedbackContent(
      {required final String category,
      required final String content,
      final List<String>? attachments,
      final FeedbackClientInfo? clientInfo}) = _$FeedbackContentImpl;

  factory _FeedbackContent.fromJson(Map<String, dynamic> json) =
      _$FeedbackContentImpl.fromJson;

  @override
  String get category;
  @override
  String get content;
  @override
  List<String>? get attachments;
  @override
  FeedbackClientInfo? get clientInfo;
  @override
  @JsonKey(ignore: true)
  _$$FeedbackContentImplCopyWith<_$FeedbackContentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
