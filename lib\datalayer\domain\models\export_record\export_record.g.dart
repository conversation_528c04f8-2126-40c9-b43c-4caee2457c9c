// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExportRecord _$ExportRecordFromJson(Map<String, dynamic> json) => ExportRecord(
      guid: json['guid'] as String,
      name: json['name'] as String?,
      showName: json['showName'] as String?,
      exportPaths: (json['exportPaths'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      exportState: (json['exportState'] as num).toInt(),
      createTime: (json['createTime'] as num).toInt(),
      operateTime: json['operateTime'] as String,
      itemCount: (json['itemCount'] as num).toInt(),
      successNum: (json['successNum'] as num).toInt(),
      errorNum: (json['errorNum'] as num?)?.toInt(),
      errorMessage: json['errorMessage'] as String?,
      isSample: json['isSample'] as bool,
    );

Map<String, dynamic> _$ExportRecordToJson(ExportRecord instance) =>
    <String, dynamic>{
      'guid': instance.guid,
      'name': instance.name,
      'showName': instance.showName,
      'exportPaths': instance.exportPaths,
      'exportState': instance.exportState,
      'createTime': instance.createTime,
      'operateTime': instance.operateTime,
      'itemCount': instance.itemCount,
      'successNum': instance.successNum,
      'errorNum': instance.errorNum,
      'errorMessage': instance.errorMessage,
      'isSample': instance.isSample,
    };
