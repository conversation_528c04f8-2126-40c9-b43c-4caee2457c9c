import 'dart:async';
import 'dart:convert';

import 'package:turing_art/config/env_config.dart';
import 'package:turing_art/datalayer/domain/enums/user_info_change_event_type.dart';
import 'package:turing_art/datalayer/domain/models/store/store.dart';
import 'package:turing_art/datalayer/domain/models/user/creator.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/service/current_user_store_info/current_user_store_info_service.dart';
import 'package:turing_art/datalayer/service/database/dao/creator_info_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/user_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/handler/user_state_handler.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/version_util.dart';

import 'current_user_repository.dart';

class CurrentUserRepositoryImpl implements CurrentUserRepository {
  final UserStateHandler _userStateHandler = UserStateHandler();
  final DataBase _db;
  final CurrentUserStoreInfoService _currentUserStoreInfoService;

  // 新增事件流控制器（用于添加微信后通知UI刷新）
  final _streamController =
      StreamController<UserInfoChangeEventType>.broadcast();

  // 兼容主动和check被动变更
  @override
  Stream<UserInfoChangeEventType> get currentUserInfoChange =>
      _streamController.stream;

  @override
  User? get user => _currentUser;

  @override
  Store? get store => _currentStore;

  @override
  Creator? get creator => _creatorInfo;

  @override
  bool get isLoggedIn => _currentUser != null;

  User? _currentUser;
  Store? _currentStore;
  Creator? _creatorInfo;

  CurrentUserRepositoryImpl({
    required DataBase db,
    required CurrentUserStoreInfoService currentUserStoreInfoService,
  })  : _db = db,
        _currentUserStoreInfoService = currentUserStoreInfoService {
    _initialize();
  }

  /// 初始化函数
  Future<void> _initialize() async {
    final uid = await getUserId();
    final eid = await getEmployeeId();
    if (uid == null || eid == null) {
      return;
    }

    // 根据应用当前版本号判断是否需要强制重新登录
    if (await _needForceReLogin()) {
      return;
    }

    _readFromDB(uid, eid);
  }

  Future<bool> _needForceReLogin() async {
    // 获取上次登录的版本号
    final lastLoginVersion = SharedPreferencesService.getLastLoginVersion();
    final currentVersion = EnvConfig.version;

    // 如果上次登录版本为空（首次登录）或当前版本小于强制重新登录版本，则强制重新登录
    bool needForceReLogin = lastLoginVersion.isEmpty ||
        VersionUtil.isVersionLessThan(
            lastLoginVersion, EnvConfig.forceReLoginVersion);

    if (needForceReLogin) {
      PGLog.i('应用版本升级，需要重新登录: 上次登录版本=$lastLoginVersion, 当前版本=$currentVersion');
      await clearCurrentUser();
      return true;
    }

    // 记录当前版本为最后登录版本
    if (lastLoginVersion != currentVersion) {
      SharedPreferencesService.setLastLoginVersion(currentVersion);
    }
    return false;
  }

  void _readFromDB(String uid, String eid) {
    // 这两张表主键不一样，需要区别处理
    _db.getCreatorInfoById(uid).then((creatorEntity) async {
      if (creatorEntity != null) {
        _creatorInfo = Creator.fromEntity(creatorEntity);
        syncCreator(_creatorInfo);
      }
    });

    _db.getUserById(eid).then((userEntity) async {
      if (userEntity != null) {
        final user = User.fromEntity(userEntity);
        // 从SharedPreferences读取store字符串并转为Store对象
        final storeJson = SharedPreferencesService.getStore();
        final store = storeJson.isNotEmpty
            ? Store.fromJson(jsonDecode(storeJson))
            : const Store(
                id: '',
                name: '',
                address: '',
                tel: '',
                customerSupport: '',
                volumeControl: false,
              );
        syncCurrentUser(user, store);
      }
    });
  }

  // 从持久化存储获取用户ID
  @override
  Future<String?> getUserId() async {
    final currentUser = _currentUser;
    if (currentUser != null) {
      return currentUser.userId;
    }
    return SharedPreferencesService.getUserId();
  }

  // 从持久化存储获取用户员工ID
  @override
  Future<String?> getEmployeeId() async {
    final currentUser = _currentUser;
    if (currentUser != null) {
      return currentUser.id;
    }
    return SharedPreferencesService.getEmployeeId();
  }

  // 从持久化存储获取用户Token
  @override
  Future<String?> getUserToken() async {
    final currentUser = _currentUser;
    if (currentUser != null) {
      return currentUser.token;
    }
    return SharedPreferencesService.getUserToken();
  }

  // 从持久化存储获取Token过期时间
  @override
  Future<String?> getTokenExpire() async {
    final currentUser = _currentUser;
    if (currentUser != null) {
      return currentUser.tokenEnd;
    }
    return SharedPreferencesService.getTokenExpire();
  }

  // 检查是否需要刷新Token
  @override
  Future<bool> needToRefreshToken() async {
    final userId = await getUserId();
    final userToken = await getUserToken();
    if (userId == null || userToken == null) {
      return false;
    }

    final expireStr = await getTokenExpire();
    if (expireStr == null) {
      return false;
    }

    final expiresAt = DateTime.parse(expireStr);
    final now = DateTime.now();
    return !expiresAt.isAfter(now.add(const Duration(days: 7)));
  }

  @override
  Future<void> clearCurrentUser() async {
    SharedPreferencesService.clear();
    _currentUser = null;
    _currentStore = null;
    _userStateHandler.handleUserStateChange(null, null);
  }

  // 同步creator（子账号情况下需要展示主账号）信息到持久化存储
  @override
  Future<void> syncCreator(Creator? creator) async {
    if (creator != null) {
      SharedPreferencesService.setCreator(jsonEncode(creator.toJson()));
    }
    _creatorInfo = creator;
  }

  @override
  Future<void> syncCurrentUser(User user, Store? store) async {
    PGLog.d(
        'CurrentUserRepositoryImpl - syncCurrentUser storeId: ${store?.id}');

    SharedPreferencesService.setUserInfo(
      user.userId,
      user.id,
      user.token,
      user.tokenEnd,
      store?.id ?? '',
    );

    // 用户成功登录后，更新最后登录版本号
    SharedPreferencesService.setLastLoginVersion(EnvConfig.version);

    // store转为json字符串，再保存
    if (store != null) {
      SharedPreferencesService.setStore(jsonEncode(store.toJson()));
    }

    _currentUser = user;
    _currentStore = store;
    PGLog.d(
        'CurrentUserRepositoryImpl - syncCurrentUser isfirstlogin: ${user.firstLogin}');
    _userStateHandler.handleUserStateChange(user, store);
    _streamController.add(UserInfoChangeEventType.userRefreshed);
  }

  @override
  Future<void> refreshStore() async {
    try {
      final store = await _currentUserStoreInfoService.getCurrentUserStore();
      // 如果store有变更，则更新
      if (store != _currentStore) {
        _currentStore = store;
        // 通知UI刷新(目前是否加微信成功导致一系列UI变更)
        _streamController.add(UserInfoChangeEventType.storeRefreshed);
        // 同步最新store信息到缓存
        SharedPreferencesService.setStore(jsonEncode(store.toJson()));
      }
    } catch (e) {
      PGLog.e('Failed to refresh store: $e');
    }
  }

  /// 检查用户是否拥有某个能力
  @override
  bool hasCapability(UserCapability capability) {
    final capabilities = _currentStore?.capabilities ?? [];
    return capabilities.contains(capability.capabilityName);
  }
}
