import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/dialog/core/alert_dialog.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 删除工程弹窗
/// 由AlertDialog组件实现UI
class DeleteProjectDialog {
  static const _tag = "DeleteProjectDialog";

  /// 展示弹窗
  /// [onConfirm] 确认回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  /// [onCancel] 取消回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  static void show({
    Function? onConfirm,
    Function? onCancel,
  }) {
    SmartDialog.show(
      maskColor: const Color(0x99000000),
      animationType: SmartAnimationType.fade,
      builder: (context) => AlertDialog(
        title: "确认删除？",
        content: "删除后将无法恢复，请谨慎删除！",
        confirmText: "确认删除",
        cancelText: "取消",
        onConfirm: onConfirm ?? hide,
        onCancel: onCancel ?? hide,
      ),
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (controller, child, param) =>
          AnimatedDialog(controller: controller, child: child),
      tag: _tag,
    );
  }

  /// 隐藏弹窗
  static Future<void> hide() async {
    await PGDialog.dismiss(tag: _tag);
  }
}
