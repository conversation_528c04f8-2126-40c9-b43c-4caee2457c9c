import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/services/hostname_service.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理导出完成消息的UseCase
class HandleExportCompletedUseCase {
  final ExportUseCaseProvider _exportUseCaseProvider;
  final AccountRepository _accountRepository;
  final NetworkProvider _networkProvider;
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  HandleExportCompletedUseCase(
    this._exportUseCaseProvider,
    this._currentUserRepository,
    this._accountRepository,
    this._networkProvider,
    this._projectRepository,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    if (message.args == null || message.args!.isEmpty) {
      PGLog.e('处理导出完成消息失败: 参数为空');
      return;
    }
    try {
      final argsCount = message.args?.length ?? 0;
      bool result = argsCount > 0 ? message.args![0] as bool : false;
      var isCancelled = argsCount > 1 ? message.args![1] as bool : false;
      var imagePHash = argsCount > 2 ? message.args![2] as String : '';
      var tokenId = argsCount > 3 ? message.args![3] as String? : '';
      var projectId = argsCount > 4 ? message.args![4] as String? : '';
      var exportTypeStr = argsCount > 5
          ? message.args![5] as String?
          : 'retouch'; // 返回retouch或者sample

      // 解析导出类型
      ExportType exportType = ExportType.retouch;
      if (exportTypeStr == 'sample') {
        exportType = ExportType.sample;
      }

      // 刷新账户信息（不管成功与否都需要，因为存在兑换码过期这一情况）
      _accountRepository.refreshAllAccount();

      // result是true不用管，是false区分
      if (result == false) {
        var tips = '';
        if (isCancelled) {
          tips = '导出已取消';
        } else {
          // 优先检查网络状态
          final isConnected = await _networkProvider.isConnected();
          if (!isConnected) {
            tips = '网络连接错误，请检查网络设置';
          } else if (tokenId == null || tokenId.isEmpty) {
            tips = '未获取导出权限，网络错误或余额不足';
            // 子账号提示
            final currentUser = _currentUserRepository.user;
            if (currentUser != null && currentUser.role == UserRole.employee) {
              tips = '余额不足请联系主账号进行充值';
            }
          } else {
            tips = '导出流程出错';
          }
        }
        // 这里可以根据是否获取到tokenId来判断是否获取导出权限
        if (GlobalVars.isWinEditScreenActive) {
          PGDialog.showToastOnUnity(tips);
        } else {
          PGDialog.showToast(tips);
        }
        return;
      }

      PGLog.d(
          'EditViewModel: 导出项完成: $result, $imagePHash, $tokenId, $projectId, 导出类型: ${exportType.name}');

      // 调用报告导出成功的用例
      final reportTokenId =
          tokenId?.isEmpty == true || tokenId == null ? 'origin' : tokenId;
      var projectName = '';
      var hostname = await HostnameService.getHostname();

      if (projectId != null && projectId.isNotEmpty) {
        final project = await _projectRepository.getProjectById(projectId);
        projectName = project?.name ?? '';
      }
      _exportUseCaseProvider.reportExportSuccess.invoke(
        imagePHash,
        reportTokenId,
        projectName,
        projectId ?? '',
        hostname,
        exportType: exportType,
      );
    } catch (e) {
      PGLog.e('EditViewModel: 导出项完成错误: $e');
    }
  }
}
