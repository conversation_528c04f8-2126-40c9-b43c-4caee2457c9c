import 'package:turing_art/core/service/disk_info_service/models/disk_info_cache_entry.dart';
import 'package:turing_art/core/service/disk_info_service/models/disk_info_models.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘信息缓存管理
/// 负责磁盘信息的内存缓存管理
class DiskInfoCache {
  /// 磁盘信息缓存 - 使用磁盘根路径作为key（如 "C:\" 或 "/"）
  final Map<String, DiskInfoCacheEntry> _cache = {};

  /// 获取缓存的磁盘信息数量
  int get size => _cache.length;

  /// 检查是否有缓存
  bool get isEmpty => _cache.isEmpty;

  /// 检查是否有缓存
  bool get isNotEmpty => _cache.isNotEmpty;

  /// 获取指定磁盘根路径的缓存
  /// [driveRoot] 磁盘根路径，如 "C:\" 或 "/"
  /// [allowExpired] 是否允许使用过期的缓存
  DiskInfoCacheEntry? get(String driveRoot, {bool allowExpired = true}) {
    final cached = _cache[driveRoot];
    if (cached == null) {
      return null;
    }

    if (!allowExpired && cached.isExpired()) {
      PGLog.d('DiskInfoCache: 缓存已过期: $driveRoot');
      return null;
    }

    return cached;
  }

  /// 设置缓存
  /// [driveRoot] 磁盘根路径
  /// [type] 磁盘类型
  void set(String driveRoot, DiskType type) {
    final entry = DiskInfoCacheEntry(
      driveRoot: driveRoot,
      type: type,
      cachedAt: DateTime.now(),
    );
    _cache[driveRoot] = entry;
    PGLog.d('DiskInfoCache: 缓存已更新: $driveRoot -> $type');
  }

  /// 批量设置缓存
  /// [entries] 缓存条目映射
  void setBatch(Map<String, DiskType> entries) {
    final now = DateTime.now();
    for (final entry in entries.entries) {
      _cache[entry.key] = DiskInfoCacheEntry(
        driveRoot: entry.key,
        type: entry.value,
        cachedAt: now,
      );
    }
    PGLog.d('DiskInfoCache: 批量缓存已更新: ${entries.length}个条目');
  }

  /// 检查是否包含指定的磁盘根路径
  bool contains(String driveRoot) {
    return _cache.containsKey(driveRoot);
  }

  /// 获取所有缓存条目
  List<DiskInfoCacheEntry> getAll() {
    return _cache.values.toList();
  }

  /// 获取所有缓存的磁盘根路径
  List<String> getAllDriveRoots() {
    return _cache.keys.toList();
  }

  /// 清理过期的缓存项
  /// [maxAge] 最大缓存时间
  /// 返回清理的缓存条目数量
  int cleanExpired({Duration maxAge = const Duration(hours: 24)}) {
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      if (entry.value.isExpired(maxAge: maxAge)) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      PGLog.i('DiskInfoCache: 清理了${expiredKeys.length}个过期缓存项');
    }

    return expiredKeys.length;
  }

  /// 清空所有缓存
  void clear() {
    final count = _cache.length;
    _cache.clear();
    if (count > 0) {
      PGLog.i('DiskInfoCache: 清空了$count个缓存项');
    }
  }

  /// 移除指定的缓存
  bool remove(String driveRoot) {
    final removed = _cache.remove(driveRoot);
    if (removed != null) {
      PGLog.d('DiskInfoCache: 移除缓存: $driveRoot');
      return true;
    }
    return false;
  }

  @override
  String toString() {
    return 'DiskInfoCache(size: ${_cache.length}, drives: ${_cache.keys.toList()})';
  }
}
