import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_config/cache_config.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class DiskCacheGuardViewModel extends ChangeNotifier {
  final DiskCacheManager _diskCacheManager;
  final SettingRepository _settingRepository;
  final ProjectCacheRuleProvider _projectCacheRuleProvider;
  bool _isInitialized = false;

  DiskCacheGuardViewModel(
    this._diskCacheManager,
    this._settingRepository,
    this._projectCacheRuleProvider,
  ) {
    // 监听项目缓存规则变化
    _projectCacheRuleProvider.addListener(_onProjectCacheRuleChanged);

    // 初始化缓存配置
    unawaited(_initializeCacheConfig().catchError((error) {
      PGLog.e('DiskCacheGuardViewModel - 初始化失败: $error');
    }));
  }

  @override
  void dispose() {
    // 移除监听器
    _projectCacheRuleProvider.removeListener(_onProjectCacheRuleChanged);
    super.dispose();
  }

  /// 初始化缓存配置
  Future<void> _initializeCacheConfig() async {
    try {
      final cacheSetting = await _settingRepository.getCacheCleanSetting();
      final cacheConfig = CacheConfig.fromGB(
        cleanupDays: CacheValidTime.fromDays(cacheSetting.maxDays),
        cleanupThresholdSizeGB: cacheSetting.maxSize,
      );
      _diskCacheManager.updateCacheConfig(
        cacheConfig,
        tryCleanupWithConfig: _isInitialized,
      );
      PGLog.d('DiskCacheGuardViewModel - 缓存配置初始化完成');
    } catch (e) {
      PGLog.e('DiskCacheGuardViewModel - 初始化缓存配置失败: $e');
    }
  }

  /// 当项目缓存规则发生变化时的回调
  void _onProjectCacheRuleChanged() {
    _isInitialized = true;
    PGLog.d('DiskCacheGuardViewModel - 检测到项目缓存规则变化，触发清理检查');
    // 当规则更新时，触发条件清理
    _diskCacheManager.tryCleanupConditional().then((result) {
      PGLog.d(
          'DiskCacheGuardViewModel - 规则变化触发清理完成: ${result.totalDeletedSize} 字节');
    }).catchError((error) {
      PGLog.e('DiskCacheGuardViewModel - 规则变化触发清理失败: $error');
    });
  }

  /// 页面重新变为活跃时的回调
  void onPageActive() {
    PGLog.d('DiskCacheGuardViewModel - 页面重新变为活跃，触发清理检查');
    _diskCacheManager.tryCleanupConditional();
  }

  /// 手动触发清理（用于测试或特殊情况）
  Future<void> triggerCleanup() async {
    PGLog.d('DiskCacheGuardViewModel - 手动触发清理');
    await _diskCacheManager.tryCleanupConditional();
  }
}
