import 'dart:async';
import 'dart:io';

import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_processors.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/utils/pg_log.dart';

class ProjectCoverGenerater {
  final AigcService _aigcService;
  final MediaRepository _mediaRepository;

  // 添加销毁标志和资源管理
  bool _disposed = false;
  final Map<String, StreamSubscription<AigcTaskResultMessage>>
      _activeSubscriptions = {};
  final Map<String, Completer<File?>> _activeCompleters = {};

  ProjectCoverGenerater({
    required AigcService aigcService,
    required MediaRepository mediaRepository,
  })  : _aigcService = aigcService,
        _mediaRepository = mediaRepository;

  Future<File?> getCoverThumbnail(String projectId, String fileId) async {
    return getCoverThumbnailWithPriority(projectId, fileId, 0);
  }

  Future<File?> getCoverThumbnailWithPriority(
      String projectId, String fileId, int projectIndex) async {
    // 检查是否已被销毁
    if (_disposed) {
      PGLog.w('ProjectCoverGenerater: 尝试在已销毁的实例上生成封面');
      return null;
    }

    PGLog.d(
        'ProjectCoverGenerater: 开始生成封面 - projectId: $projectId, fileId: $fileId');

    // 可能已经产生过icon，（精修工程）
    final minIconResource = _mediaRepository.getFileResource(
      projectId,
      fileId,
      MediaResourceType.midIcon,
    );

    if (minIconResource != null && minIconResource.existsSync()) {
      PGLog.d('ProjectCoverGenerater: 找到已存在的minIcon资源');
      return minIconResource;
    }

    // 可能已经产生过（AIGC工程）
    final coverResource = _mediaRepository.getFileResource(
      projectId,
      fileId,
      MediaResourceType.coverResource,
    );

    if (coverResource != null && coverResource.existsSync()) {
      PGLog.d('ProjectCoverGenerater: 找到已存在的cover资源');
      return coverResource;
    }

    // 生成icon
    // 可能已经产生过（AIGC工程）
    final originalResource = await _mediaRepository.getFileResourceAsync(
      projectId,
      fileId,
      MediaResourceType.originalResource,
    );

    if (originalResource == null || !originalResource.existsSync()) {
      PGLog.w('ProjectCoverGenerater: 原始资源不存在 - fileId: $fileId');
      return null;
    }

    final coverPath = _mediaRepository
        .getResourceFilePath(
          projectId,
          fileId,
          MediaResourceType.coverResource,
        )
        .path;

    PGLog.d(
        'ProjectCoverGenerater: 开始生成缩略图 - inputPath: ${originalResource.path}, outputPath: $coverPath');

    final taskId =
        AigcService.generateTaskId(originalResource.path, AigcTaskType.cover);

    Completer<File?> completer = Completer<File?>();
    StreamSubscription<AigcTaskResultMessage>? subscription;

    try {
      // 检查是否已被销毁
      if (_disposed) {
        PGLog.w('ProjectCoverGenerater: 在生成过程中被销毁，取消操作');
        return null;
      }

      // 记录活跃的 Completer 和 Subscription
      _activeCompleters[taskId] = completer;

      // 监听任务结果
      subscription = _aigcService.resultStream.listen((event) {
        // 检查是否已被销毁
        if (_disposed) {
          PGLog.d('ProjectCoverGenerater: 在监听过程中被销毁，忽略结果');
          return;
        }

        // 使用正确的taskId匹配逻辑
        final expectedTaskId = AigcService.generateTaskId(
            originalResource.path, AigcTaskType.cover);

        if (event.taskId == expectedTaskId) {
          PGLog.d('ProjectCoverGenerater: 收到任务结果 - success: ${event.success}');

          // 清理活跃的 Completer
          final activeCompleter = _activeCompleters.remove(taskId);
          if (activeCompleter == null) {
            PGLog.w('ProjectCoverGenerater: 未找到对应的 Completer，可能已被清理');
            return;
          }

          if (event.success && event.resultData != null) {
            // 任务成功，返回生成的预览文件
            final result = event.resultData as AigcCoverTaskResult;
            final coverFile =
                result.coverPath != null ? File(result.coverPath!) : null;

            if (coverFile != null && coverFile.existsSync()) {
              PGLog.d(
                  'ProjectCoverGenerater: 缩略图生成成功 - path: ${coverFile.path}');
              activeCompleter.complete(coverFile);
            } else {
              PGLog.w('ProjectCoverGenerater: 缩略图文件不存在');
              activeCompleter.complete(null);
            }
          } else {
            // 任务失败
            PGLog.e(
                'ProjectCoverGenerater: 缩略图生成失败 - error: ${event.errorMessage}');
            activeCompleter.complete(null);
          }
        }
      });

      // 记录活跃的 Subscription
      _activeSubscriptions[taskId] = subscription;

      // 提交任务，使用项目索引作为优先级排序
      // 索引越小的项目（越靠前显示）优先级越高
      await _aigcService.submitTask(
          inputPath: originalResource.path,
          outputPath: coverPath,
          fileId: fileId,
          taskType: AigcTaskType.cover,
          sortBy: projectIndex >= 0 ? projectIndex : 9999, // 无效索引使用最低优先级
          executeNow: false);

      PGLog.d('ProjectCoverGenerater: 任务已提交，等待结果...');

      // 等待任务完成，设置超时
      return await completer.future.timeout(
        const Duration(minutes: 1),
        onTimeout: () {
          PGLog.w('ProjectCoverGenerater: 任务超时');
          // 清理超时的 Completer
          _activeCompleters.remove(taskId);
          completer.complete(null);
          return null;
        },
      );
    } catch (e) {
      PGLog.e('ProjectCoverGenerater: 生成封面时发生异常 - $e');
      // 清理异常的 Completer
      _activeCompleters.remove(taskId);
      completer.complete(null);
      return null;
    } finally {
      // 清理资源
      subscription?.cancel();
      _activeSubscriptions.remove(taskId);
    }
  }

  /// 清理所有活跃的资源
  void _cleanupActiveResources() {
    PGLog.d(
        'ProjectCoverGenerater: 清理活跃资源 - 活跃 Completers: ${_activeCompleters.length}, 活跃 Subscriptions: ${_activeSubscriptions.length}');

    // 取消所有活跃的 Completer
    for (final completer in _activeCompleters.values) {
      if (!completer.isCompleted) {
        completer.complete(null);
      }
    }
    _activeCompleters.clear();

    // 取消所有活跃的 Subscription
    for (final subscription in _activeSubscriptions.values) {
      subscription.cancel();
    }
    _activeSubscriptions.clear();
  }

  /// 销毁 ProjectCoverGenerater 实例
  void dispose() {
    if (_disposed) {
      return;
    }

    PGLog.d('ProjectCoverGenerater: 销毁实例');
    _disposed = true;

    // 清理所有活跃的资源
    _cleanupActiveResources();
  }

  /// 检查是否已被销毁
  bool get isDisposed => _disposed;

  /// 获取活跃任务数量
  int get activeTaskCount => _activeCompleters.length;
}
