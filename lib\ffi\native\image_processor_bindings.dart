// 自动生成的图片处理器FFI绑定文件
// 基于image_processor_api.h头文件生成

import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/models/image_processor_model.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

// FFI函数签名定义
typedef ImageProcessorInitNative = Int32 Function();
typedef ImageProcessorInitDart = int Function();

typedef ImageProcessorDecodeNative = Int32 Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<ImageProcessorOptions> options,
    Pointer<Utf8> errorMessage,
    Int32 errorMessageSize);
typedef ImageProcessorDecodeDart = int Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<ImageProcessorOptions> options,
    Pointer<Utf8> errorMessage,
    int errorMessageSize);

typedef ImageProcessorEncodeNative = Int32 Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<ImageProcessorOptions> options,
    Pointer<Utf8> errorMessage,
    Int32 errorMessageSize);
typedef ImageProcessorEncodeDart = int Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<ImageProcessorOptions> options,
    Pointer<Utf8> errorMessage,
    int errorMessageSize);

typedef ImageProcessorBatchProcessNative = Int32 Function(
    Pointer<Utf8> inputFolder,
    Pointer<Utf8> outputFolder,
    Pointer<ImageProcessorOptions> options,
    Int32 recursive,
    Pointer<Utf8> errorMessage,
    Int32 errorMessageSize);
typedef ImageProcessorBatchProcessDart = int Function(
    Pointer<Utf8> inputFolder,
    Pointer<Utf8> outputFolder,
    Pointer<ImageProcessorOptions> options,
    int recursive,
    Pointer<Utf8> errorMessage,
    int errorMessageSize);

typedef ImageProcessorFreeNative = Void Function();
typedef ImageProcessorFreeDart = void Function();

typedef ImageProcessorGetDefaultOptionsNative = Void Function(
    Pointer<ImageProcessorOptions> options);
typedef ImageProcessorGetDefaultOptionsDart = void Function(
    Pointer<ImageProcessorOptions> options);

typedef ImageProcessorIsInitializedNative = Int32 Function();
typedef ImageProcessorIsInitializedDart = int Function();

typedef ImageProcessorGetImageInfoNative = Int32 Function(
    Pointer<Utf8> filePath,
    Pointer<Int32> width,
    Pointer<Int32> height,
    Pointer<Int32> channels);
typedef ImageProcessorGetImageInfoDart = int Function(Pointer<Utf8> filePath,
    Pointer<Int32> width, Pointer<Int32> height, Pointer<Int32> channels);

typedef ImageProcessorExtractExifThumbnailNative = Int32 Function(
    Pointer<Utf8> inputFile,
    Int32 maxSize,
    Pointer<Utf8> outputFile,
    Pointer<Utf8> errorMessage,
    Int32 errorMessageSize);
typedef ImageProcessorExtractExifThumbnailDart = int Function(
    Pointer<Utf8> inputFile,
    int maxSize,
    Pointer<Utf8> outputFile,
    Pointer<Utf8> errorMessage,
    int errorMessageSize);

// 动态库加载和函数绑定
class ImageProcessorBindings {
  static DynamicLibrary? _dylib;
  static late ImageProcessorInitDart _imageProcessorInit;
  static late ImageProcessorDecodeDart _imageProcessorDecode;
  static late ImageProcessorEncodeDart _imageProcessorEncode;
  static late ImageProcessorBatchProcessDart _imageProcessorBatchProcess;
  static late ImageProcessorFreeDart _imageProcessorFree;
  static late ImageProcessorGetDefaultOptionsDart
      _imageProcessorGetDefaultOptions;
  static late ImageProcessorIsInitializedDart _imageProcessorIsInitialized;
  static late ImageProcessorGetImageInfoDart _imageProcessorGetImageInfo;
  static late ImageProcessorExtractExifThumbnailDart
      _imageProcessorExtractExifThumbnail;

  static void _loadLibrary() {
    if (_dylib != null) {
      return;
    }

    try {
      _dylib = UniversalPlatformLoader.loadSugoiNativeLibrary();

      // 绑定函数
      _imageProcessorInit = _dylib!
          .lookup<NativeFunction<ImageProcessorInitNative>>(
              'ImageProcessor_Init')
          .asFunction<ImageProcessorInitDart>();

      _imageProcessorDecode = _dylib!
          .lookup<NativeFunction<ImageProcessorDecodeNative>>(
              'ImageProcessor_Decode')
          .asFunction<ImageProcessorDecodeDart>();

      _imageProcessorEncode = _dylib!
          .lookup<NativeFunction<ImageProcessorEncodeNative>>(
              'ImageProcessor_Encode')
          .asFunction<ImageProcessorEncodeDart>();

      _imageProcessorBatchProcess = _dylib!
          .lookup<NativeFunction<ImageProcessorBatchProcessNative>>(
              'ImageProcessor_BatchProcess')
          .asFunction<ImageProcessorBatchProcessDart>();

      _imageProcessorFree = _dylib!
          .lookup<NativeFunction<ImageProcessorFreeNative>>(
              'ImageProcessor_Free')
          .asFunction<ImageProcessorFreeDart>();

      _imageProcessorGetDefaultOptions = _dylib!
          .lookup<NativeFunction<ImageProcessorGetDefaultOptionsNative>>(
              'ImageProcessor_GetDefaultOptions')
          .asFunction<ImageProcessorGetDefaultOptionsDart>();

      _imageProcessorIsInitialized = _dylib!
          .lookup<NativeFunction<ImageProcessorIsInitializedNative>>(
              'ImageProcessor_IsInitialized')
          .asFunction<ImageProcessorIsInitializedDart>();

      _imageProcessorGetImageInfo = _dylib!
          .lookup<NativeFunction<ImageProcessorGetImageInfoNative>>(
              'ImageProcessor_GetImageInfo')
          .asFunction<ImageProcessorGetImageInfoDart>();

      _imageProcessorExtractExifThumbnail = _dylib!
          .lookup<NativeFunction<ImageProcessorExtractExifThumbnailNative>>(
              'ImageProcessor_ExtractExifThumbnail')
          .asFunction<ImageProcessorExtractExifThumbnailDart>();
    } catch (e) {
      PGLog.e('加载ImageProcessor动态库失败: $e');
      _dylib = null;
      return;
    }
  }

  // 公共接口
  static int init() {
    _loadLibrary();
    return _imageProcessorInit();
  }

  static int decode(
    String inputFile,
    String outputFile,
    Pointer<ImageProcessorOptions>? options,
    int errorMessageSize,
  ) {
    _loadLibrary();
    final inputFilePtr = inputFile.toNativeUtf8();
    final outputFilePtr = outputFile.toNativeUtf8();
    final errorMessagePtr = malloc.allocate<Utf8>(errorMessageSize);

    try {
      return _imageProcessorDecode(
        inputFilePtr,
        outputFilePtr,
        options ?? nullptr,
        errorMessagePtr,
        errorMessageSize,
      );
    } finally {
      malloc.free(inputFilePtr);
      malloc.free(outputFilePtr);
      malloc.free(errorMessagePtr);
    }
  }

  static int encode(
    String inputFile,
    String outputFile,
    Pointer<ImageProcessorOptions>? options,
    int errorMessageSize,
  ) {
    _loadLibrary();
    final inputFilePtr = inputFile.toNativeUtf8();
    final outputFilePtr = outputFile.toNativeUtf8();
    final errorMessagePtr = malloc.allocate<Utf8>(errorMessageSize);

    try {
      return _imageProcessorEncode(
        inputFilePtr,
        outputFilePtr,
        options ?? nullptr,
        errorMessagePtr,
        errorMessageSize,
      );
    } finally {
      malloc.free(inputFilePtr);
      malloc.free(outputFilePtr);
      malloc.free(errorMessagePtr);
    }
  }

  static int batchProcess(
    String inputFolder,
    String outputFolder,
    Pointer<ImageProcessorOptions>? options, {
    required bool recursive,
    required int errorMessageSize,
  }) {
    _loadLibrary();
    final inputFolderPtr = inputFolder.toNativeUtf8();
    final outputFolderPtr = outputFolder.toNativeUtf8();
    final errorMessagePtr = malloc.allocate<Utf8>(errorMessageSize);

    try {
      return _imageProcessorBatchProcess(
        inputFolderPtr,
        outputFolderPtr,
        options ?? nullptr,
        recursive ? 1 : 0,
        errorMessagePtr,
        errorMessageSize,
      );
    } finally {
      malloc.free(inputFolderPtr);
      malloc.free(outputFolderPtr);
      malloc.free(errorMessagePtr);
    }
  }

  static void free() {
    _loadLibrary();
    _imageProcessorFree();
  }

  static void getDefaultOptions(Pointer<ImageProcessorOptions> options) {
    _loadLibrary();
    _imageProcessorGetDefaultOptions(options);
  }

  static bool isInitialized() {
    _loadLibrary();
    return _imageProcessorIsInitialized() == 1;
  }

  static ImageInfo? getImageInfo(String filePath) {
    _loadLibrary();
    final filePathPtr = filePath.toNativeUtf8();
    final widthPtr = malloc<Int32>();
    final heightPtr = malloc<Int32>();
    final channelsPtr = malloc<Int32>();

    try {
      final result = _imageProcessorGetImageInfo(
        filePathPtr,
        widthPtr,
        heightPtr,
        channelsPtr,
      );

      if (result == ImageProcessorErrorCode.success) {
        return ImageInfo(
          width: widthPtr.value,
          height: heightPtr.value,
          channels: channelsPtr.value,
        );
      }
      return null;
    } finally {
      malloc.free(filePathPtr);
      malloc.free(widthPtr);
      malloc.free(heightPtr);
      malloc.free(channelsPtr);
    }
  }

  static int extractExifThumbnail(
    String inputFile,
    int maxSize,
    String outputFile,
    int errorMessageSize,
  ) {
    _loadLibrary();
    final inputFilePtr = inputFile.toNativeUtf8();
    final outputFilePtr = outputFile.toNativeUtf8();
    final errorMessagePtr = malloc.allocate<Utf8>(errorMessageSize);

    try {
      return _imageProcessorExtractExifThumbnail(
        inputFilePtr,
        maxSize,
        outputFilePtr,
        errorMessagePtr,
        errorMessageSize,
      );
    } finally {
      malloc.free(inputFilePtr);
      malloc.free(outputFilePtr);
      malloc.free(errorMessagePtr);
    }
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo() {
    return UniversalPlatformLoader.getLibraryInfo(
      'PGImageProcessor',
      subDirectory: 'image_processor',
    );
  }
}
