import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:turing_art/ffi/native/windows_api_bindings.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'file_path_interface.dart';

/// Mac系统生成的元数据文件夹名称常量
const String macOSMetadataFolder = '__MACOSX';

class DesktopFilePathHandler implements FilePathInterface {
  @override
  Future<String> getDesktopPath() async {
    try {
      if (Platform.isWindows) {
        return _getWindowsDesktopPath();
      } else if (Platform.isMacOS) {
        return _getMacOSDesktopPath();
      } else if (Platform.isLinux) {
        return _getLinuxDesktopPath();
      } else {
        // 不支持的平台返回临时目录
        final tempDir = await getTemporaryDirectory();
        return tempDir.path;
      }
    } catch (e) {
      PGLog.e("获取桌面路径失败: $e");
      // 获取失败时返回临时目录
      final tempDir = await getTemporaryDirectory();
      return tempDir.path;
    }
  }

  @override
  Future<bool> openFolder(String filePath) async {
    if (filePath.isEmpty) {
      PGLog.w('DesktopFilePathHandler: 路径为空，无法打开');
      return false;
    }
    // 检查文件夹或者文件路径是否存在
    if (!File(filePath).existsSync()) {
      if (!Directory(filePath).existsSync()) {
        PGLog.w('DesktopFilePathHandler: 路径不存在: $filePath');
        return false;
      }
    }

    try {
      await _openFolderByPlatform(filePath);
      PGLog.d('DesktopFilePathHandler: 成功打开文件夹: $filePath');
      return true;
    } catch (e) {
      PGLog.e('DesktopFilePathHandler: 打开文件夹失败: $e');
      return false;
    }
  }

  /// 检查路径是否存在（兼容文件和文件夹）
  /// [path] 要检查的路径
  /// 返回true表示路径存在，false表示不存在
  static bool pathExists(String path) {
    try {
      return FileSystemEntity.isFileSync(path) ||
          FileSystemEntity.isDirectorySync(path);
    } catch (e) {
      return false;
    }
  }

  /// 获取文件系统实体类型
  /// [path] 要检查的路径
  /// 返回 FileSystemEntityType，如果路径不存在则返回 FileSystemEntityType.notFound
  static FileSystemEntityType getEntityType(String path) {
    try {
      if (FileSystemEntity.isFileSync(path)) {
        return FileSystemEntityType.file;
      } else if (FileSystemEntity.isDirectorySync(path)) {
        return FileSystemEntityType.directory;
      } else {
        return FileSystemEntityType.notFound;
      }
    } catch (e) {
      return FileSystemEntityType.notFound;
    }
  }

  /// 根据平台打开文件夹
  Future<void> _openFolderByPlatform(String path) async {
    switch (defaultTargetPlatform) {
      case TargetPlatform.windows:
        // Windows: 区分文件和文件夹处理
        if (FileSystemEntity.isFileSync(path)) {
          // 如果是文件，打开文件夹并选中该文件
          await Process.run('explorer', ['/select,', path],
              stdoutEncoding: const SystemEncoding(),
              stderrEncoding: const SystemEncoding());
        } else {
          // 如果是文件夹，在新窗口中打开并使用资源管理器视图
          await Process.run('explorer', ['/n,', '/e,', path],
              stdoutEncoding: const SystemEncoding(),
              stderrEncoding: const SystemEncoding());
        }
        break;
      case TargetPlatform.macOS:
        // macOS: 使用open
        await Process.run('open', [path]);
        break;
      case TargetPlatform.linux:
        // Linux: 使用xdg-open
        await Process.run('xdg-open', [path]);
        break;
      default:
        throw UnsupportedError('当前平台不支持打开文件夹: ${defaultTargetPlatform.name}');
    }
  }

  /// 获取Windows桌面路径
  String _getWindowsDesktopPath() {
    // 首选方案：使用USERPROFILE环境变量
    final userProfile = Platform.environment['USERPROFILE'] ?? '';
    if (userProfile.isNotEmpty) {
      return '$userProfile\\Desktop';
    }

    // 备用方案1：使用HOMEDRIVE和HOMEPATH环境变量
    final homeDrive = Platform.environment['HOMEDRIVE'] ?? '';
    final homePath = Platform.environment['HOMEPATH'] ?? '';
    if (homeDrive.isNotEmpty && homePath.isNotEmpty) {
      return '$homeDrive$homePath\\Desktop';
    }

    // 备用方案2：使用当前目录
    return '${Directory.current.path}\\Desktop';
  }

  /// 获取macOS桌面路径
  String _getMacOSDesktopPath() {
    // 首选方案：使用HOME环境变量
    final homeDir = Platform.environment['HOME'] ?? '';
    if (homeDir.isNotEmpty) {
      return '$homeDir/Desktop';
    }

    // 备用方案：使用当前目录
    return '${Directory.current.path}/Desktop';
  }

  /// 获取Linux桌面路径
  String _getLinuxDesktopPath() {
    // 首选方案：使用HOME环境变量
    final homeDir = Platform.environment['HOME'] ?? '';
    if (homeDir.isNotEmpty) {
      return '$homeDir/Desktop';
    }

    // 备用方案1：使用XDG_DESKTOP_DIR环境变量
    final xdgDesktopDir = Platform.environment['XDG_DESKTOP_DIR'] ?? '';
    if (xdgDesktopDir.isNotEmpty) {
      return xdgDesktopDir;
    }

    // 备用方案2：使用当前目录
    return '${Directory.current.path}/Desktop';
  }

  @override
  bool isHiddenFile(File file) {
    return isHiddenFileStatic(file);
  }

  /// 静态方法：检查文件是否为隐藏文件
  /// 可以在 isolate 中安全使用
  static bool isHiddenFileStatic(File file) {
    try {
      final fileName = file.path.split(Platform.pathSeparator).last;

      if (Platform.isWindows) {
        // Windows系统：检查文件属性
        return WindowsApiBindings.isHiddenFile(file.path);
      } else if (Platform.isMacOS || Platform.isLinux) {
        // Unix-like系统（macOS/Linux）：以点开头的文件为隐藏文件
        return fileName.startsWith('.');
      } else {
        // 其他平台默认以点开头判断
        return fileName.startsWith('.');
      }
    } catch (e) {
      // 在isolate中无法使用PGLog，静默处理错误
      return false;
    }
  }

  /// 静态方法：检查路径是否为隐藏文件或文件夹
  /// 可以在 isolate 中安全使用，兼容文件和文件夹
  static bool isHiddenPath(String path) {
    try {
      final fileName = path.split(Platform.pathSeparator).last;

      if (Platform.isWindows) {
        // Windows系统：检查文件属性（兼容文件和文件夹）
        return WindowsApiBindings.isHiddenFile(path);
      } else if (Platform.isMacOS || Platform.isLinux) {
        // Unix-like系统（macOS/Linux）：以点开头的文件或文件夹为隐藏
        return fileName.startsWith('.');
      } else {
        // 其他平台默认以点开头判断
        return fileName.startsWith('.');
      }
    } catch (e) {
      // 在isolate中无法使用PGLog，静默处理错误
      return false;
    }
  }

  /// 检查文件或目录是否应该被跳过的核心逻辑（统一的文件过滤方法）
  /// [path] 文件或目录路径
  /// [enableLogging] 是否启用日志记录（在isolate中应设为false）
  /// 跳过条件：
  /// 1. 隐藏文件（Windows使用文件属性，Unix系统使用文件名以点开头）
  /// 2. Mac系统产生的 __MACOSX 文件夹（仅在非Mac平台上跳过）
  static bool shouldSkipFileOrDirectory(String path,
      {bool enableLogging = true}) {
    try {
      final fileName = path.split(Platform.pathSeparator).last;

      // 检查是否为 __MACOSX 文件夹（仅在非Mac平台上跳过）
      // 在Mac平台上，__MACOSX 文件夹通常是正常可见的
      if (!Platform.isMacOS) {
        if (fileName == macOSMetadataFolder) {
          if (enableLogging) {
            PGLog.d('跳过Mac系统产生的$macOSMetadataFolder文件夹: $path');
          }
          return true;
        }

        // 检查路径中是否包含 __MACOSX（可能是子目录）
        if (path.contains(
                '${Platform.pathSeparator}$macOSMetadataFolder${Platform.pathSeparator}') ||
            path.contains('${Platform.pathSeparator}$macOSMetadataFolder')) {
          if (enableLogging) {
            PGLog.d('跳过$macOSMetadataFolder目录下的文件: $path');
          }
          return true;
        }
      }

      // 检查是否为隐藏文件或文件夹
      if (pathExists(path) && isHiddenPath(path)) {
        if (enableLogging) {
          final entityType = getEntityType(path);
          final typeText =
              entityType == FileSystemEntityType.file ? '文件' : '文件夹';
          PGLog.d('跳过隐藏$typeText: $path');
        }
        return true;
      }

      return false;
    } catch (e) {
      if (enableLogging) {
        PGLog.e('检查文件是否应跳过时出错: $path, 错误: $e');
      }
      return false; // 出错时不跳过，继续处理
    }
  }
}

/// 移动端文件路径处理器实现
class MobileFilePathHandler implements FilePathInterface {
  @override
  Future<String> getDesktopPath() async {
    // 移动端没有桌面路径的概念，返回应用文档目录
    PGLog.w('MobileFilePathHandler: 移动端没有桌面路径的概念');
    return '';
  }

  @override
  Future<bool> openFolder(String path) async {
    PGLog.w('MobileFilePathHandler: 移动端不支持打开文件夹');
    return false;
  }

  @override
  bool isHiddenFile(File file) {
    PGLog.w('MobileFilePathHandler: 移动端不支持判断隐藏文件');
    return false;
  }
}
