import 'package:pg_ops_sdk/config/device_info_provider.dart';

import '../../utils/device_info_util.dart';
import '../../config/env_config.dart';

class OpsDeviceInfoProvider extends DeviceInfoProvider {
  @override
  String getAPPVersion() {
    return EnvConfig.version;
  }

  @override
  String getAppId() {
    return EnvConfig.appId;
  }

  @override
  String getChannel() {
    return DeviceInfoUtil().channel;
  }

  @override
  String getDeviceId() {
    return DeviceInfoUtil().deviceId;
  }

  @override
  String getDeviceModel() {
    return DeviceInfoUtil().model;
  }

  @override
  String getInitStamp() {
    return DeviceInfoUtil().initStamp;
  }

  @override
  String getLanguage() {
    return DeviceInfoUtil().language;
  }

  @override
  String getLocale() {
    return DeviceInfoUtil().locale;
  }

  @override
  String getNetwork() {
    return DeviceInfoUtil().network;
  }

  @override
  String getOsVersion() {
    return DeviceInfoUtil().osVersion;
  }

  @override
  String getPlatform() {
    return DeviceInfoUtil().platform;
  }

  @override
  String getScreenSize() {
    return DeviceInfoUtil().screenSize;
  }

  @override
  String getUtcOffset() {
    return DeviceInfoUtil().utcOffset;
  }

  @override
  String getUpgradeStamp() {
    return DeviceInfoUtil().initStamp;
  }
}
