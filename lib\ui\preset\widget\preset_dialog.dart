import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/collect/customaction/popup_action.dart'
    as collect;
import 'package:provider/provider.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/preset/preset_item.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/setting/widgets/setting_segment.dart';

import '../../../utils/pg_dialog/dialog_tags.dart';
import '../../../utils/pg_dialog/pg_dialog.dart';
import '../view_model/preset_dialog_view_model.dart';
import 'preset_selector.dart';

class PresetDialog extends StatefulWidget {
  final Function(PresetItem? preset, String? path) confirmClick;

  const PresetDialog({super.key, required this.confirmClick});

  @override
  State<PresetDialog> createState() => _PresetDialogState();

  static void show(
    BuildContext context,
    Function(
      PresetItem? preset,
      String? path,
    ) confirmClick,
  ) {
    PGDialog.showCustomDialog(
      width: 437,
      height: 350,
      tag: DialogTags.preset,
      needBlur: true,
      child: PresetDialog(confirmClick: confirmClick),
    );
  }
}

class _PresetDialogState extends State<PresetDialog> {
  // 是否是默认位置
  bool _isOriginalLocation = true;
  // 是否展示预设选择器
  bool _showPresetSelector = false;

  // 是否悬停在浏览上
  bool _hoveredFolder = false;
  // 是否悬停在确认上
  bool _hoveredConfirm = false;
  // 当前悬停的分类索引
  int? _hoveredCategoryIndex;
  // 当前悬停的预设项索引
  int? _hoveredItemIndex;

  // 用于埋点记录用户id
  String? _userId;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_userId == null) {
      _userId = context.read<CurrentUserRepository>().user?.effectiveId;
      _recordEvent(collect.Action.show);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => PresetDialogViewModel(context.read<UnityController>()),
      child: Consumer<PresetDialogViewModel>(
        builder: (context, viewModel, child) {
          return ValueListenableBuilder<bool>(
              valueListenable: viewModel.isLoading,
              builder: (context, isLoading, _) {
                if (isLoading) {
                  return _buildLoading();
                }
                return _buildContent(context);
              });
        },
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFF121315),
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 56,
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '批量修图',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const Spacer(),
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: Colors.white.withAlpha(150),
                        ),
                        onPressed: () {
                          _recordEvent(collect.Action.close);
                          PGDialog.dismiss(tag: DialogTags.preset);
                        },
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        iconSize: 24,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '应用预设',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular),
              ),
              const SizedBox(height: 12),
              PresetSelector(
                presetUiStatus:
                    context.read<PresetDialogViewModel>().presetUiStatus,
                onTap: () =>
                    setState(() => _showPresetSelector = !_showPresetSelector),
                displayText: context
                        .read<PresetDialogViewModel>()
                        .presetUiStatus
                        .currentPresetItem
                        .value
                        ?.title ??
                    '请选择预设',
              ),
              const SizedBox(height: 16),
              Text('保存位置',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular)),
              const SizedBox(height: 12),
              _buildSegment(
                  context, ['原始位置', '自定义位置'], _isOriginalLocation ? 0 : 1),
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        _isOriginalLocation
                            ? context
                                .read<PresetDialogViewModel>()
                                .defaultSavePath
                            : context
                                        .read<PresetDialogViewModel>()
                                        .customSavePath ==
                                    null
                                ? context
                                    .read<PresetDialogViewModel>()
                                    .defaultSavePath
                                : '当前位置： ${context.read<PresetDialogViewModel>().customSavePath}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 10,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                        ),
                      ),
                    ),
                  ),
                  if (!_isOriginalLocation)
                    PlatformMouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (_) => setState(() => _hoveredFolder = true),
                      onExit: (_) => setState(() => _hoveredFolder = false),
                      child: Container(
                        width: 40,
                        height: 24,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: _hoveredFolder
                              ? const Color(0xFF26292A)
                              : const Color(0xFF1B1E1F),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () async {
                              await context
                                  .read<PresetDialogViewModel>()
                                  .selectCustomSavePath();
                              setState(() {});
                            },
                            borderRadius: BorderRadius.circular(4),
                            child: Center(
                              child: Text(
                                '浏览',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 10,
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontWeight: Fonts.regular,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              const Spacer(),
              _buildBottomBar(context),
              const SizedBox(height: 12),
            ],
          ),
        ),
        if (_showPresetSelector) _buildSelectList(context),
      ],
    );
  }

  // 路径选择器
  Widget _buildSegment(
    BuildContext context,
    List<String> choices,
    int selectIndex,
  ) {
    return SizedBox(
      height: 36,
      width: double.infinity,
      child: SettingSegment(
        segments: choices,
        initialSelection: selectIndex,
        onSegmentSelected: (index) {
          setState(() {
            _isOriginalLocation = index == 0;
          });
        },
        backgroundColor: const Color(0xFF000000),
        selectedBackgroundColor: const Color(0xFF222526),
        textColor: const Color(0xFFEBF2F5).withOpacity(0.6),
        selectedTextColor: Colors.white,
        fontSize: 12,
        fontWeight: Fonts.regular,
        selectedFontWeight: Fonts.medium,
        height: 36,
        borderRadius: 6,
      ),
    );
  }

  /// 预设选择器
  Widget _buildSelectList(BuildContext context) {
    final presets =
        context.read<PresetDialogViewModel>().presetUiStatus.presets.value;
    final ScrollController scrollController = ScrollController();
    return Positioned(
      top: 152, // 位于预设选择器下方
      left: 16,
      right: 0,
      child: SizedBox(
        height: 176,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 200,
              decoration: BoxDecoration(
                color: const Color(0xFF1B1E1F),
                borderRadius: BorderRadius.circular(12),
              ),
              child: RawScrollbar(
                thumbVisibility: true,
                trackVisibility: false,
                thickness: 2,
                radius: const Radius.circular(16),
                thumbColor: const Color(0xFF676767),
                controller: scrollController,
                padding: const EdgeInsets.only(top: 8, right: 6, bottom: 8),
                child: ValueListenableBuilder(
                  valueListenable: context
                      .read<PresetDialogViewModel>()
                      .presetUiStatus
                      .currentPresetIdx,
                  builder: (context, currentIdx, _) {
                    final presets = context
                        .read<PresetDialogViewModel>()
                        .presetUiStatus
                        .presets
                        .value;
                    return ListView.builder(
                      controller: scrollController,
                      padding: const EdgeInsets.fromLTRB(8, 8, 12, 8),
                      itemCount: presets.length,
                      itemBuilder: (context, index) {
                        final preset = presets[index];
                        final isSelected = index == currentIdx;
                        return InkWell(
                          onTap: () {
                            // 选中某分类
                            context
                                .read<PresetDialogViewModel>()
                                .selectPreset(index);
                          },
                          child: PlatformMouseRegion(
                            cursor: SystemMouseCursors.click,
                            onEnter: (_) =>
                                setState(() => _hoveredCategoryIndex = index),
                            onExit: (_) =>
                                setState(() => _hoveredCategoryIndex = null),
                            child: Container(
                              height: 40,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? const Color(0xFFFFFFFF).withAlpha(12)
                                    : _hoveredCategoryIndex == index
                                        ? const Color(0xFFFFFFFF).withAlpha(12)
                                        : Colors.transparent,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      preset.category,
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : const Color(0xFFEBF2F5)
                                                .withOpacity(0.6),
                                        fontSize: 12,
                                        fontFamily: Fonts.defaultFontFamily,
                                        fontWeight: isSelected
                                            ? Fonts.semiBold
                                            : Fonts.regular,
                                      ),
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    color: const Color(0xFFEBF2F5)
                                        .withOpacity(0.6),
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 4),
            ValueListenableBuilder(
              valueListenable: context
                  .read<PresetDialogViewModel>()
                  .presetUiStatus
                  .currentPresetIdx,
              builder: (context, currentIdx, _) {
                if (presets.isEmpty) {
                  return const SizedBox();
                }
                final currentPreset = presets[currentIdx];
                final ScrollController itemScrollController =
                    ScrollController();
                return Container(
                  width: 200,
                  height: 136,
                  decoration: BoxDecoration(
                    color: const Color(0xFF1B1E1F),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: RawScrollbar(
                    thumbVisibility: true,
                    trackVisibility: false,
                    thickness: 2,
                    radius: const Radius.circular(16),
                    thumbColor: const Color(0xFF676767),
                    controller: itemScrollController,
                    padding: const EdgeInsets.only(top: 8, right: 6, bottom: 8),
                    child: ValueListenableBuilder(
                      valueListenable: context
                          .read<PresetDialogViewModel>()
                          .presetUiStatus
                          .currentPresetItem,
                      builder: (context, currentItem, _) {
                        return ListView.builder(
                          controller: itemScrollController,
                          padding: const EdgeInsets.fromLTRB(8, 8, 12, 8),
                          itemCount: currentPreset.items.length,
                          itemBuilder: (context, index) {
                            final item = currentPreset.items[index];
                            final isSelected = item == currentItem;
                            return InkWell(
                              onTap: () {
                                context
                                    .read<PresetDialogViewModel>()
                                    .selectPresetItem(item);
                                setState(() => _showPresetSelector = false);
                              },
                              child: PlatformMouseRegion(
                                cursor: SystemMouseCursors.click,
                                onEnter: (_) =>
                                    setState(() => _hoveredItemIndex = index),
                                onExit: (_) =>
                                    setState(() => _hoveredItemIndex = null),
                                child: Container(
                                  height: 40,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? const Color(0xFFFFFFFF).withAlpha(12)
                                        : _hoveredItemIndex == index
                                            ? const Color(0xFFFFFFFF)
                                                .withAlpha(12)
                                            : Colors.transparent,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          item.title,
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : const Color(0xFFEBF2F5)
                                                    .withOpacity(0.6),
                                            fontSize: 12,
                                            fontFamily: Fonts.defaultFontFamily,
                                            fontWeight: isSelected
                                                ? Fonts.semiBold
                                                : Fonts.regular,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 顶部操作栏
  Widget _buildBottomBar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: 80,
          height: 40,
          child: TextButton(
            onPressed: () {
              _recordEvent(collect.Action.close);
              PGDialog.dismiss(tag: DialogTags.preset);
            },
            style: TextButton.styleFrom(
              backgroundColor: const Color(0xFF1B1E1F),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('取消',
                style: TextStyle(
                  color: const Color(0xFFEBF2F5).withAlpha(150),
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                )),
          ),
        ),
        const SizedBox(width: 12),
        SizedBox(
          width: 80,
          height: 40,
          child: ValueListenableBuilder(
            valueListenable: context
                .read<PresetDialogViewModel>()
                .presetUiStatus
                .currentPresetItem,
            builder: (context, currentPresetItem, _) {
              // 判断是否已选择预设
              final bool hasSelectedPreset = currentPresetItem != null;

              return PlatformMouseRegion(
                cursor: hasSelectedPreset
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.basic,
                onEnter: hasSelectedPreset
                    ? (event) {
                        setState(() {
                          _hoveredConfirm = true;
                        });
                      }
                    : null,
                onExit: hasSelectedPreset
                    ? (event) {
                        setState(() {
                          _hoveredConfirm = false;
                        });
                      }
                    : null,
                child: ElevatedButton(
                  onPressed: () {
                    if (hasSelectedPreset) {
                      final vm = context.read<PresetDialogViewModel>();
                      widget.confirmClick(
                          vm.currentPresetItem, vm.getSavePath());
                      _recordEvent(collect.Action.click);
                      PGDialog.dismiss(tag: DialogTags.preset);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: !hasSelectedPreset
                        ? const Color(0xFF7B4AFF)
                            .withOpacity(0.5) // 使用原有颜色，只降低透明度
                        : _hoveredConfirm
                            ? const Color(0xFF8253FF)
                            : const Color(0xFF7B4AFF),
                    disabledBackgroundColor:
                        const Color(0xFF7B4AFF).withOpacity(0.5),
                    disabledForegroundColor: Colors.white.withOpacity(0.5),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Center(
                    child: Text(
                      '批量修图',
                      textAlign: TextAlign.center,
                      softWrap: false,
                      style: TextStyle(
                        color: hasSelectedPreset
                            ? Colors.white
                            : Colors.white.withOpacity(0.5), // 文字也置灰
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _recordEvent(collect.Action action) {
    collect.recordPopupAction(
        subElementId: collect.SubElementId.multiple_edit,
        userId: _userId ?? '',
        action: action);
  }
}
