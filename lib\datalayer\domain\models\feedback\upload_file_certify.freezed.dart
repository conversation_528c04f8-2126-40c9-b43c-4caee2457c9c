// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_file_certify.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UploadFileCertify _$UploadFileCertifyFromJson(Map<String, dynamic> json) {
  return _UploadFileCertify.fromJson(json);
}

/// @nodoc
mixin _$UploadFileCertify {
  String get uploadCertify => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get domain => throw _privateConstructorUsedError;
  String get accelerationDomain => throw _privateConstructorUsedError;
  String get innerDomain => throw _privateConstructorUsedError;
  String get expireAt => throw _privateConstructorUsedError;
  String get scheme => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UploadFileCertifyCopyWith<UploadFileCertify> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadFileCertifyCopyWith<$Res> {
  factory $UploadFileCertifyCopyWith(
          UploadFileCertify value, $Res Function(UploadFileCertify) then) =
      _$UploadFileCertifyCopyWithImpl<$Res, UploadFileCertify>;
  @useResult
  $Res call(
      {String uploadCertify,
      String type,
      String domain,
      String accelerationDomain,
      String innerDomain,
      String expireAt,
      String scheme});
}

/// @nodoc
class _$UploadFileCertifyCopyWithImpl<$Res, $Val extends UploadFileCertify>
    implements $UploadFileCertifyCopyWith<$Res> {
  _$UploadFileCertifyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadCertify = null,
    Object? type = null,
    Object? domain = null,
    Object? accelerationDomain = null,
    Object? innerDomain = null,
    Object? expireAt = null,
    Object? scheme = null,
  }) {
    return _then(_value.copyWith(
      uploadCertify: null == uploadCertify
          ? _value.uploadCertify
          : uploadCertify // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      domain: null == domain
          ? _value.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as String,
      accelerationDomain: null == accelerationDomain
          ? _value.accelerationDomain
          : accelerationDomain // ignore: cast_nullable_to_non_nullable
              as String,
      innerDomain: null == innerDomain
          ? _value.innerDomain
          : innerDomain // ignore: cast_nullable_to_non_nullable
              as String,
      expireAt: null == expireAt
          ? _value.expireAt
          : expireAt // ignore: cast_nullable_to_non_nullable
              as String,
      scheme: null == scheme
          ? _value.scheme
          : scheme // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadFileCertifyImplCopyWith<$Res>
    implements $UploadFileCertifyCopyWith<$Res> {
  factory _$$UploadFileCertifyImplCopyWith(_$UploadFileCertifyImpl value,
          $Res Function(_$UploadFileCertifyImpl) then) =
      __$$UploadFileCertifyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String uploadCertify,
      String type,
      String domain,
      String accelerationDomain,
      String innerDomain,
      String expireAt,
      String scheme});
}

/// @nodoc
class __$$UploadFileCertifyImplCopyWithImpl<$Res>
    extends _$UploadFileCertifyCopyWithImpl<$Res, _$UploadFileCertifyImpl>
    implements _$$UploadFileCertifyImplCopyWith<$Res> {
  __$$UploadFileCertifyImplCopyWithImpl(_$UploadFileCertifyImpl _value,
      $Res Function(_$UploadFileCertifyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadCertify = null,
    Object? type = null,
    Object? domain = null,
    Object? accelerationDomain = null,
    Object? innerDomain = null,
    Object? expireAt = null,
    Object? scheme = null,
  }) {
    return _then(_$UploadFileCertifyImpl(
      uploadCertify: null == uploadCertify
          ? _value.uploadCertify
          : uploadCertify // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      domain: null == domain
          ? _value.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as String,
      accelerationDomain: null == accelerationDomain
          ? _value.accelerationDomain
          : accelerationDomain // ignore: cast_nullable_to_non_nullable
              as String,
      innerDomain: null == innerDomain
          ? _value.innerDomain
          : innerDomain // ignore: cast_nullable_to_non_nullable
              as String,
      expireAt: null == expireAt
          ? _value.expireAt
          : expireAt // ignore: cast_nullable_to_non_nullable
              as String,
      scheme: null == scheme
          ? _value.scheme
          : scheme // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UploadFileCertifyImpl implements _UploadFileCertify {
  const _$UploadFileCertifyImpl(
      {required this.uploadCertify,
      required this.type,
      required this.domain,
      required this.accelerationDomain,
      required this.innerDomain,
      required this.expireAt,
      required this.scheme});

  factory _$UploadFileCertifyImpl.fromJson(Map<String, dynamic> json) =>
      _$$UploadFileCertifyImplFromJson(json);

  @override
  final String uploadCertify;
  @override
  final String type;
  @override
  final String domain;
  @override
  final String accelerationDomain;
  @override
  final String innerDomain;
  @override
  final String expireAt;
  @override
  final String scheme;

  @override
  String toString() {
    return 'UploadFileCertify(uploadCertify: $uploadCertify, type: $type, domain: $domain, accelerationDomain: $accelerationDomain, innerDomain: $innerDomain, expireAt: $expireAt, scheme: $scheme)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadFileCertifyImpl &&
            (identical(other.uploadCertify, uploadCertify) ||
                other.uploadCertify == uploadCertify) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.accelerationDomain, accelerationDomain) ||
                other.accelerationDomain == accelerationDomain) &&
            (identical(other.innerDomain, innerDomain) ||
                other.innerDomain == innerDomain) &&
            (identical(other.expireAt, expireAt) ||
                other.expireAt == expireAt) &&
            (identical(other.scheme, scheme) || other.scheme == scheme));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, uploadCertify, type, domain,
      accelerationDomain, innerDomain, expireAt, scheme);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadFileCertifyImplCopyWith<_$UploadFileCertifyImpl> get copyWith =>
      __$$UploadFileCertifyImplCopyWithImpl<_$UploadFileCertifyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UploadFileCertifyImplToJson(
      this,
    );
  }
}

abstract class _UploadFileCertify implements UploadFileCertify {
  const factory _UploadFileCertify(
      {required final String uploadCertify,
      required final String type,
      required final String domain,
      required final String accelerationDomain,
      required final String innerDomain,
      required final String expireAt,
      required final String scheme}) = _$UploadFileCertifyImpl;

  factory _UploadFileCertify.fromJson(Map<String, dynamic> json) =
      _$UploadFileCertifyImpl.fromJson;

  @override
  String get uploadCertify;
  @override
  String get type;
  @override
  String get domain;
  @override
  String get accelerationDomain;
  @override
  String get innerDomain;
  @override
  String get expireAt;
  @override
  String get scheme;
  @override
  @JsonKey(ignore: true)
  _$$UploadFileCertifyImplCopyWith<_$UploadFileCertifyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
