import 'dart:async';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 选中文件信息
class SelectedFileInfo {
  final String originalPath;
  final String exportPath;
  final bool isSelected;

  SelectedFileInfo({
    required this.originalPath,
    required this.exportPath,
    required this.isSelected,
  });

  factory SelectedFileInfo.fromJson(Map<String, dynamic> json) {
    return SelectedFileInfo(
      originalPath: json['originalPath'] as String,
      exportPath: json['exportPath'] as String,
      isSelected: json['isSelected'] as bool,
    );
  }
}

/// N8排版调用结果
class N8LayoutResult {
  final bool success;
  final String? errorMessage;

  const N8LayoutResult.success()
      : success = true,
        errorMessage = null;
  const N8LayoutResult.failure(this.errorMessage) : success = false;
}

/// N8排版服务
/// 负责检查N8安装路径并执行N8排版调用
class N8LayoutService {
  // N8企业版路径
  static const String _n8EnterprisePathPattern =
      r"C:\Program Files (x86)\NemoInfo\N8Cloud\N8Cloud\StudioPublisher.exe";

  // N8云版路径
  static const String _n8CloudPathPattern =
      r"C:\Program Files (x86)\NemoInfo\N8Design\N8Cloud\StudioPublisher.exe";

  /// 检查N8安装并返回可用的执行路径
  static String? _checkN8Installation() {
    // 优先检查企业版
    if (File(_n8EnterprisePathPattern).existsSync()) {
      PGLog.d('找到N8企业版: $_n8EnterprisePathPattern');
      return _n8EnterprisePathPattern;
    }

    // 检查云版
    if (File(_n8CloudPathPattern).existsSync()) {
      PGLog.d('找到N8云版: $_n8CloudPathPattern');
      return _n8CloudPathPattern;
    }

    PGLog.w('未找到N8安装');
    return null;
  }

  /// 从导出记录获取图片文件路径列表
  static Future<List<String>> _getImageFilesFromRecord(
    ExportRecord record,
    ExportRecordsRepository repository,
  ) async {
    try {
      // 使用导出记录的guid作为exportTaskId获取所有文件的finalPath
      final filePaths =
          await repository.fetchExportTaskFilesFinalPath(record.guid);

      // 过滤出图片文件
      final imageFiles =
          filePaths.where((filePath) => _isImageFile(filePath)).toList();

      PGLog.d('从导出任务获取到 ${imageFiles.length} 个图片文件');
      return imageFiles;
    } catch (e) {
      PGLog.e('获取导出任务文件失败: $e');
      return [];
    }
  }

  /// 检查文件是否为图片格式
  static bool _isImageFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
    return imageExtensions.contains(extension);
  }

  /// 构建N8调用命令参数
  static String _buildN8Command(String photoRoot, List<String> imageFiles) {
    if (imageFiles.isEmpty) {
      throw ArgumentError('图片文件列表不能为空');
    }

    // 获取图片文件名（不包含路径）
    final fileNames =
        imageFiles.map((filePath) => path.basename(filePath)).toList();

    // 构建命令格式：n8://PhotoRoot=路径|图片1.jpg|图片2.jpg|图片3.jpg
    final command = 'n8://PhotoRoot=$photoRoot|${fileNames.join('|')}';

    PGLog.d('构建N8命令: $command');
    return command;
  }

  /// 调用N8排版
  /// [record] 导出记录
  /// [repository] 导出记录仓库，用于获取文件路径
  /// 返回N8LayoutResult包含成功状态和错误信息
  /// 启动N8进程后立即返回，不等待进程完成
  static Future<N8LayoutResult> callN8Layout(
    ExportRecord record,
    ExportRecordsRepository repository,
  ) async {
    try {
      // 检查N8安装
      final n8Path = _checkN8Installation();
      if (n8Path == null) {
        PGLog.w('未找到N8安装，无法调用N8排版');
        return const N8LayoutResult.failure('未找到N8安装，请确认N8已正确安装');
      }

      // const n8Path = "mockPaht";

      // 获取图片文件
      final imageFiles = await _getImageFilesFromRecord(record, repository);
      if (imageFiles.isEmpty) {
        PGLog.w('未找到图片文件，无法调用N8排版');
        return const N8LayoutResult.failure('未找到图片文件，请确认导出目录中有图片文件');
      }

      // 使用第一个导出路径作为图片根目录
      final photoRoot = record.exportPaths.isNotEmpty
          ? record.exportPaths.first
          : path.dirname(imageFiles.first);

      // 构建N8命令
      final command = _buildN8Command(photoRoot, imageFiles);

      // 执行N8程序 - 使用Process.start启动后立即返回
      PGLog.d('开始调用N8: $n8Path');
      PGLog.d('命令参数: $command');

      // 以分离模式启动进程，确保子进程完全独立运行
      final process = await Process.start(
        n8Path,
        [command],
        runInShell: false,
        mode: ProcessStartMode.detached,
      );

      PGLog.d('N8进程已启动并分离，PID: ${process.pid}');

      // 启动成功后立即返回，不等待进程完成
      return const N8LayoutResult.success();
    } catch (e) {
      PGLog.e('调用N8排版时发生异常: $e');
      return N8LayoutResult.failure('调用N8排版时发生异常: $e');
    }
  }
}
