import 'package:freezed_annotation/freezed_annotation.dart';

part 'upload_file_certify.freezed.dart';
part 'upload_file_certify.g.dart';

@freezed
class UploadFileCertify with _$UploadFileCertify {
  const factory UploadFileCertify({
    required String uploadCertify,
    required String type,
    required String domain,
    required String accelerationDomain,
    required String innerDomain,
    required String expireAt,
    required String scheme,
  }) = _UploadFileCertify;

  factory UploadFileCertify.fromJson(Map<String, dynamic> json) =>
      _$UploadFileCertifyFromJson(json);
}
