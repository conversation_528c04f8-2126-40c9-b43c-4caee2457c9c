import '../database.dart';

extension ExportTaskDao on DataBase {
  // 查询所有导出任务
  Future<List<ExportTaskEntityData>> getAllExportTasks() async {
    final exportTaskEntities = await select(exportTaskEntity).get();
    return exportTaskEntities;
  }

  // 根据 guid 获取单个导出任务
  Future<ExportTaskEntityData?> getExportTaskById(String guid) async {
    final result = await (select(exportTaskEntity)
          ..where((t) => t.guid.equals(guid)))
        .getSingleOrNull();
    return result;
  }

  // 根据用户ID获取导出任务列表
  Future<List<ExportTaskEntityData>> getExportTasksByUserId(
    String userId,
  ) async {
    final result = await (select(exportTaskEntity)
          ..where((t) => t.userId.equals(userId)))
        .get();
    return result;
  }

  // 根据导出状态获取任务列表
  Future<List<ExportTaskEntityData>> getExportTasksByState(
    int exportState,
  ) async {
    final result = await (select(exportTaskEntity)
          ..where((t) => t.exportState.equals(exportState)))
        .get();
    return result;
  }

  // 根据多个guid获取导出任务列表
  Future<List<ExportTaskEntityData>> getExportTasksByIds(
    List<String> guids,
  ) async {
    final result = await (select(exportTaskEntity)
          ..where((t) => t.guid.isIn(guids)))
        .get();
    return result;
  }

  // 插入新导出任务
  Future<int> insertExportTask(ExportTaskEntityCompanion entity) {
    return into(exportTaskEntity).insert(entity);
  }

  // 更新导出任务信息
  Future<bool> updateExportTask(ExportTaskEntityCompanion entity) {
    return update(exportTaskEntity).replace(entity);
  }

  // 删除导出任务
  Future<int> deleteExportTask(String guid) {
    return (delete(exportTaskEntity)..where((t) => t.guid.equals(guid))).go();
  }

  // 根据用户ID删除导出任务
  Future<int> deleteExportTasksByUserId(String userId) {
    return (delete(exportTaskEntity)..where((t) => t.userId.equals(userId)))
        .go();
  }

  // 根据导出状态删除任务
  Future<int> deleteExportTasksByState(int exportState) {
    return (delete(exportTaskEntity)
          ..where((t) => t.exportState.equals(exportState)))
        .go();
  }
}
