import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/ui/dialog/import_project_choice_dialog.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 导入项目操作类型
enum ImportProjectAction {
  createNew, // 新建工程
  overwrite, // 覆盖工程
}

/// 导入项目对话框服务
/// 负责处理导入项目选择对话框的显示和用户交互
class ImportProjectDialogService {
  /// 显示导入项目选择对话框，并在用户选择后执行相应操作
  /// [context] - Flutter上下文
  /// [onCreateNew] - 创建新工程的异步操作
  /// [onOverwrite] - 覆盖工程的异步操作
  /// 返回操作是否成功执行
  static Future<bool> showImportProjectChoiceDialogWithAction(
    BuildContext context, {
    required int fileCount,
    required int selectedCount,
    required Future<bool> Function() onCreateNew,
    required Future<bool> Function() onOverwrite,
  }) async {
    final completer = Completer<bool>();

    ImportProjectChoiceDialog.show(
      onCreateNew: () async {
        ImportProjectChoiceDialog.hide();

        // 显示Loading
        PGDialog.showLoading();
        try {
          final success = await onCreateNew();
          completer.complete(success);
        } catch (e) {
          completer.complete(false);
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      },
      onOverwrite: () async {
        ImportProjectChoiceDialog.hide();

        // 显示Loading
        PGDialog.showLoading();
        try {
          final success = await onOverwrite();
          completer.complete(success);
        } catch (e) {
          completer.complete(false);
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      },
      onCancel: () {
        ImportProjectChoiceDialog.hide();
        completer.complete(false);
      },
      fileCount: fileCount,
      selectedCount: selectedCount,
    );

    return completer.future;
  }

  /// 显示导入项目选择对话框（原有方法保持兼容性）
  /// 返回用户选择的操作，如果用户取消则返回null
  static Future<ImportProjectAction?> showImportProjectChoiceDialog(
    BuildContext context, {
    required int fileCount,
    required int selectedCount,
  }) async {
    final completer = Completer<ImportProjectAction?>();

    ImportProjectChoiceDialog.show(
      onCreateNew: () {
        ImportProjectChoiceDialog.hide();
        completer.complete(ImportProjectAction.createNew);
      },
      onOverwrite: () {
        ImportProjectChoiceDialog.hide();
        completer.complete(ImportProjectAction.overwrite);
      },
      onCancel: () {
        ImportProjectChoiceDialog.hide();
        completer.complete(null);
      },
      fileCount: fileCount,
      selectedCount: selectedCount,
    );

    return completer.future;
  }

  /// 显示协作请求对话框（编辑页面状态下的特殊弹窗）
  /// [context] - Flutter上下文
  /// [onCreateNew] - 创建新工程的异步操作
  /// 返回操作是否成功执行
  static Future<bool> showCollaborationRequestDialogWithAction(
    BuildContext context, {
    required int fileCount,
    required int selectedCount,
    required Future<bool> Function() onCreateNew,
  }) async {
    final completer = Completer<bool>();

    ImportProjectChoiceDialog.showCollaborationRequest(
      onCreateNew: () async {
        ImportProjectChoiceDialog.hide();

        // 显示Loading
        PGDialog.showLoading();
        try {
          final success = await onCreateNew();
          completer.complete(success);
        } catch (e) {
          completer.complete(false);
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      },
      onCancel: () {
        ImportProjectChoiceDialog.hide();
        completer.complete(false);
      },
      fileCount: fileCount,
      selectedCount: selectedCount,
    );

    return completer.future;
  }

  /// 显示编辑页面协作请求对话框
  /// [context] - Flutter上下文
  /// [onProcessNow] - 立即处理回调
  /// [onProcessLater] - 稍后处理回调
  /// 返回操作是否成功执行
  static Future<bool> showEditPageCollaborationDialogWithAction(
    BuildContext context, {
    required Future<bool> Function() onProcessNow,
    required Future<bool> Function() onProcessLater,
  }) async {
    final completer = Completer<bool>();

    ImportProjectChoiceDialog.showEditPageCollaboration(
      onProcessNow: () async {
        ImportProjectChoiceDialog.hide();

        // 显示Loading
        PGDialog.showLoading();
        try {
          final success = await onProcessNow();
          completer.complete(success);
        } catch (e) {
          completer.complete(false);
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      },
      onProcessLater: () async {
        ImportProjectChoiceDialog.hide();

        // 显示Loading
        PGDialog.showLoading();
        try {
          final success = await onProcessLater();
          completer.complete(success);
        } catch (e) {
          completer.complete(false);
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      },
    );

    return completer.future;
  }
}
