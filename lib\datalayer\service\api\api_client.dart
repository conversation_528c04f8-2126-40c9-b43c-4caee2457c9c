import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:turing_art/utils/pg_log.dart';

class ApiClient {
  late final Dio _dio;
  final String baseUrl;
  final String? proxyUrl;

  ApiClient({required this.baseUrl, this.proxyUrl}) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 5),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    if (proxyUrl != null) {
      (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        HttpClient client = HttpClient();
        client.findProxy = (uri) {
          return "PROXY $proxyUrl"; // 设置代理
        };
        return client;
      };
    }

    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));
  }

  Future<ApiResult<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
      );
      return ApiResult.success(
        data: response.data,
        statusCode: response.statusCode ?? -1,
        message: response.statusMessage,
      );
    } catch (e) {
      PGLog.e('GET request failed: $e');
      return ApiResult.failure(
        error: _handleError(e),
        statusCode: _getStatusCode(e),
      );
    }
  }

  Future<ApiResult<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
      );

      return ApiResult.success(
        data: response.data,
        statusCode: response.statusCode ?? -1,
        message: response.statusMessage,
      );
    } catch (e) {
      PGLog.e('POST request failed: $e');
      return ApiResult.failure(
        error: _handleError(e),
        statusCode: _getStatusCode(e),
        data: e is DioException ? e.response?.data : null,
      );
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return '网络请求超时，请检查网络稍后重试';
        case DioExceptionType.badResponse:
          return _handleResponseError(
              error.response?.statusCode, error.response?.data);
        case DioExceptionType.cancel:
          return '网络请求被取消';
        case DioExceptionType.connectionError:
          return '网络连接错误，请检查网络稍后重试';
        default:
          return '网络错误';
      }
    }
    return '未知错误: $error';
  }

  String _handleResponseError(int? statusCode, dynamic data) {
    switch (statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权';
      case 403:
        return '禁止访问';
      case 404:
        return '资源不存在';
      case 500:
        return '服务器错误';
      default:
        if (data is Map && data['message'] != null) {
          return data['message'].toString();
        }
        return '请求失败: $statusCode';
    }
  }

  int _getStatusCode(dynamic error) {
    if (error is DioException && error.response?.statusCode != null) {
      return error.response!.statusCode!;
    }
    return -1;
  }

  void dispose() {
    _dio.close();
  }
}

class ApiResult<T> {
  final bool success;
  final int statusCode;
  final T? data;
  final String? message;
  final String? error;

  ApiResult({
    required this.success,
    required this.statusCode,
    this.data,
    this.message,
    this.error,
  });

  factory ApiResult.success({
    required int statusCode,
    T? data,
    String? message,
  }) {
    return ApiResult(
      success: true,
      statusCode: statusCode,
      data: data,
      message: message,
    );
  }

  factory ApiResult.failure({
    required String error,
    required int statusCode,
    T? data,
  }) {
    return ApiResult(
      success: false,
      statusCode: statusCode,
      error: error,
      data: data,
    );
  }
}
