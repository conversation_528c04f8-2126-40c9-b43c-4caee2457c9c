import 'package:pg_turing_collect_event/collect/clickaction/topbar_progress.dart';
import 'package:pg_turing_collect_event/model.dart'
    show recordProjectInfo, ProjectAction;
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 编辑页面操作服务
/// 处理编辑页面的共享操作，如项目操作上报、状态清理等
class EditActionService {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final ProjectStateProvider _projectStateProvider;
  final NavigatorService _navigator;

  EditActionService(
    this._projectRepository,
    this._currentUserRepository,
    this._projectStateProvider,
    this._navigator,
  );

  /// 处理返回首页的完整流程
  /// 包括上报操作、清理状态和导航
  Future<void> handleBackToHome(String projectId) async {
    // 1. 上报项目操作埋点
    await reportProjectAction(projectId, ProjectAction.save);

    // 2. 清理项目状态
    clearProjectState();

    // 3. 返回首页
    _navigator.pop();
  }

  /// 上报Project操作埋点
  Future<void> reportProjectAction(
      String projectId, ProjectAction action) async {
    if (projectId.isEmpty) {
      return;
    }

    try {
      final project = await _projectRepository.getProjectById(projectId);
      if (project == null) {
        return;
      }

      final files = await _projectRepository.getProjectFiles(projectId);
      final updateTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      recordProjectInfo(
        userId: _currentUserRepository.user?.effectiveId ?? "",
        projectId: projectId,
        projectAction: action,
        updatetime: updateTime,
        projectName: project.name,
        photoNum: files.length.toString(),
      );
    } catch (e) {
      PGLog.e('上报项目操作失败: $e');
    }
  }

  /// 上报导出进度点击事件
  void statExportProgress() {
    recordTopbarProgress(
        userId: _currentUserRepository.user?.effectiveId ?? '',
        clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
  }

  /// 清理项目状态
  void clearProjectState() {
    _projectStateProvider.exitEdit();
  }
}
