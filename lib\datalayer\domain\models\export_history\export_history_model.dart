import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/common/pagination_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_user_model.dart';

part 'export_history_model.freezed.dart';
part 'export_history_model.g.dart';

@freezed
class ExportHistoryModel with _$ExportHistoryModel {
  const factory ExportHistoryModel({
    required List<ExportHistoryItem> items,
    required PaginationModel pagination,
    required Map<String, ExportUserModel> users,
  }) = _ExportHistoryModel;

  factory ExportHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$ExportHistoryModelFromJson(json);
}

@freezed
class ExportHistoryItem with _$ExportHistoryItem {
  const factory ExportHistoryItem({
    // 主键
    required String id,
    // 名称
    required String name,
    // 门店ID
    required String storeId,
    // 用户ID
    required String userId,
    // 平台
    required String platform,
    // 创建时间
    required String createdAt,
    // 更新时间
    required String updatedAt,
    // 导出张数
    required int exportNumber,
    // 小样张数
    required int sampleNumber,
    // 计费张数
    required int chargeNumber,
    // 主机名
    required String hostname,
    // 设备ID
    required String deviceId,
    // 客户端项目ID
    required String clientProjectId,
  }) = _ExportHistoryItem;

  factory ExportHistoryItem.fromJson(Map<String, dynamic> json) =>
      _$ExportHistoryItemFromJson(json);
}
