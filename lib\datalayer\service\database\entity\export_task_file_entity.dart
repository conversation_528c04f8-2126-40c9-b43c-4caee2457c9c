import 'package:drift/drift.dart';

/// ExportTaskFileEntity - 导出任务文件实体
///
/// 对应Unity中的ExportTaskFileEntity，用于存储导出任务中的文件信息
class ExportTaskFileEntity extends Table {
  TextColumn get guid => text()(); // 主键
  TextColumn get exportTaskId => text()(); // 导出任务ID
  TextColumn get historyId => text().nullable()(); // 历史ID

  // LoadWorkFileParam json
  TextColumn get fileParam => text().nullable()();

  // ExportFileConfig json
  TextColumn get exportFileConfig => text().nullable()();

  TextColumn get errorMessage =>
      text().nullable().withDefault(const Constant(''))(); // 错误信息

  // ExportErrorType
  IntColumn get errorNum => integer().withDefault(const Constant(0))();

  TextColumn get finalPath => text().nullable()(); // 最终路径

  // ExportState
  IntColumn get exportState => integer().withDefault(const Constant(0))();

  // 添加主键
  @override
  Set<Column> get primaryKey => {guid};
}
