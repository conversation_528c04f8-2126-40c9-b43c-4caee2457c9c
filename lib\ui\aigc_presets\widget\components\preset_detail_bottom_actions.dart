import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/providers/aigc_preset_expand_provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_operation_buttons.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 底部操作区域组件
class PresetDetailBottomActions extends StatelessWidget {
  final VoidCallback? onExit;

  const PresetDetailBottomActions({super.key, this.onExit});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Selector<AIGCPresetDetailViewModel,
              ({int totalCount, int selectedCount})>(
            selector: (_, viewModel) => (
              totalCount: viewModel.totalCount,
              selectedCount: viewModel.selectedCount,
            ),
            builder: (context, counts, child) {
              return Text(
                  '${counts.totalCount}张创意效果，已选 ${counts.selectedCount} 张',
                  style: TextStyle(
                    color: const Color(0xFFFFFFFF),
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                  ));
            },
          ),

          const Spacer(),
          // 再次生成按钮 - 使用Selector监听相关状态
          Selector<
              AIGCPresetDetailViewModel,
              ({
                String? presetStatus,
              })>(
            selector: (_, viewModel) =>
                (presetStatus: viewModel.presetDetail?.status,),
            builder: (context, data, child) {
              return CustomButton(
                width: 100,
                height: 32,
                backgroundColor: const Color(0xFF333333),
                disabledBackgroundColor: const Color(0xFF404040),
                enabled: true,
                onTap: () {
                  // 切换再次生成展开状态
                  context.read<AigcPresetExpandProvider>().toggleMainExpanded();
                },
                child: Text(
                  '再次生成',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          // 确认按钮
          CustomButton(
            width: 100,
            height: 32,
            backgroundColor: const Color(0xFFF72561),
            child: Text(
              '完成',
              style: TextStyle(
                  color: const Color(0xFFFFFFFF),
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium),
            ),
            onTap: () async {
              await context
                  .read<AIGCPresetDetailViewModel>()
                  .confirmSelection();
              if (context.mounted) {
                onExit?.call();
              }
            },
          ),
        ],
      ),
    );
  }
}
