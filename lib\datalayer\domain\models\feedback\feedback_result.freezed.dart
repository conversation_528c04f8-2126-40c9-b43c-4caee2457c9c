// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feedback_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FeedbackResult _$FeedbackResultFromJson(Map<String, dynamic> json) {
  return _FeedbackResult.fromJson(json);
}

/// @nodoc
mixin _$FeedbackResult {
  String get id => throw _privateConstructorUsedError;
  String get ticketId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FeedbackResultCopyWith<FeedbackResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedbackResultCopyWith<$Res> {
  factory $FeedbackResultCopyWith(
          FeedbackResult value, $Res Function(FeedbackResult) then) =
      _$FeedbackResultCopyWithImpl<$Res, FeedbackResult>;
  @useResult
  $Res call({String id, String ticketId});
}

/// @nodoc
class _$FeedbackResultCopyWithImpl<$Res, $Val extends FeedbackResult>
    implements $FeedbackResultCopyWith<$Res> {
  _$FeedbackResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? ticketId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      ticketId: null == ticketId
          ? _value.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FeedbackResultImplCopyWith<$Res>
    implements $FeedbackResultCopyWith<$Res> {
  factory _$$FeedbackResultImplCopyWith(_$FeedbackResultImpl value,
          $Res Function(_$FeedbackResultImpl) then) =
      __$$FeedbackResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String ticketId});
}

/// @nodoc
class __$$FeedbackResultImplCopyWithImpl<$Res>
    extends _$FeedbackResultCopyWithImpl<$Res, _$FeedbackResultImpl>
    implements _$$FeedbackResultImplCopyWith<$Res> {
  __$$FeedbackResultImplCopyWithImpl(
      _$FeedbackResultImpl _value, $Res Function(_$FeedbackResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? ticketId = null,
  }) {
    return _then(_$FeedbackResultImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      ticketId: null == ticketId
          ? _value.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedbackResultImpl implements _FeedbackResult {
  const _$FeedbackResultImpl({required this.id, required this.ticketId});

  factory _$FeedbackResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedbackResultImplFromJson(json);

  @override
  final String id;
  @override
  final String ticketId;

  @override
  String toString() {
    return 'FeedbackResult(id: $id, ticketId: $ticketId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedbackResultImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, ticketId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedbackResultImplCopyWith<_$FeedbackResultImpl> get copyWith =>
      __$$FeedbackResultImplCopyWithImpl<_$FeedbackResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedbackResultImplToJson(
      this,
    );
  }
}

abstract class _FeedbackResult implements FeedbackResult {
  const factory _FeedbackResult(
      {required final String id,
      required final String ticketId}) = _$FeedbackResultImpl;

  factory _FeedbackResult.fromJson(Map<String, dynamic> json) =
      _$FeedbackResultImpl.fromJson;

  @override
  String get id;
  @override
  String get ticketId;
  @override
  @JsonKey(ignore: true)
  _$$FeedbackResultImplCopyWith<_$FeedbackResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
