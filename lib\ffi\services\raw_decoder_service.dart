// Raw图像解码器的Dart封装API
// 提供更友好的Flutter接口

import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/models/raw_decoder_model.dart';
import 'package:turing_art/ffi/native/raw_decoder_bindings.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Raw解码器选项
class RawDecoderConfig {
  final int bitDepth;
  final int maxDim;
  final bool thumbnail;
  final bool denoise;
  final bool clarity;
  final double exposure;
  final String outputFormat;

  const RawDecoderConfig({
    this.bitDepth = 8,
    this.maxDim = 0,
    this.thumbnail = false,
    this.denoise = false,
    this.clarity = false,
    this.exposure = 0.0,
    this.outputFormat = 'jpg',
  });

  /// 获取默认配置
  static const RawDecoderConfig defaultConfig = RawDecoderConfig();

  /// 转换为原生结构体
  Pointer<RawDecoderOptions> toNative() {
    final options = malloc<RawDecoderOptions>();
    options.ref.bitDepth = bitDepth;
    options.ref.maxDim = maxDim;
    options.ref.thumbnail = thumbnail ? 1 : 0;
    options.ref.denoise = denoise ? 1 : 0;
    options.ref.clarity = clarity ? 1 : 0;
    options.ref.exposure = exposure;
    options.ref.outputFormat = outputFormat.toNativeUtf8();
    return options;
  }

  /// 释放原生结构体内存
  static void freeNative(Pointer<RawDecoderOptions> options) {
    if (options != nullptr) {
      if (options.ref.outputFormat != nullptr) {
        malloc.free(options.ref.outputFormat);
      }
      malloc.free(options);
    }
  }
}

/// Raw解码器异常
class RawDecoderException implements Exception {
  final int errorCode;
  final String message;

  const RawDecoderException(this.errorCode, this.message);

  @override
  String toString() => 'RawDecoderException($errorCode): $message';

  /// 根据错误码获取错误描述
  static String getErrorMessage(int errorCode) {
    switch (errorCode) {
      case RawDecoderErrorCode.success:
        return '成功';
      case RawDecoderErrorCode.errorInitFailed:
        return '初始化失败';
      case RawDecoderErrorCode.errorNotInitialized:
        return '解码器未初始化';
      case RawDecoderErrorCode.errorInvalidParam:
        return '参数无效';
      case RawDecoderErrorCode.errorFileNotFound:
        return '文件未找到';
      case RawDecoderErrorCode.errorDecodeFailed:
        return '解码失败';
      case RawDecoderErrorCode.errorSaveFailed:
        return '保存失败';
      case RawDecoderErrorCode.errorUnknown:
        return '未知错误';
      default:
        return '未知错误代码: $errorCode';
    }
  }
}

/// Raw图像解码器
class RawDecoderService {
  static bool _initialized = false;

  /// 初始化解码器
  ///
  /// [numThreads] 线程数，0表示自动检测
  /// [bundlePath] Bundle文件路径，null表示使用默认路径
  static Future<void> initialize({
    int numThreads = 0,
    String? bundlePath,
    String? key,
  }) async {
    if (_initialized) {
      return;
    }

    // 如果没有提供bundlePath，尝试自动获取
    bundlePath ??= UniversalPlatformLoader.getRawDecoderBundlePath();

    final result = RawDecoderBindings.init(numThreads, bundlePath, key);
    if (result != RawDecoderErrorCode.success) {
      throw RawDecoderException(
        result,
        RawDecoderException.getErrorMessage(result),
      );
    }

    _initialized = true;
  }

  /// 检查是否已初始化
  static bool get isInitialized => RawDecoderBindings.isInitialized();

  /// 处理单个RAW文件
  ///
  /// [inputFile] 输入RAW文件路径
  /// [outputFile] 输出图像文件路径
  /// [config] 解码配置，null表示使用默认配置
  static Future<void> processFile(
    String inputFile,
    String outputFile, {
    RawDecoderConfig? config,
  }) async {
    if (!isInitialized) {
      throw const RawDecoderException(
        RawDecoderErrorCode.errorNotInitialized,
        '解码器未初始化，请先调用initialize()',
      );
    }

    // 检查输入文件是否存在
    if (!File(inputFile).existsSync()) {
      throw RawDecoderException(
        RawDecoderErrorCode.errorFileNotFound,
        '输入文件不存在: $inputFile',
      );
    }

    Pointer<RawDecoderOptions>? options;
    try {
      if (config != null) {
        options = config.toNative();
      }

      final result = RawDecoderBindings.process(
        inputFile,
        outputFile,
        options,
        1024, // 错误消息缓冲区大小
      );

      if (result != RawDecoderErrorCode.success) {
        throw RawDecoderException(
          result,
          RawDecoderException.getErrorMessage(result),
        );
      }
    } catch (e) {
      PGLog.e('RawDecoderService processFile error: $e');
    } finally {
      if (options != null) {
        RawDecoderConfig.freeNative(options);
      }
    }
  }

  /// 批量处理RAW文件
  ///
  /// [inputFiles] 输入RAW文件路径列表
  /// [outputDir] 输出目录
  /// [config] 解码配置
  /// [onProgress] 进度回调 (当前索引, 总数)
  static Future<List<String>> processBatch(
    List<String> inputFiles,
    String outputDir, {
    RawDecoderConfig? config,
    Function(int current, int total)? onProgress,
  }) async {
    final results = <String>[];

    // 确保输出目录存在
    final outputDirectory = Directory(outputDir);
    if (!outputDirectory.existsSync()) {
      outputDirectory.createSync(recursive: true);
    }

    for (int i = 0; i < inputFiles.length; i++) {
      final inputFile = inputFiles[i];
      final fileName = File(inputFile).uri.pathSegments.last;
      final nameWithoutExt = fileName.split('.').first;
      final outputFormat = config?.outputFormat ?? 'jpg';
      final outputFile = '$outputDir/$nameWithoutExt.$outputFormat';

      try {
        await processFile(inputFile, outputFile, config: config);
        results.add(outputFile);
      } catch (e) {
        PGLog.d('处理文件失败 $inputFile: $e');
        // 继续处理下一个文件，但记录错误
      }

      onProgress?.call(i + 1, inputFiles.length);
    }

    return results;
  }

  /// 获取默认解码选项
  static RawDecoderConfig getDefaultConfig() {
    final options = malloc<RawDecoderOptions>();
    try {
      RawDecoderBindings.getDefaultOptions(options);

      return RawDecoderConfig(
        bitDepth: options.ref.bitDepth,
        maxDim: options.ref.maxDim,
        thumbnail: options.ref.thumbnail == 1,
        denoise: options.ref.denoise == 1,
        clarity: options.ref.clarity == 1,
        exposure: options.ref.exposure,
        outputFormat: options.ref.outputFormat.toDartString(),
      );
    } finally {
      malloc.free(options);
    }
  }

  /// 释放解码器资源
  static void dispose() {
    if (_initialized) {
      RawDecoderBindings.free();
      _initialized = false;
    }
  }
}
