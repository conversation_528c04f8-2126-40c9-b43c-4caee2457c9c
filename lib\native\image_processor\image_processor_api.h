#pragma once

#ifdef _WIN32
    #ifdef IMAGE_PROCESSOR_EXPORTS
        #define IMAGE_PROCESSOR_API __declspec(dllexport)
    #else
        #define IMAGE_PROCESSOR_API __declspec(dllimport)
    #endif
#else
    #define IMAGE_PROCESSOR_API __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 错误码定义
typedef enum {
    IMAGE_PROCESSOR_SUCCESS = 0,
    IMAGE_PROCESSOR_ERROR_INIT_FAILED = -1,
    IMAGE_PROCESSOR_ERROR_NOT_INITIALIZED = -2,
    IMAGE_PROCESSOR_ERROR_INVALID_PARAM = -3,
    IMAGE_PROCESSOR_ERROR_FILE_NOT_FOUND = -4,
    IMAGE_PROCESSOR_ERROR_UNSUPPORTED_FORMAT = -5,
    IMAGE_PROCESSOR_ERROR_DECODE_FAILED = -6,
    IMAGE_PROCESSOR_ERROR_ENCODE_FAILED = -7,
    IMAGE_PROCESSOR_ERROR_RESIZE_FAILED = -8,
    IMAGE_PROCESSOR_ERROR_UNKNOWN = -99
} ImageProcessorErrorCode;

// 图像处理选项结构体
typedef struct {
    int target_width;           // 目标宽度，0表示保持原尺寸
    int target_height;          // 目标高度，0表示保持原尺寸
    int maintain_aspect;        // 是否保持宽高比，0=否，1=是
    int quality;               // JPEG质量 (1-100)，对PNG无效
    const char* output_format; // 输出格式："jpg", "jpeg", "png"
    int interpolation;         // 插值方法：0=最近邻，1=双线性，2=双三次, 3=区域插值（适合缩小）, 4=Lanczos插值（高质量）
    int rotation;              // 旋转角度 (0, 90, 180, 270)
    int fix_rotation;          // 是否根据EXIF自动修正方向，0=否，1=是
    int use_pillow_resize;     // 是否使用PillowResize重采样，0=否，1=是
} ImageProcessorOptions;

// 用于返回内存中的数据块
typedef struct {
    unsigned char* data; // 数据指针
    long size;           // 数据大小
} ImageData;

/**
 * 初始化图像处理器
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_Init();

/**
 * 解码图像文件（支持尺寸调整）
 * @param input_file 输入图像文件路径
 * @param output_file 输出图像文件路径
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_Decode(const char* input_file, 
                                             const char* output_file,
                                             const ImageProcessorOptions* options,
                                             char* error_message,
                                             int error_message_size);

/**
 * 编码图像文件
 * @param input_file 输入图像文件路径
 * @param output_file 输出图像文件路径
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_Encode(const char* input_file,
                                             const char* output_file, 
                                             const ImageProcessorOptions* options,
                                             char* error_message,
                                             int error_message_size);

/**
 * 批量处理图像文件
 * @param input_folder 输入文件夹路径
 * @param output_folder 输出文件夹路径
 * @param options 处理选项，可以为NULL使用默认选项
 * @param recursive 是否递归处理子文件夹，0=否，1=是
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return 成功处理的文件数量，-1表示错误
 */
IMAGE_PROCESSOR_API int ImageProcessor_BatchProcess(const char* input_folder,
                                                   const char* output_folder,
                                                   const ImageProcessorOptions* options,
                                                   int recursive,
                                                   char* error_message,
                                                   int error_message_size);

/**
 * 释放图像处理器资源
 */
IMAGE_PROCESSOR_API void ImageProcessor_Free();

/**
 * 获取默认处理选项
 * @param options 输出的默认选项
 */
IMAGE_PROCESSOR_API void ImageProcessor_GetDefaultOptions(ImageProcessorOptions* options);

/**
 * 检查是否已初始化
 * @return 1表示已初始化，0表示未初始化
 */
IMAGE_PROCESSOR_API int ImageProcessor_IsInitialized();

/**
 * 获取图像信息
 * @param file_path 图像文件路径
 * @param width 输出图像宽度
 * @param height 输出图像高度
 * @param channels 输出图像通道数
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_GetImageInfo(const char* file_path,
                                                   int* width,
                                                   int* height,
                                                   int* channels);

/**
 * 将图像处理到内存中
 * @param input_file 输入图像文件路径
 * @param options 处理选项，可以为NULL使用默认选项
 * @param processed_image_data 输出的处理后图像数据（调用方需要使用ImageProcessor_FreeImageData释放）
 * @param exif_data 输出的EXIF数据（调用方需要使用ImageProcessor_FreeImageData释放）
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_ProcessToMemory(const char *input_file,
                                                       const ImageProcessorOptions *options,
                                                       ImageData *processed_image_data,
                                                       char *temp_exif_path,
                                                       int temp_exif_path_size);

/**
 * 将一个文件中的EXIF数据写入另一个文件
 * @param source_exif_file 包含源EXIF数据的图像文件路径
 * @param target_file 要写入EXIF的目标图像文件路径
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_CopyExifToFile(const char *source_exif_file,
                                                      const char *target_file);

/**
 * 释放由API分配的ImageData内存
 * @param image_data 要释放的ImageData结构体指针
 */
IMAGE_PROCESSOR_API void ImageProcessor_FreeImageData(ImageData *image_data);

/**
 * 从JPEG图像的EXIF数据中提取缩略图并调整尺寸
 * @param input_file 输入JPEG图像文件路径
 * @param max_size 缩略图目标最大边长尺寸（像素）
 * @param output_file 缩略图保存文件路径
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return IMAGE_PROCESSOR_SUCCESS表示成功，其他值表示错误码
 */
IMAGE_PROCESSOR_API int ImageProcessor_ExtractExifThumbnail(const char *input_file,
                                                            int max_size,
                                                            const char *output_file,
                                                            char *error_message,
                                                            int error_message_size);

#ifdef __cplusplus
}
#endif 