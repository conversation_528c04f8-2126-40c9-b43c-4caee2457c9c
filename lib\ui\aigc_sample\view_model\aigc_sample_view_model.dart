import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/providers/aigc_sample_export_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_list_polling_provider.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 加载状态枚举，用于替代简单的布尔标志
enum LoadState {
  initial, // 初始状态，还未开始加载
  loading, // 正在加载中
  loaded, // 已加载完成
  error, // 加载错误
}

class AigcSampleViewModel extends ChangeNotifier {
  // 预设仓库
  final AigcSampleRepository _repository;
  final AccountRepository _accountRepository;

  // 轮询Provider
  final AigcSampleListPollingProvider _pollingProvider;

  // 导出轮询Provider
  final AigcSampleExportPollingProvider _exportPollingProvider;

  // 导出管理器
  final AigcMySampleExportManager _exportManager;

  // 静态变量保存历史已完成样本ID，避免页面销毁导致状态丢失
  static final Set<String> _historicalCompletedSampleIds = <String>{};
  // 静态变量保存是否已经加载过样本数据
  static bool _hadLoadSamples = false;

  // 当前选中的tab
  int _selectedTab = 0;
  int get selectedTab => _selectedTab;

  // Stream订阅
  StreamSubscription<String>? _dataChangeSubscription;
  StreamSubscription<RetryExportRefreshEvent>? _retryRefreshSubscription;

  AigcSampleViewModel({
    required AigcSampleRepository repository,
    required AccountRepository accountRepository,
    required AigcSampleListPollingProvider pollingProvider,
    required AigcSampleExportPollingProvider exportPollingProvider,
    required AigcMySampleExportManager exportManager,
  })  : _repository = repository,
        _accountRepository = accountRepository,
        _pollingProvider = pollingProvider,
        _exportPollingProvider = exportPollingProvider,
        _exportManager = exportManager {
    _initializeDataStream();
    _initializeRetryRefreshStream();
    loadLocalProjectList();
  }

  List<AigcSampleModel> _aigcSampleList = [];
  List<AigcSampleModel> get aigcSampleList => _aigcSampleList;

  // 已完成数量
  final Map<String, List<AigcSampleModel>> _completedListMap = {};

  // 正在处理数量
  final Map<String, List<AigcSampleModel>> _processingListMap = {};

  // 已完成的当前选中的项目名称
  int _selectedProjectIndex = -1;
  int get selectedProjectIndex => _selectedProjectIndex;

  // 动态获取当前选中的项目ID（根据当前tab）
  String get projectId {
    return _selectedTab == 0
        ? _selectedCompletedProjectId
        : _selectedProcessingProjectId;
  }

  // 是否有新的已完成项目
  List<AigcSampleModel> _newCompletedSampleList = [];
  List<AigcSampleModel> get newCompletedSampleList => _newCompletedSampleList;

  // 正在处理项目
  final List<AigcSampleModel> _runningSampleList = [];
  List<AigcSampleModel> get runningSampleList => _runningSampleList;

  // 本地项目列表
  List<AigcSampleProjectModel> _localProjectList = [];
  List<AigcSampleProjectModel> get localProjectList => _localProjectList;

  // 当前tab对应的项目列表（根据tab筛选后的项目）
  List<AigcSampleProjectModel> _currentTabProjectList = [];
  List<AigcSampleProjectModel> get currentTabProjectList =>
      _currentTabProjectList;

  // 记录用户在不同tab下手动选择的项目ID
  String _selectedCompletedProjectId = '';
  String _selectedProcessingProjectId = '';

  // 加载状态，用于判断是否是第一次加载、正在加载、加载完成、加载失败
  LoadState _loadState = LoadState.initial;
  LoadState get loadState => _loadState;

  // 便捷的状态检查方法
  bool get isInitial => _loadState == LoadState.initial;
  bool get isDataLoading => _loadState == LoadState.loading;
  bool get isLoaded => _loadState == LoadState.loaded;
  bool get hasError => _loadState == LoadState.error;

  // 是否已经完成过首次初始化（专门用于判断是否需要执行初始化逻辑）
  bool _hasCompletedInitialLoad = false;

  /// 初始化数据流监听
  void _initializeDataStream() {
    _dataChangeSubscription = _repository.dataChangeStream.listen((eventType) {
      final isExportStatusRefresh = eventType
          .startsWith(AigcRequestConst.proofingListExportStatusRefresh);
      final isProofingStatusRefresh =
          eventType.startsWith(AigcRequestConst.listProofingStatusRefresh);

      if (isProofingStatusRefresh || isExportStatusRefresh) {
        PGLog.d('收到列表打样状态刷新事件');

        // 解析事件中的projectId
        // String? eventProjectId;
        // final parts = eventType.split(':');
        // if (parts.length > 1) {
        //   eventProjectId = parts[1];
        // }
        // 1.获取最新打样状态列表并判断是否需要刷新
        final isChange = isProofingStatusRefresh
            ? _checkListProofingStatusChange()
            : _checkListExportStatusChange();
        if (!isChange) {
          PGLog.d('列表${isProofingStatusRefresh ? '打样' : '导出'}状态未变更，不刷新');
          return;
        }
        // 2.变更后先停止轮询，获取最新数据后会重新检查是否需要轮询
        _pollingProvider.stopPolling();
        // 3.更新列表状态数据并检查是否还需要轮询
        PGLog.d(
            '更新当前项目列表${isProofingStatusRefresh ? '打样' : '导出'}状态，当前项目ID: $projectId');
        _refreshSampleList();
      }
    });
  }

  // 获取本地项目列表
  Future<void> loadLocalProjectList() async {
    final projects = await _repository.getAigcSampleProjectList();
    // 按照更新时间排序
    final sortedProjects = projects.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    if (sortedProjects.isNotEmpty) {
      _localProjectList = sortedProjects;
      loadSampleList(refresh: true);
    }
  }

  Future<void> _refreshSampleList() async {
    try {
      _loadState = LoadState.loading;
      // 1.获取打样列表
      final samples = await _repository.getAigcSampleList('');
      // 按照更新时间排序
      final sortedSamples = samples.toList()
        ..sort((a, b) => b.updateAt.compareTo(a.updateAt));
      // 2.更新本地数据
      _processAndUpdateSampleData(sortedSamples);
      // 3.检查是否需要开始或停止轮询
      _checkPollingStatus();
      // 4.检查是否需要开始或停止导出轮询
      _checkExportPollingStatus();
      // 5.通知监听器 - 已经在_processAndUpdateSampleData中调用了notifyListeners()，这里不需要重复调用
      // notifyListeners();
      PGLog.d('刷新打样列表完成，当前选中Tab: $_selectedTab');
    } catch (e) {
      PGLog.e("加载打样列表失败: $e");
      _clearSampleData(projectId);
      _loadState = LoadState.error;
      notifyListeners(); // 错误情况下需要通知UI更新
    }
  }

  // 根据项目获取打样列表
  Future<void> loadSampleList({bool refresh = false}) async {
    if (refresh) {
      _refreshSampleList();
      return;
    }

    if (!refresh &&
            (_selectedTab == 0 && _completedListMap[projectId] != null) ||
        (_selectedTab == 1 && _processingListMap[projectId] != null)) {
      // 根据选中的项目名称，获取对应的打样列表
      _updateCurrentDisplayList(projectId);
      // 检查是否需要开始或停止轮询
      _checkPollingStatus();
      notifyListeners();
      return;
    }

    // 如果缓存中没有数据，尝试从Repository获取（可能从缓存或本地获取）
    _refreshSampleList();
  }

  /// 处理和更新样本数据的通用方法
  void _processAndUpdateSampleData(List<AigcSampleModel> samples) {
    try {
      // 1. 数据分组和分类（一次遍历完成所有分类）
      _classifyAndGroupSamples(samples);

      // 2. 过滤和更新项目列表
      _filterProjectList();
      _updateCurrentTabProjectList();

      // 3. 处理历史记录和新增项目
      _updateHistoricalRecords(samples);

      // 4. 处理项目选择逻辑
      _handleProjectSelection();

      // 5. 更新UI状态
      _updateUIState();

      PGLog.d(
          '处理和更新样本数据完成，当前选中Tab: $_selectedTab，正在处理数量: ${_runningSampleList.length}');
    } catch (e) {
      PGLog.e('处理和更新样本数据时发生异常: $e');
      _loadState = LoadState.error;
      notifyListeners();
    }
  }

  /// 分类和分组样本数据（优化：一次遍历完成所有操作）
  void _classifyAndGroupSamples(List<AigcSampleModel> samples) {
    // 清空之前的数据
    _runningSampleList.clear();
    _completedListMap.clear();
    _processingListMap.clear();

    // 初始化项目映射
    for (final project in _localProjectList) {
      _completedListMap[project.projectId] = <AigcSampleModel>[];
      _processingListMap[project.projectId] = <AigcSampleModel>[];
    }

    // 一次遍历完成所有分类和分组
    for (final sample in samples) {
      final isRunning = sample.status == AigcRequestConst.running;

      // 全局分类
      if (isRunning) {
        _runningSampleList.add(sample);
      }

      // 按项目分组
      final projectId = sample.projectId;
      if (isRunning) {
        _processingListMap[projectId]?.add(sample);
      } else {
        _completedListMap[projectId]?.add(sample);
      }
    }
  }

  /// 更新历史记录和计算新增项目
  void _updateHistoricalRecords(List<AigcSampleModel> samples) {
    final completedSamples =
        samples.where((e) => e.status != AigcRequestConst.running).toList();

    // 首次加载时初始化历史记录
    if (!_hadLoadSamples) {
      _hadLoadSamples = true;
      _historicalCompletedSampleIds.addAll(completedSamples.map((e) => e.id));
      _newCompletedSampleList = []; // 首次加载没有新增项目
      PGLog.d(
          '首次加载，将所有已完成项目添加到历史记录，总数: ${_historicalCompletedSampleIds.length}');
      return;
    }

    // 计算新增的已完成项目（从第二次请求开始才会有新增项目）
    _newCompletedSampleList = completedSamples
        .where((sample) => !_historicalCompletedSampleIds.contains(sample.id))
        .toList();

    for (final sample in completedSamples) {
      // 如果新增的已完成项目中不包含这个id，则添加到历史记录
      if (!_newCompletedSampleList.any((e) => e.id == sample.id)) {
        _historicalCompletedSampleIds.add(sample.id);
      }
    }

    PGLog.d('新增已完成项目数量: ${_newCompletedSampleList.length}');
    PGLog.d('历史已完成项目总数: ${_historicalCompletedSampleIds.length}');

    if (_newCompletedSampleList.isNotEmpty) {
      PGLog.d(
          '新增已完成项目ID: ${_newCompletedSampleList.map((e) => e.id).join(', ')}');
    }
  }

  /// 处理项目选择逻辑
  void _handleProjectSelection() {
    // 只有在首次初始化时才执行特殊逻辑
    if (!_hasCompletedInitialLoad) {
      _handleInitialProjectSelection();
      _hasCompletedInitialLoad = true;
      PGLog.d('标记首次初始化完成');
    } else {
      // 非首次加载时，智能选择项目
      _selectAppropriateProject();
    }
  }

  /// 处理首次项目选择逻辑
  void _handleInitialProjectSelection() {
    PGLog.d('执行首次初始化逻辑');

    if (_runningSampleList.isNotEmpty) {
      // 有正在处理的项目，切换到正在处理tab
      _selectedTab = 1;
      _updateCurrentTabProjectList(); // 确保tab列表是最新的

      final firstRunningSampleProjectId = _findFirstRunningSampleProjectId();
      if (firstRunningSampleProjectId != null) {
        _selectedProcessingProjectId = firstRunningSampleProjectId;
        _selectedProjectIndex = _currentTabProjectList
            .indexWhere((e) => e.projectId == firstRunningSampleProjectId);
      }
      PGLog.d('首次加载：有正在处理的打样，切换到正在处理Tab');
    } else {
      // 没有正在处理的打样，确保在已完成tab
      _selectedTab = 0;
      _updateCurrentTabProjectList(); // 确保tab列表是最新的

      if (_currentTabProjectList.isNotEmpty) {
        _selectedCompletedProjectId = _currentTabProjectList.first.projectId;
        _selectedProjectIndex = 0;
        PGLog.d(
            '首次加载：选中第一个已完成项目: ${_currentTabProjectList.first.projectName}, ID: $projectId');
      } else {
        PGLog.w('首次加载：没有找到任何已完成的项目');
      }
      PGLog.d('首次加载：没有正在处理的打样，切换到已完成Tab');
    }
  }

  /// 更新UI状态
  void _updateUIState() {
    // 标记为已加载完成
    _loadState = LoadState.loaded;
    PGLog.d('设置加载状态为已完成');

    // 更新当前显示的列表
    _updateCurrentDisplayList(projectId);
    PGLog.d('更新当前显示列表完成，当前项目ID: $projectId，列表项数量: ${_aigcSampleList.length}');

    // 通知UI更新
    PGLog.d('准备通知UI更新');
    notifyListeners();
  }

  // 过滤掉没有任何打样数据的项目（既没有已完成也没有正在处理的）
  void _filterProjectList() {
    try {
      PGLog.d('开始过滤项目，当前项目数量: ${_localProjectList.length}');

      final validProjects = <AigcSampleProjectModel>[];

      for (final project in _localProjectList) {
        try {
          final projectId = project.projectId;
          final completedList = _completedListMap[projectId] ?? [];
          final processingList = _processingListMap[projectId] ?? [];

          // 如果项目有任何数据（已完成或正在处理），则保留
          if (completedList.isNotEmpty || processingList.isNotEmpty) {
            validProjects.add(project);
            PGLog.d(
                '保留项目: ${project.projectName} (已完成: ${completedList.length}, 处理中: ${processingList.length})');
          } else {
            PGLog.d('过滤掉项目: ${project.projectName} (无数据)');
          }
        } catch (e) {
          PGLog.e('处理项目时出错: $e, 项目: ${project.toString()}');
          // 出错时保留项目，避免意外删除
          validProjects.add(project);
        }
      }

      _localProjectList = validProjects;
      PGLog.d('过滤完成，剩余项目数量: ${_localProjectList.length}');
    } catch (e) {
      PGLog.e('过滤项目时发生异常: $e');
      // 如果过滤过程出错，保持原有项目列表不变
    }
  }

  /// 更新当前tab对应的项目列表
  void _updateCurrentTabProjectList() {
    if (_selectedTab == 0) {
      // 已完成tab：只显示有已完成打样的项目
      _currentTabProjectList = _localProjectList.where((project) {
        final completedList = _completedListMap[project.projectId] ?? [];
        return completedList.isNotEmpty;
      }).toList();
      PGLog.d('已完成tab项目列表更新，项目数量: ${_currentTabProjectList.length}');
    } else {
      // 进行中tab：只显示有正在进行打样的项目
      _currentTabProjectList = _localProjectList.where((project) {
        final processingList = _processingListMap[project.projectId] ?? [];
        return processingList.isNotEmpty;
      }).toList();
      PGLog.d('进行中tab项目列表更新，项目数量: ${_currentTabProjectList.length}');
    }
  }

  /// 智能选择合适的项目
  void _selectAppropriateProject() {
    if (_currentTabProjectList.isEmpty) {
      _selectedProjectIndex = -1;
      // 清空当前tab对应的选中项目ID
      if (_selectedTab == 0) {
        _selectedCompletedProjectId = '';
      } else {
        _selectedProcessingProjectId = '';
      }
      PGLog.d('当前tab没有可用项目，重置选择');
      return;
    }

    // 获取当前tab下用户之前选择的项目ID
    final previousSelectedProjectId = _selectedTab == 0
        ? _selectedCompletedProjectId
        : _selectedProcessingProjectId;

    // 检查之前选择的项目是否在当前tab的项目列表中
    final previousProjectIndex = _currentTabProjectList.indexWhere(
        (project) => project.projectId == previousSelectedProjectId);

    if (previousProjectIndex != -1) {
      // 之前选择的项目仍然可用，继续选择它
      _selectedProjectIndex = previousProjectIndex;
      PGLog.d('继续选择之前的项目: $previousSelectedProjectId');
    } else {
      // 之前选择的项目不可用，选择第一个项目
      _selectedProjectIndex = 0;
      final newProjectId = _currentTabProjectList.first.projectId;

      // 更新对应tab的选择记录
      if (_selectedTab == 0) {
        _selectedCompletedProjectId = newProjectId;
      } else {
        _selectedProcessingProjectId = newProjectId;
      }

      PGLog.d('选择第一个可用项目: $newProjectId');
    }
  }

  /// 清理样本数据的通用方法
  void _clearSampleData(String projectId) {
    _aigcSampleList = [];
    _completedListMap[projectId] = [];
    _processingListMap[projectId] = [];
    _selectedProjectIndex = -1;
  }

  /// 更新当前显示的列表,根据项目id更新当前显示的列表
  void _updateCurrentDisplayList([String? projectId]) {
    final targetProjectId = projectId ?? this.projectId;

    if (_selectedTab == 0) {
      _aigcSampleList = _completedListMap[targetProjectId] ?? [];
    } else {
      _aigcSampleList = _processingListMap[targetProjectId] ?? [];
    }
  }

  /// 检查轮询状态
  void _checkPollingStatus() {
    final processingList = _processingListMap.values.expand((e) => e).toList();

    if (processingList.isNotEmpty) {
      // 有正在处理的项目，开始轮询
      final processingIds = processingList.map((e) => e.id).toList();
      _pollingProvider.startPolling(processingIds, projectId);
      PGLog.d('开始轮询，正在处理的数量: ${processingList.length}');
    } else {
      // 没有正在处理的项目，停止可能已有的轮询
      _pollingProvider.stopPolling();
      PGLog.d('停止轮询，没有正在处理的项目');
    }
  }

  // 检查列表打样状态是否变更
  bool _checkListProofingStatusChange() {
    final statusList =
        _repository.getLocalAigcSampleProofingStatusList(projectId);
    if (statusList.isEmpty) {
      return true;
    }

    bool hasChanged = false;
    bool hasFailure = false;

    for (final status in statusList) {
      // 只要有一个状态不是在处理，就说明数据发生了变更
      if (status.status != AigcRequestConst.running) {
        hasChanged = true;
        // 检查是否有失败状态
        if (status.status == AigcRequestConst.failed) {
          hasFailure = true;
        }
      }
    }

    // 如果有失败状态，刷新账户信息，需要退钱
    if (hasFailure) {
      _accountRepository.refreshAllAccount().catchError((e) {
        PGLog.e('刷新账户信息失败: $e');
      });
    }

    return hasChanged;
  }

  // 检查列表导出状态是否变更
  bool _checkListExportStatusChange() {
    final exportStatusList =
        _repository.getLocalAigcSampleExportStatusList(projectId);
    if (exportStatusList.isEmpty) {
      return true;
    }
    PGLog.d('检查列表导出状态是否变更，当前项目ID: $projectId，导出状态列表: $exportStatusList');

    bool hasFailure = false;

    // 获取服务器最新结果，转化成工程和是否完成的map
    final Map<String, bool> projectExportCompletedMap = {};
    for (final exportStatus in exportStatusList) {
      final projectId = exportStatus.id;
      if (!projectExportCompletedMap.containsKey(projectId)) {
        // 还没有过先默认设置为已完成
        projectExportCompletedMap[projectId] = true;
      }
      // 只要有一个效果是running状态，就说明这个工程的导出未完成(导出成功和失败都是完成)
      if (exportStatus.status == AigcRequestConst.running) {
        projectExportCompletedMap[projectId] = false;
      }
      // 检查是否有失败状态
      if (exportStatus.status == AigcRequestConst.failed) {
        hasFailure = true;
      }
    }

    // 如果有失败状态，刷新账户信息，需要退钱
    if (hasFailure) {
      _accountRepository.refreshAllAccount().catchError((e) {
        PGLog.e('刷新账户信息失败: $e');
      });
    }

    // 只要有一个工程的所有效果都是导出完成状态，就说明发生了变更，需要刷新列表
    for (final isCompleted in projectExportCompletedMap.values) {
      if (isCompleted) {
        PGLog.d('检查列表导出状态是否变更，有工程导出是完成状态，需要刷新列表');
        return true;
      }
    }

    PGLog.d('检查列表导出状态是否变更，没有工程导出是完成状态，不需要刷新列表');
    return false;
  }

  /// 检查导出轮询状态
  void _checkExportPollingStatus() {
    final completedList = _completedListMap[projectId] ?? [];

    // 在_completedListMap里面去查找哪些打样效果在导出，则记录下打样id和对应的效果id
    final List<AigcSampleListExportRequest> exportingRequests = [];

    for (final sample in completedList) {
      for (final effect in sample.effects) {
        // 如果效果图的导出状态是running，则需要轮询
        if (effect.exportStatus == AigcRequestConst.running) {
          exportingRequests.add(AigcSampleListExportRequest(
            proofingId: sample.id,
            effectCode: effect.effectCode,
          ));
        }
      }
    }

    if (exportingRequests.isNotEmpty) {
      // 有需要轮询的导出任务，开始轮询
      _exportPollingProvider.startPolling(exportingRequests, projectId);
      PGLog.d('开始轮询导出状态，正在导出的数量: ${exportingRequests.length}，项目ID: $projectId');
    } else {
      // 没有正在导出的任务，停止可能已有的轮询
      _exportPollingProvider.stopPolling();
      PGLog.d('停止导出轮询，没有正在导出的任务');
    }
  }

  // 设置选中的tab
  void setSelectedTab(int index) {
    if (_selectedTab != index) {
      _selectedTab = index;

      // 更新当前tab对应的项目列表
      _updateCurrentTabProjectList();

      // 智能选择项目
      _selectAppropriateProject();

      // 更新显示列表
      _updateCurrentDisplayList(projectId);

      PGLog.d('设置选中Tab: $index，更新显示列表');
      notifyListeners();
    }
  }

  // 设置选中项目
  void setSelectedProjectIndex(int index) {
    if (index < 0 || index >= _currentTabProjectList.length) {
      PGLog.w('无效的项目索引: $index，当前tab项目列表长度: ${_currentTabProjectList.length}');
      return;
    }

    _selectedProjectIndex = index;
    final newProjectId =
        _currentTabProjectList[_selectedProjectIndex].projectId;

    // 记录用户在当前tab下的选择
    if (_selectedTab == 0) {
      _selectedCompletedProjectId = newProjectId;
    } else {
      _selectedProcessingProjectId = newProjectId;
    }

    loadSampleList(refresh: false);
    PGLog.d('用户选择项目: $projectId，tab: $_selectedTab');
  }

  // 删除打样
  Future<void> deleteAigcSample(String id) async {
    await _repository.deleteAigcSample(id);
    loadSampleList(refresh: true);
  }

  // 删除新增的已完成样本
  void deleteNewCompletedSample(AigcSampleModel sample) {
    _newCompletedSampleList.removeWhere((e) => e.id == sample.id);
    // 将删除的样本添加到历史记录
    _historicalCompletedSampleIds.add(sample.id);
    notifyListeners();
  }

  /// 清除历史已完成样本记录
  static void clearHistoricalCompletedSamples() {
    _historicalCompletedSampleIds.clear();
    PGLog.d('已清除历史已完成样本记录');
  }

  /// 获取历史已完成样本数量
  static int getHistoricalCompletedSamplesCount() {
    return _historicalCompletedSampleIds.length;
  }

  String? _findFirstRunningSampleProjectId() {
    for (final project in _localProjectList) {
      if (_processingListMap[project.projectId]?.isNotEmpty ?? false) {
        return project.projectId;
      }
    }
    return null;
  }

  /// 初始化重试导出刷新事件监听
  void _initializeRetryRefreshStream() {
    _retryRefreshSubscription =
        _exportManager.retryRefreshStream.listen((event) {
      PGLog.d('收到重试导出刷新事件，涉及的打样ID: ${event.proofingIds}');
      _refreshSampleList();
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription?.cancel();
    _retryRefreshSubscription?.cancel();
    super.dispose();
  }
}
