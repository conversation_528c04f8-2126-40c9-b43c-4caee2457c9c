import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/aigc_preset_detail_polling_provider.dart';
import 'package:turing_art/ui/aigc_presets/providers/aigc_preset_expand_provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_regenerate_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/error_message_listener.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_expanded_side_content.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_left_content.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_right_content.dart';
import 'package:turing_art/ui/aigc_presets/widgets/preset_preview_scope.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// AIGC 预设详情弹窗
class AigcPresetDetailDialog extends StatefulWidget {
  // 通知外层刷新列表的回调
  final VoidCallback? onRefresh;

  const AigcPresetDetailDialog({
    super.key,
    this.onRefresh,
  });

  static void show(
    BuildContext context, {
    required String presetId,
    AigcPcPresetsDetailResponse? initialData,
    VoidCallback? onRefresh,
  }) {
    PGDialog.showCustomDialog(
      width: PresetDetailConstants.dialogWidth,
      height: PresetDetailConstants.dialogHeight,
      needBlur: false,
      tag: DialogTags.aigcPresetDetail,
      child: MultiProvider(
        providers: [
          Provider<AigcPresetDetailPollingProvider>(
            create: (context) => AigcPresetDetailPollingProvider(
              repository: context.read<AigcPresetsRepository>(),
              presetId: presetId,
            ),
            dispose: (_, service) => service.dispose(),
          ),
          ChangeNotifierProvider<AigcPresetExpandProvider>(
            create: (_) => AigcPresetExpandProvider(),
          ),
          ChangeNotifierProvider<AIGCPresetDetailViewModel>(
            create: (context) => AIGCPresetDetailViewModel(
              repository: context.read<AigcPresetsRepository>(),
              accountRepository: context.read<AccountRepository>(),
              pollingService: context.read<AigcPresetDetailPollingProvider>(),
              presetId: presetId,
              initialData: initialData,
            ),
          ),
          ChangeNotifierProvider<AIGCPresetRegenerateViewModel>(
            create: (_) => AIGCPresetRegenerateViewModel(
              presetId: presetId,
              repository: context.read<AigcPresetsRepository>(),
              customTableRepository: context.read<OpsCustomTableRepository>(),
            ),
          )
        ],
        child: AigcPresetDetailDialog(onRefresh: onRefresh),
      ),
      radius: 16,
    );
  }

  @override
  State<AigcPresetDetailDialog> createState() => _AigcPresetDetailDialogState();
}

class _AigcPresetDetailDialogState extends State<AigcPresetDetailDialog> {
  @override
  Widget build(BuildContext context) {
    return PresetPreviewScope(
      child: Selector<AigcPresetExpandProvider, bool>(
        selector: (_, provider) => provider.isMainExpanded,
        builder: (context, isMainExpanded, child) {
          return Selector<AIGCPresetDetailViewModel, bool>(
            selector: (_, viewModel) => viewModel.isLoading,
            builder: (context, isLoading, child) {
              return _buildDialogContent(isMainExpanded, isLoading);
            },
          );
        },
      ),
    );
  }

  Widget _buildDialogContent(bool isMainExpanded, bool isLoading) {
    return Stack(
      children: [
        // 左侧主要内容容器
        _buildMainContent(isMainExpanded, isLoading),

        // 遮罩层
        if (isMainExpanded) _buildOverlay(),

        // 右侧展开区域
        _buildExpandedSideContent(isMainExpanded),
      ],
    );
  }

  Widget _buildMainContent(bool isMainExpanded, bool isLoading) {
    return AnimatedPositioned(
      duration: const Duration(
          milliseconds: PresetDetailConstants.animationDurationMs),
      curve: Curves.easeInOut,
      left: isMainExpanded
          ? 0 // 展开时贴左边
          : (PresetDetailConstants.dialogWidth -
                  PresetDetailConstants.collapsedWidth) /
              2,
      top: 0,
      width: PresetDetailConstants.collapsedWidth,
      height: PresetDetailConstants.dialogHeight,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1F1F1F),
          borderRadius:
              BorderRadius.circular(PresetDetailConstants.containerRadius),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4D000000),
              blurRadius: 20,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // 错误消息监听器 - 独立处理，不影响UI重建
            const ErrorMessageListener(),
            // 主要内容区域
            Expanded(
              child: isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildMainContentBody(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContentBody() {
    return Row(
      children: [
        Container(
          width: 192,
          padding: const EdgeInsets.all(16),
          child: PresetDetailLeftContent(
            coverImageSize: PresetDetailConstants.coverImageSize,
            onExit: widget.onRefresh,
          ),
        ),
        const VerticalDivider(
          color: Color(0x1AFFFFFF),
          width: 1,
          thickness: 1,
        ),
        Expanded(
          child: PresetDetailRightContent(
            onExit: _exitDialog,
          ),
        ),
      ],
    );
  }

  Widget _buildExpandedSideContent(bool isMainExpanded) {
    return AnimatedPositioned(
      duration: const Duration(
          milliseconds: PresetDetailConstants.animationDurationMs),
      curve: Curves.easeInOut,
      left: isMainExpanded
          ? (PresetDetailConstants.collapsedWidth +
              PresetDetailConstants.sideContentSpacing)
          : (PresetDetailConstants.dialogWidth -
                      PresetDetailConstants.collapsedWidth) /
                  2 +
              PresetDetailConstants.collapsedWidth,
      top: 0,
      width: PresetDetailConstants.expandedSideWidth,
      height: PresetDetailConstants.expandedSideExpandedHeight,
      child: AnimatedOpacity(
        duration: const Duration(
            milliseconds: PresetDetailConstants.animationDurationMs),
        opacity: isMainExpanded ? 1.0 : 0.0,
        child: const Align(
          alignment: Alignment.topCenter,
          child: PresetDetailExpandedSideContent(),
        ),
      ),
    );
  }

  Widget _buildOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius:
            BorderRadius.circular(PresetDetailConstants.containerRadius),
      ),
    );
  }

  void _exitDialog() {
    // 重置展开状态
    context.read<AigcPresetExpandProvider>().reset();

    PGDialog.dismiss(tag: DialogTags.aigcPresetDetail);
    if (context.read<AIGCPresetDetailViewModel>().hasEffectChanged) {
      widget.onRefresh?.call();
    }
  }
}
