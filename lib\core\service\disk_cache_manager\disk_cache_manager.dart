import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_result/cache_cleanup_result.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_config/cache_config.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_server/cache_service.dart';

/// 缓存管理器工作状态枚举
enum CacheManagerState {
  /// 空闲状态
  idle,

  /// 正在获取缓存大小
  gettingCacheSize,

  /// 正在清理缓存
  clearingCache,
}

abstract class DiskCacheManager {
  /// 当前配置
  CacheConfig get currentCacheConfig;

  /// 当前工作状态
  CacheManagerState get currentState;

  /// 工作状态变化流
  Stream<CacheManagerState> get stateStream;

  /// 是否正在清理缓存
  bool get isClearingCache;

  /// 是否正在获取缓存大小
  bool get isGettingCacheSize;

  /// 更新配置
  void updateCacheConfig(
    CacheConfig newConfig, {
    bool tryCleanupWithConfig = false,
  });

  /// 条件清理，根据当前配置的规则尝试进行进行清理
  Future<CacheCleanupResult> tryCleanupConditional();

  /// 全部清理，清理所有注册服务的缓存
  Future<CacheCleanupResult> cleanupFull();

  /// 注册缓存服务
  void registerService(CacheService service);

  /// 注销缓存服务
  void unregisterService(String serviceName);

  /// 获取缓存服务
  CacheService? getService(String serviceName);

  /// 获取缓存大小
  Future<int> getCacheSize();

  /// 获取缓存大小
  Future<int> getCacheSizeByService(String serviceName);
}
