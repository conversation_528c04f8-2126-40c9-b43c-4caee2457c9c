import 'package:turing_art/datalayer/domain/models/feedback/feedback_content.dart';
import 'package:turing_art/datalayer/domain/models/feedback/feedback_result.dart';
import 'package:turing_art/datalayer/domain/models/feedback/upload_file_certify.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';

import 'feedback_request_api.dart';
import 'upload_file.dart' as upload;

abstract class UserFeedbackService {
  //请求文件上传签名
  Future<UploadFileCertify> requestUploadCertify(String filename);

  //上传文件
  Future<void> uploadFile(String uploadCertify, String filePath);

  //发送反馈
  Future<FeedbackResult> postFeedback(FeedbackContent feedbackContent);
}

class UserFeedbackServiceImpl implements UserFeedbackService {
  final ApiClient apiClient;
  late FeedbackRequestApi api;
  UserFeedbackServiceImpl({required this.apiClient}) {
    api = FeedbackRequestApi(DioFactory.createDio(apiClient));
  }
  @override
  Future<FeedbackResult> postFeedback(FeedbackContent feedbackContent) async {
    return api.postFeedback(content: feedbackContent.toJson());
  }

  @override
  Future<UploadFileCertify> requestUploadCertify(String filename) {
    return api.createFeedbackUpload(filename: filename);
  }

  @override
  Future<void> uploadFile(String uploadCertify, String filePath) async {
    await upload.uploadFile(
        uploadCertifyJson: uploadCertify, filePath: filePath);
  }
}
