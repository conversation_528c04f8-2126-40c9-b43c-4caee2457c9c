import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 检查项目是否存在且不属于当前用户的用例
class CheckProjectNotOwnedUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;

  CheckProjectNotOwnedUseCase(
    this._projectRepository,
    this._currentUserRepository,
  );

  /// 检查项目是否存在且不属于当前用户
  /// 当projectId存在但不属于当前用户时抛出异常
  Future<void> invoke(String? projectId) async {
    if (projectId == null || projectId.isEmpty) {
      PGLog.d('项目ID为空，跳过检查');
      return;
    }

    try {
      // 获取所有项目
      final List<ProjectInfo> projects =
          await _projectRepository.getAllProjects();

      // 获取当前用户ID
      final String? currentUserId = _currentUserRepository.user?.effectiveId;
      if (currentUserId == null) {
        PGLog.d('当前用户ID为空，跳过检查');
        return;
      }

      // 查找是否存在相同ID的项目
      final ProjectInfo? existingProject =
          projects.cast<ProjectInfo?>().firstWhere(
                (project) => project?.uuid == projectId,
                orElse: () => null,
              );

      if (existingProject != null) {
        // 项目存在，检查是否属于当前用户
        if (existingProject.author != currentUserId) {
          PGLog.e(
              '项目存在但不属于当前用户: projectId=$projectId, author=${existingProject.author}, currentUserId=$currentUserId');
          throw Exception('导入项目失败：该项目已存在且不属于当前用户');
        }
      }

      PGLog.d('项目检查通过: projectId=$projectId');
    } catch (e) {
      if (e.toString().contains('导入项目失败：该项目已存在且不属于当前用户')) {
        // 重新抛出我们的自定义异常
        rethrow;
      }
      PGLog.e('检查项目所有权时发生错误: $e');
      // 对于其他错误，我们不阻止操作
    }
  }
}
