import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/model.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/core/ui/h5_webview.dart';
import 'package:turing_art/ui/employee/widget/employee_dialog.dart';
import 'package:turing_art/ui/export_history/viewModel/export_history_view_model.dart';
import 'package:turing_art/ui/export_history/widgets/export_history_dialog.dart';
import 'package:turing_art/ui/login/widgets/privacy/privacy_const.dart';
import 'package:turing_art/ui/preset_import/widget/preset_import_list_widget.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_profile_dialog.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_dialog.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_qr_code_dialog.dart';
import 'package:turing_art/ui/recharge_record/widgets/recharge_record_dialog.dart';
import 'package:turing_art/ui/setting/widgets/general_setting_dialog.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

/// 个人中心功能列表弹窗服务
class ProfileFunctionListShowService {
  /// 显示个人中心功能列表弹窗
  static void showProfileDialog({
    required BuildContext context,
    required GlobalKey profileViewKey,
    required GlobalKey userCardKey,
    required bool isSubAccount,
    required bool isOpenAccountManagement,
    required NavigatorService navigatorService,
    ExportHistoryType defaultExportType = ExportHistoryType.export,
    String? userId, // 用于埋点统计传参
  }) {
    // 获取 userCard 的 RenderBox
    final RenderBox? renderBox =
        userCardKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      PGLog.d('renderBox == null');
      return;
    }

    // 获取userCard在屏幕上的位置
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonSize = renderBox.size;

    // 计算对话框的高度，根据用户角色动态计算
    final optionsCount = isSubAccount
        ? 3
        : 7 + (isOpenAccountManagement ? 1 : 0); // 子账号3个选项，主账号7个选项
    final dialogHeight = optionsCount * 40.0 + 16.0; // 确保使用double类型

    PGDialog.showPositionedDialog(
      tag: DialogTags.homeProfile, // 使用字符串常量
      width: 296,
      height: dialogHeight,
      alignment: Alignment.topLeft, // 使用左上角对齐
      padding: EdgeInsets.only(
        left: buttonPosition.dx,
        top: buttonPosition.dy + buttonSize.height + 4,
      ), // 设置与原来相同的位置
      child: ProjectHomePcProfileDialog(
        isSubAccount: isSubAccount,
        isOpenAccountManagement: isOpenAccountManagement,
        onLogout: () async {
          // 使用AuthUseCaseProvider中的登出用例
          final authUseCaseProvider = context.read<AuthUseCaseProvider>();
          await authUseCaseProvider.logoutUseCase.invoke();
          navigatorService.navigateToLogin();
          recordLogoutBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onSettings: () {
          GeneralSettingDialog.show();
          // SettingDialog.show(context);
          recordSettingsBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onRecharge: () {
          //跳转续费页面
          PurchaseDialog.show(context, (payChannelList, orderId, collectInfo) {
            // 订单创建成功后，显示支付二维码，传递可支付渠道马上创建UI
            PurchaseQRCodeDialog.show(payChannelList, orderId, collectInfo);
          }, SourceType.self_home_page);
          recordRenewBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onCombo: () {
          // 显示充值记录弹窗
          RechargeRecordDialog.show();
          recordOrderListBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onShare: () {
          // 显示账号管理弹窗
          EmployeeDialog.show();
          recordSubAccountBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onExport: () {
          //导出明细功能
          ExportHistoryDialog.show(exportType: defaultExportType);
          recordExportListBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onAboutUs: () {
          _handleAboutUsTap();
          recordAboutBtn(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
        onImportPreset: () {
          // 导入咻图AI预设
          PresetImportListWidget.show();
          recordImportXtPreset(
              userId: userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
        },
      ),
    );
  }

  /// 处理"关于我们"点击事件
  static void _handleAboutUsTap() {
    PGLog.d('点击了关于我们');

    if (Platform.isWindows || Platform.isMacOS) {
      UrlLauncherUtil.openInSystemBrowser(PrivacyConst.aboutUrl);
    } else {
      PGDialog.showCustomDialog(
        width: PrivacyConst.privacyPolicyWidth,
        height: PrivacyConst.privacyPolicyHeight,
        tag: DialogTags.aboutUs,
        needBlur: true,
        child: H5WebView(
          url: PrivacyConst.aboutUrl,
          title: '关于我们',
          onClose: () => PGDialog.dismiss(tag: DialogTags.aboutUs),
          forDialog: true,
        ),
      );
    }
  }
}
