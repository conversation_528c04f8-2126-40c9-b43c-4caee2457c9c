import 'package:freezed_annotation/freezed_annotation.dart';

part 'export_record.freezed.dart';
part 'export_record.g.dart';

@freezed
@JsonSerializable()
class ExportRecord with _$ExportRecord {
  const factory ExportRecord({
    required String guid,
    String? name,
    String? showName,
    required List<String> exportPaths,
    required int exportState,
    required int createTime,
    required String operateTime,
    required int itemCount,
    required int successNum,
    int? errorNum,
    String? errorMessage,
    @Default(false) bool isSample, // 是否为小样导出
  }) = _ExportRecord;
  const ExportRecord._();

  factory ExportRecord.fromJson(Map<String, dynamic> json) {
    // 解析是否为小样导出
    bool isSample = false;
    try {
      final exportFileConfig =
          json['exportFileConfig'] as Map<String, dynamic>?;
      if (exportFileConfig != null) {
        final editorType = exportFileConfig['editorType'] as int?;
        isSample = editorType == 1;
      }
    } catch (e) {
      // 解析失败时默认为false
      isSample = false;
    }

    // 创建一个修改后的JSON，包含isSample字段
    final modifiedJson = Map<String, dynamic>.from(json);
    modifiedJson['isSample'] = isSample;

    return _$ExportRecordFromJson(modifiedJson);
  }

  static List<ExportRecord> fromJsonList(List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => ExportRecord.fromJson(e)).toList();

  Map<String, dynamic> toJson() {
    return _$ExportRecordToJson(
      this,
    );
  }
}

enum ExportState {
  pause(0), // 暂停
  pausing(1), // 暂停中
  wait(2), // 等待处理
  processing(3), // 处理中
  finish(4), // 全部完成，没错误
  error(5), // 部分完成，但有错误
  ioError(6); // IO错误

  const ExportState(this.stateCode);
  final int stateCode;

  // 根据状态码获取枚举值
  factory ExportState.fromCode(int code) {
    return values.firstWhere(
      (e) => e.stateCode == code,
      orElse: () => ExportState.pause,
    );
  }
}

enum ExportErrorType {
  none(0),
  fileNotFound(1 << 1),
  fileBroken(1 << 2),
  fileWriteFailed(1 << 3),
  authFailed(1 << 4),
  diskSpaceNotEnough(1 << 5),
  renderFailed(1 << 6),
  prepareFailed(1 << 7),
  historyFailed(1 << 8),
  saveFileFailed(1 << 9),
  loadOrgFailed(1 << 10),
  ioError(1 << 11),
  other(1 << 31);

  final int value;
  const ExportErrorType(this.value);

  static ExportErrorType fromValue(int value) {
    // 如果值为0，返回none
    if (value == 0) {
      return ExportErrorType.none;
    }

    // 按优先级检查各个错误类型
    if (value & ExportErrorType.authFailed.value != 0) {
      return ExportErrorType.authFailed;
    }
    if (value & ExportErrorType.diskSpaceNotEnough.value != 0) {
      return ExportErrorType.diskSpaceNotEnough;
    }
    if (value & ExportErrorType.fileNotFound.value != 0) {
      return ExportErrorType.fileNotFound;
    }
    if (value & ExportErrorType.fileBroken.value != 0) {
      return ExportErrorType.fileBroken;
    }
    if (value & ExportErrorType.fileWriteFailed.value != 0) {
      return ExportErrorType.fileWriteFailed;
    }
    if (value & ExportErrorType.renderFailed.value != 0) {
      return ExportErrorType.renderFailed;
    }
    if (value & ExportErrorType.prepareFailed.value != 0) {
      return ExportErrorType.prepareFailed;
    }
    if (value & ExportErrorType.historyFailed.value != 0) {
      return ExportErrorType.historyFailed;
    }
    if (value & ExportErrorType.saveFileFailed.value != 0) {
      return ExportErrorType.saveFileFailed;
    }
    if (value & ExportErrorType.loadOrgFailed.value != 0) {
      return ExportErrorType.loadOrgFailed;
    }
    if (value & ExportErrorType.ioError.value != 0) {
      return ExportErrorType.ioError;
    }

    // 如果没有匹配到任何已知错误类型，返回other
    return ExportErrorType.other;
  }

  /// 获取错误提示消息
  String get message {
    switch (this) {
      case ExportErrorType.none:
        return '无错误';
      case ExportErrorType.fileNotFound:
        return '文件丢失';
      case ExportErrorType.fileBroken:
        return '文件损坏';
      case ExportErrorType.fileWriteFailed:
        return '文件写入失败';
      case ExportErrorType.authFailed:
        return '认证失败';
      case ExportErrorType.diskSpaceNotEnough:
        return '磁盘空间不足';
      case ExportErrorType.renderFailed:
        return '渲染失败';
      case ExportErrorType.prepareFailed:
        return '准备失败';
      case ExportErrorType.historyFailed:
        return '历史失败';
      case ExportErrorType.saveFileFailed:
        return '保存文件失败';
      case ExportErrorType.loadOrgFailed:
        return '加载原图失败';
      case ExportErrorType.ioError:
        return 'IO错误';
      case ExportErrorType.other:
        return '其他错误';
    }
  }
}
