import 'package:turing_art/datalayer/domain/models/login_info/login_info.dart';

import '../domain/models/account/account.dart';
import '../domain/models/user/user.dart';

abstract class AuthRepository {
  // 登录
  Future<LoginInfo?> logIn(
    String phoneNumber,
    String? cc,
    String code,
    String storeId,
  );

  // 刷新token
  Future<User?> refreshUserToken(User user);

  // 登出
  Future<void> logOut(String userId);

  // 发送验证码
  Future<bool> sendVerificationCode(String phoneNumber, String? cc);

  // 获取账户列表
  Future<List<Account>> getAccountList(String phoneNumber);

  // 获取最后登录的门店ID
  Future<String> getLastLoginStoreIdByPhoneNumber(String phoneNumber);
}
