import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/datalayer/domain/enums/arrow_position.dart';
import 'package:turing_art/datalayer/domain/enums/export_status.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/aigc_sample_detail_polling_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_icon_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_eidt_top_bar_widget.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_detail_view_model.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_credit_bubble_content.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_export_path_dialog.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_image_preview_widget.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_sample_effect_list_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/utils/extensions/widget_extensions.dart';
import 'package:turing_art/utils/pg_bubble/pg_bubble_overlay.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class AigcSampleDetailScreen extends StatefulWidget {
  final String sampleId;

  const AigcSampleDetailScreen({
    super.key,
    required this.sampleId,
  });

  @override
  State<AigcSampleDetailScreen> createState() => _AigcSampleDetailScreenState();
}

class _AigcSampleDetailScreenState extends State<AigcSampleDetailScreen> {
  // 使用ValueNotifier替代布尔状态变量
  final ValueNotifier<bool> _againButtonHoveredNotifier =
      ValueNotifier<bool>(false);
  final ValueNotifier<bool> _exportButtonHoveredNotifier =
      ValueNotifier<bool>(false);

  final GlobalKey _againButtonKey = GlobalKey();
  final GlobalKey _exportButtonKey = GlobalKey();

  // 预览图片区域的GlobalKey，用于获取图片区域位置
  final GlobalKey _dialogKey = GlobalKey();

  AigcSampleModel? _initialSampleModel;

  @override
  void initState() {
    super.initState();
    // 初始化时获取样片详情
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    try {
      final repository = context.read<AigcSampleRepository>();
      final detail = await repository.getAigcSampleDetail(widget.sampleId);
      setState(() {
        _initialSampleModel = detail;
      });
    } catch (e) {
      // 如果加载失败，显示错误提示
      PGDialog.showToast('加载样片详情失败: $e');
      // 返回上一页
      if (mounted) {
        context.pop();
      }
    }
  }

  /// 计算Toast在屏幕中的top和left位置
  /// 返回相对于屏幕顶部的偏移量，默认在预览图片区域下移16像素
  (double top, double left) _calculateToastTopAndLeft() {
    final rect = _dialogKey.globalRect;
    if (rect != null) {
      // 1345 - 957  再偏移（- 118/2.0 - 16 *2）（toast内部是center对齐）
      return (rect.top + 32, -399.0 - 59 - 32);
    }
    return (84.0, 420.0 + 288.0); // 默认位置
  }

  @override
  void dispose() {
    // 释放ValueNotifier资源
    _againButtonHoveredNotifier.dispose();
    _exportButtonHoveredNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_initialSampleModel == null) {
      // 显示加载状态
      return Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        body: TitleBarWidget(
          backgroundColor: const Color(0xFF0D0D0D),
          funcWidget: AigcEditTopBarWidget(
            score: 0,
            backgroundColor: const Color(0xFF0D0D0D),
            showPurchaseButton: false,
            showMakingCenterButton: false,
            showBackButton: false,
            onPresetPressed: () {
              // 处理预设点击事件
              final navigator = GoRouterNavigatorService(context);
              navigator.navigateToAigcPresets(
                maskPath: '',
                previewPath: '',
              );
            },
            onExportPressed: () {
              // 处理导出点击事件，默认选中AIGC导出
              ExportListDialog2.show(defaultExportType: ExportType.aigc);
            },
          ),
          child: const Center(
            child: SizedBox(),
          ),
        ),
      );
    }

    return ChangeNotifierProvider(
      create: (context) => AigcSampleDetailViewModel(
        repository: context.read<AigcSampleRepository>(),
        accountRepository: context.read<AccountRepository>(),
        uploadRepository: context.read<MediaUploadRepository>(),
        mediaRepository: context.read<MediaRepository>(),
        processingService: context.read<AigcService>(),
        exportManager: context.read<AigcMySampleExportManager>(),
        proofingId: widget.sampleId,
        pollingProvider: context.read<AigcSampleDetailPollingProvider>(),
        networkProvider: context.read<NetworkProvider>(),
        customTableRepository: context.read<OpsCustomTableRepository>(),
        sampleModel: _initialSampleModel,
        onDetailClosed: () {
          // 返回时传递refresh标记
          context.pop('refresh');
        },
        onAgainButtonClicked: () {
          // 可以在这里添加特定的逻辑
        },
        onExportButtonClicked: () {
          // 可以在这里添加特定的逻辑
        },
      ),
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        body: TitleBarWidget(
          backgroundColor: const Color(0xFF0D0D0D),
          funcWidget: AigcEditTopBarWidget(
            score: 0,
            backgroundColor: const Color(0xFF0D0D0D),
            showPurchaseButton: false,
            showMakingCenterButton: false,
            showBackButton: false,
            onPresetPressed: () {
              // 处理预设点击事件
              final navigator = GoRouterNavigatorService(context);
              navigator.navigateToAigcPresets(
                maskPath: '',
                previewPath: '',
              );
            },
            onExportPressed: () {
              // 处理导出点击事件，默认选中AIGC导出
              ExportListDialog2.show(defaultExportType: ExportType.aigc);
            },
          ),
          child: _buildMainContent(),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      key: _dialogKey,
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Color(0xFF0D0D0D),
      ),
      child: Stack(
        children: [
          // Loading状态监听 - 控制PGDialog的显示和隐藏
          Selector<AigcSampleDetailViewModel, Tuple3<bool, String?, String?>>(
            selector: (_, model) => Tuple3<bool, String?, String?>(
                model.isLoding, model.errorTip, model.importantTip),
            builder: (context, data, _) {
              final isLoding = data.item1;
              final exportTip = data.item2;
              final importantTip = data.item3;
              final buildContext = context;
              final viewModel = context.read<AigcSampleDetailViewModel>();

              WidgetsBinding.instance.addPostFrameCallback((_) async {
                // 首先处理重要提示（最高优先级）
                bool hasShownImportantTip = false;
                if (viewModel.hasUnshownImportantTip() &&
                    importantTip != null) {
                  PGDialog.showToast(importantTip);
                  hasShownImportantTip = true;
                  if (buildContext.mounted) {
                    viewModel.markImportantTipShown();
                  }
                }

                // 然后处理loading状态
                if (isLoding) {
                  PGDialog.showLoading();
                } else {
                  // 调用后需要等待dismiss完成，否则会消失不掉
                  await PGDialog.dismiss();
                  // 导出完成，显示普通提示 - 只有在没有显示重要提示时才显示
                  if (!hasShownImportantTip &&
                      exportTip != null &&
                      exportTip.isNotEmpty) {
                    PGDialog.showToast(exportTip);
                    if (buildContext.mounted) {
                      // 清除提示，防止重复显示
                      viewModel.clearExportTip();
                    }
                  }
                }
              });
              return const SizedBox.shrink(); // 不显示任何UI，只用于监听状态
            },
          ),

          // 主要布局 - 三列布局
          Row(
            children: [
              // 1. 左侧返回按钮区域
              _buildBackButtonArea(),

              // 2. 中间图片预览区域
              Expanded(
                child: _buildImagePreviewArea(),
              ),

              // 3. 右侧编辑区域
              _buildEditArea(),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建返回按钮区域
  Widget _buildBackButtonArea() {
    return Container(
      width: 80,
      height: double.infinity,
      color: const Color(0xFF0D0D0D),
      child: Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 24),
          child: GestureDetector(
            onTap: () {
              AigcEditIconToast.dismissIconToast();
              // 返回时传递refresh标记
              context.pop('refresh');
            },
            child: PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFF383838),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Image.asset(
                  'assets/icons/aigc_presets_back_icon.png',
                  color: Colors.white,
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建图片预览区域
  Widget _buildImagePreviewArea() {
    return Container(
      margin: const EdgeInsets.only(top: 24, bottom: 24, left: 0, right: 24),
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Selector<AigcSampleDetailViewModel,
                Tuple2<AigcSampleModel?, int>>(
              selector: (_, model) => Tuple2<AigcSampleModel?, int>(
                  model.currentSampleDetail, model.selectedEffectIndex),
              builder: (context, data, _) {
                final sampleDetail = data.item1 ?? _initialSampleModel!;
                final selectedIndex = data.item2;

                return AigcImagePreviewWidget(
                  effect: sampleDetail.effects[selectedIndex],
                  originalUrl: _initialSampleModel!.originPhotoUrl,
                  currentIndex: selectedIndex,
                  totalPage: sampleDetail.effects.length,
                  onIndexChanged: (index) {
                    context
                        .read<AigcSampleDetailViewModel>()
                        .setSelectedEffectIndex(index);
                  },
                  parentWidth: constraints.maxWidth,
                  parentHeight: constraints.maxHeight,
                );
              },
            );
          },
        ),
      ),
    );
  }

  /// 构建右侧编辑区域
  Widget _buildEditArea() {
    return Container(
      width: 349,
      margin: const EdgeInsets.only(right: 24, top: 24, bottom: 24),
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        children: [
          // 左侧渐变分割线
          Container(
            width: 1,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Color(0x33FFFFFF), // 白色0.2透明度
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // 右侧编辑内容区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部创建时间和预设名称
                _buildEditAreaHeader(),
                const SizedBox(height: 16),
                Container(
                  height: 1,
                  margin: const EdgeInsets.only(left: 24, right: 24),
                  color: const Color(0xFFFFFFFF).withOpacity(0.1),
                ),
                const SizedBox(height: 16),
                // 样片列表区域
                Expanded(
                  child: _buildSampleEffectList(),
                ),

                // 底部功能按钮区域
                _buildEditAreaFooter(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建编辑区域头部
  Widget _buildEditAreaHeader() {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 样片创建时间
          Text(
            _formatCreateTime(_initialSampleModel!.createAt),
            style: TextStyle(
              color: const Color(0xB2FFFFFF),
              fontSize: 14,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),

          // 预设名称
          Expanded(
            child: Text(
              _initialSampleModel!.presetName,
              style: TextStyle(
                color: const Color(0xB2FFFFFF),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建样片效果列表
  Widget _buildSampleEffectList() {
    return Container(
        padding: const EdgeInsets.only(left: 24),
        child: Consumer<AigcSampleDetailViewModel>(
          builder: (context, viewModel, _) {
            final sampleDetail =
                viewModel.currentSampleDetail ?? _initialSampleModel!;
            final selectedIndex = viewModel.selectedEffectIndex;

            return AigcSampleEffectListWidget(
              sampleModel: sampleDetail,
              selectedIndex: selectedIndex,
              onItemSelected: (index) {
                viewModel.setSelectedEffectIndex(index);
              },
              onItemDeleted: (effectCode) =>
                  _handleDeleteEffect(viewModel, effectCode),
            );
          },
        ));
  }

  /// 构建编辑区域底部功能按钮
  Widget _buildEditAreaFooter() {
    return Container(
      padding: const EdgeInsets.only(left: 24, right: 3, bottom: 0, top: 24),
      child: Row(
        children: [
          // 再次打样按钮
          Expanded(child: _buildAgainButton()),
          const SizedBox(width: 8),
          // 导出按钮 - 需要监听model变化改变UI
          Expanded(
            child: Selector<AigcSampleDetailViewModel,
                Tuple3<ExportStatus, bool, int>>(
              selector: (_, model) => Tuple3<ExportStatus, bool, int>(
                model.currentExportStatus,
                model.currentEffect?.photoUrl.isEmpty ?? true,
                model.exportCost,
              ),
              builder: (context, data, _) {
                final exportStatus = data.item1;
                final exportCost = data.item3;

                // 使用 WidgetsBinding 确保在下一帧调用 Toast 方法
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (exportStatus == ExportStatus.running) {
                    final (top, left) = _calculateToastTopAndLeft();
                    AigcEditIconToast.showLoadingToast(
                      context,
                      '正在导出...',
                      top: top,
                      left: left,
                    );
                  } else {
                    AigcEditIconToast.dismissIconToast();
                  }
                });
                return _buildExportButton(exportStatus, exportCost);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgainButton() {
    return Selector<AigcSampleDetailViewModel, int>(
      selector: (_, vm) => vm.proofingCost,
      builder: (context, proofingCost, child) {
        return _buildActionButton(
          key: _againButtonKey,
          iconPath: 'assets/icons/aigc_sample_detail_again.png',
          text: '再次打样',
          backgroundColor: const Color(0xFF383838),
          textColor: Colors.white,
          hoveredNotifier: _againButtonHoveredNotifier,
          onTap: () => _handleAgain(context.read<AigcSampleDetailViewModel>()),
          bubbleText: '再次打样需消耗 $proofingCost 积分',
          bubbleWidth: 159,
          bubbleTag: 'again_bubble',
        );
      },
    );
  }

  Widget _buildExportButton(ExportStatus exportStatus, int exportCost) {
    return Consumer<AigcSampleDetailViewModel>(
      builder: (context, viewModel, child) {
        final currentEffect = viewModel.currentEffect;
        if (currentEffect == null) {
          return const SizedBox.shrink();
        }

        String buttonText;
        String iconPath;
        Color textColor;
        Color backgroundColor;

        switch (exportStatus) {
          case ExportStatus.completed:
            buttonText = '查看文件路径';
            iconPath = 'assets/icons/aigc_sample_detail_check_path.png';
            textColor = Colors.white;
            backgroundColor = const Color(0xFF383838);
            break;
          case ExportStatus.running:
            buttonText = '正在导出';
            iconPath = 'assets/icons/aigc_sample_detail_export.png';
            textColor = const Color(0x59FFFFFF);
            backgroundColor = const Color(0xFF383838);
            break;
          case ExportStatus.unexported:
          default:
            buttonText = '导出';
            iconPath = 'assets/icons/aigc_sample_detail_export.png';
            if (currentEffect.photoUrl.isEmpty) {
              // 正在打样中
              textColor = const Color(0x59FFFFFF);
              backgroundColor = const Color(0xFF383838);
            } else {
              textColor = const Color(0xFF0D0D0D);
              backgroundColor = Colors.white;
            }
            break;
        }

        return _buildActionButton(
          key: _exportButtonKey,
          iconPath: iconPath,
          text: buttonText,
          backgroundColor: backgroundColor,
          textColor: textColor,
          hoveredNotifier: _exportButtonHoveredNotifier,
          onTap: viewModel.isExportButtonEnabled()
              ? () => _handleExport(viewModel)
              : null,
          bubbleText: exportStatus == ExportStatus.unexported
              ? '导出需消耗 $exportCost 积分'
              : null,
          bubbleWidth: 135,
          bubbleTag: 'export_bubble',
        );
      },
    );
  }

  Widget _buildActionButton({
    required GlobalKey key,
    required String iconPath,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required ValueNotifier<bool> hoveredNotifier,
    required VoidCallback? onTap,
    String? bubbleText,
    double? bubbleWidth,
    String? bubbleTag,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: hoveredNotifier,
      builder: (context, isHovered, _) {
        return PlatformMouseRegion(
          key: key,
          onEnter: (_) {
            hoveredNotifier.value = true;
            if (bubbleText != null &&
                bubbleWidth != null &&
                bubbleTag != null) {
              _showBubble(
                buttonKey: key,
                text: bubbleText,
                width: bubbleWidth,
                tag: bubbleTag,
              );
            }
          },
          onExit: (_) {
            hoveredNotifier.value = false;
            if (bubbleTag != null) {
              PGBubbleOverlay.dismiss(tag: bubbleTag);
            }
          },
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: isHovered
                    ? backgroundColor.withOpacity(0.9)
                    : backgroundColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ColorFiltered(
                      colorFilter: ColorFilter.mode(
                        textColor,
                        BlendMode.srcIn,
                      ),
                      child: Image.asset(
                        iconPath,
                        width: 20,
                        height: 20,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        text,
                        style: TextStyle(
                          color: textColor,
                          fontSize: 12,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showBubble({
    required GlobalKey buttonKey,
    required String text,
    required double width,
    required String tag,
  }) {
    // 获取按钮的全局位置
    final rect = buttonKey.globalRect;

    // 如果无法获取按钮位置，则不显示气泡
    if (rect == null) {
      return;
    }

    PGBubbleOverlay.show(
      context: context,
      targetRect: rect,
      content: AigcCreditBubbleContent(text: text),
      bubbleWidth: width,
      bubbleHeight: 44,
      borderRadius: 6,
      arrowPosition: ArrowPosition.bottom,
      arrowOffset: width / 2.0,
      backgroundColor: const Color(0xFF424242),
      borderColor: const Color(0xFF424242),
      tag: tag,
    );
  }

  String _formatCreateTime(int timestamp) {
    if (timestamp == 0) {
      return '';
    }
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.month}.${date.day} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _handleAgain(AigcSampleDetailViewModel viewModel) async {
    try {
      await viewModel.regenerateAigcSample(widget.sampleId);
      // 重新打样成功后刷新账户信息
      viewModel.refreshAccountAfterSuccess();
      viewModel.onAgainButtonClicked();
    } catch (e) {
      PGDialog.showToast('再次打样失败: $e');
    }
  }

  void _handleExport(AigcSampleDetailViewModel viewModel) async {
    final exportStatus = viewModel.currentExportStatus;
    if (exportStatus == ExportStatus.completed) {
      if (!_showExportPathDialogIfNeeded(viewModel)) {
        // 不需要显示导出路径设置弹窗，直接查看文件路径 - 使用统一的handler处理
        await viewModel.handleExportPathView();
      }
    } else if (exportStatus == ExportStatus.unexported ||
        exportStatus == ExportStatus.failed) {
      if (!_showExportPathDialogIfNeeded(viewModel)) {
        // 不需要显示导出路径设置弹窗，直接导出
        await viewModel.exportAigcSample();
        // 导出成功后刷新账户信息
        viewModel.refreshAccountAfterSuccess();
      }
    }
  }

  // 检查用户偏好设置，决定是否显示导出路径设置弹窗
  bool _showExportPathDialogIfNeeded(AigcSampleDetailViewModel viewModel) {
    final showDialog = UserPreferencesService.getShowExportPathDialog();
    if (showDialog) {
      final exportStatus = viewModel.currentExportStatus;

      // 显示导出路径设置弹窗
      AigcExportPathDialog.show(
        context,
        viewModel,
        () async {
          if (exportStatus == ExportStatus.unexported ||
              exportStatus == ExportStatus.failed) {
            // 导出
            await viewModel.exportAigcSample();
            // 导出成功后刷新账户信息
            viewModel.refreshAccountAfterSuccess();
          } else if (exportStatus == ExportStatus.completed) {
            // 查看文件路径
            await viewModel.handleExportPathView();
          }
        },
      );
    }
    return showDialog;
  }

  void _handleDeleteEffect(
      AigcSampleDetailViewModel viewModel, String effectCode) async {
    try {
      await viewModel.deleteAigcSampleEffect(widget.sampleId, effectCode);
    } catch (e) {
      PGDialog.showToast('删除失败: $e');
    }
  }
}
