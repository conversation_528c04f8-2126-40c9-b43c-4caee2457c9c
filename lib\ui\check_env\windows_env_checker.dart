import 'dart:io';
import 'app_mismatch_reason.dart';

/// windows app 是否与当前 windows 系统版本匹配
CheckAppMatchResult checkWindowsAppMatch(String platform) {
  if (!Platform.isWindows) {
    // 非windows平台不检查
    return const CheckAppOK();
  }

  if (platform == 'win7' && !_isCurrentPlatformLessThanWin10()) {
    return const NeedDownloadWin10App();
  }

  if (platform != 'win7' && _isCurrentPlatformLessThanWin10()) {
    return const NeedDownloadWin7App();
  }
  return const CheckAppOK();
}

bool _isCurrentPlatformLessThanWin10() {
  final versionString = Platform.operatingSystemVersion.toLowerCase();
  if (versionString.contains('windows 10') ||
      versionString.contains('windows 11')) {
    return false;
  } else {
    return true;
  }
}
