import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 购买成功弹窗
class PurchaseSuccessDialog extends StatelessWidget {
  const PurchaseSuccessDialog({super.key, this.onClose});

  final Future<void> Function()? onClose;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints.tight(const Size(700, 500)),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: const Color(0xFF1B1C1F),
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x33000000),
                blurRadius: 40,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // 关闭按钮
              Positioned(
                top: 16,
                right: 16,
                child: GestureDetector(
                  onTap: () async {
                    // 关闭弹窗
                    await PGDialog.dismiss(tag: DialogTags.purchaseSuccess);
                    onClose?.call();
                  },
                  child: Image.asset(
                    'assets/icons/home_window_close.png',
                    width: 24,
                    height: 24,
                  ),
                ),
              ),

              // 标题上方的线条和文字
              Positioned(
                top: 56,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 左线条
                    Container(
                      width: 12,
                      height: 1,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0x00FFFFFF), Color(0xFFFFFFFF)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    // 标题文字
                    Text(
                      "图灵精修套餐 无套路更实惠",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                        fontSize: 14,
                        height: 16 / 14,
                        color: const Color(0x66FFFFFF),
                      ),
                    ),
                    const SizedBox(width: 4),
                    // 右线条
                    Container(
                      width: 12,
                      height: 1,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFFFFFFFF), Color(0x00FFFFFF)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 副标题
              Positioned(
                top: 79, // 56 + 23
                left: 0,
                right: 0,
                child: Center(
                  child: Text(
                    "恭喜你 已购买成功",
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                      fontSize: 24,
                      height: 32 / 24,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              // 中间图标
              const Positioned(
                top: 135,
                left: 260,
                child: Image(
                  image: AssetImage('assets/icons/new_user_card_success.png'),
                  width: 180,
                  height: 180,
                ),
              ),

              // 好的按钮
              Positioned(
                top: 359,
                left: 286,
                child: GestureDetector(
                  onTap: () async {
                    // 关闭弹窗
                    await PGDialog.dismiss(tag: DialogTags.purchaseSuccess);
                    onClose?.call();
                  },
                  child: Container(
                    width: 128,
                    height: 44,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF72561),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "好的",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                        fontSize: 12,
                        height: 20 / 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),

              // 当前剩余张数
              Positioned(
                top: 411, // 359 + 44 + 8
                left: 0,
                right: 0,
                child: Center(
                  child: Consumer<ProfileDialogViewModel>(
                    builder: (context, profileViewModel, _) {
                      final available =
                          profileViewModel.editExportInfo?.available ?? "0";
                      final point = profileViewModel.integralCount;

                      return FutureBuilder<bool>(
                        future:
                            context.read<AigcEntranceManager>().isAigcUser(),
                        builder: (context, snapshot) {
                          final bool isAigcUser = snapshot.data ?? false;

                          return Text(
                            isAigcUser
                                ? "当前剩余张数 $available 张，当前剩余积分 $point 点"
                                : "当前剩余张数 $available 张",
                            style: TextStyle(
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.regular,
                              fontSize: 12,
                              height: 16 / 12,
                              color: const Color(0xFFF72561),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),

              // 温馨提示
              Positioned(
                bottom: 34,
                left: 0,
                right: 0,
                child: Center(
                  child: Text(
                    "温馨提示：如遇购买的套餐未到账，请尝试退出软件后重新打开。若仍未得到解决，请联系客服。",
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                      fontSize: 14,
                      height: 18 / 14,
                      color: const Color(0xFFEBEDF5).withOpacity(0.4),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 创建对话框内容，使用传入的 context 获取依赖
  static Widget _createDialogContent(
      BuildContext context, Future<void> Function()? onClose) {
    // 创建并使用 ProfileDialogViewModel，以获取最新的账户信息
    return ChangeNotifierProvider<ProfileDialogViewModel>(
      create: (context) => ProfileDialogViewModel(
        context.read<CurrentUserRepository>(),
        context.read<AccountRepository>(),
        context.read<WechatGiftRepository>(),
        context.read<OpsCustomTableRepository>(),
        context.read<AccountRightsStateProvider>(),
        context.read<RewardRepository>(),
        context.read<NewUserRepository>(),
        context.read<AuthUseCaseProvider>(),
      ),
      child: PurchaseSuccessDialog(onClose: onClose),
    );
  }

  /// 确保账户信息已经更新后再显示弹窗
  static Future<void> _ensureAccountRefreshed(BuildContext context) async {
    try {
      // 刷新账户信息
      await context.read<AccountRepository>().refreshAllAccount();
      PGLog.d('购买成功弹窗：账户信息已刷新');
    } catch (e) {
      PGLog.e('购买成功弹窗刷新账户信息失败: $e');
    }
  }

  /// public方法：显示对话框
  static Future<void> show(Future<void> Function()? onClose) async {
    // 在显示时从当前的上下文获取
    PGLog.d('显示购买成功弹窗');

    // 由于PurchaseSuccessDialog是在订单完成事件中调用的
    // 该事件应该已经在一个有效的BuildContext中执行
    // 因此可以直接创建和显示对话框
    PGDialog.showCustomDialog(
      width: 700,
      height: 500,
      needBlur: false,
      tag: DialogTags.purchaseSuccess,
      child: Builder(
        builder: (context) {
          // 在Builder中，我们可以访问当前的BuildContext
          // 确保在显示对话框前先刷新账户数据
          // 使用Future.microtask确保异步操作完成
          Future.microtask(() => _ensureAccountRefreshed(context));
          return _createDialogContent(context, onClose);
        },
      ),
    );
  }

  /// public方法：在Unity窗口上显示对话框
  static Future<void> showOnUnity(Future<void> Function()? onClose) async {
    PGLog.d('在Unity窗口上显示购买成功弹窗');

    // 在Unity窗口上显示时也使用相同的方式处理
    PGDialog.showCustomDialogOnUnity(
      width: 700,
      height: 500,
      needBlur: false,
      tag: DialogTags.purchaseSuccess,
      child: Builder(
        builder: (context) {
          // 在Builder中，我们可以访问当前的BuildContext
          // 确保在显示对话框前先刷新账户数据
          Future.microtask(() => _ensureAccountRefreshed(context));
          return _createDialogContent(context, onClose);
        },
      ),
    );
  }
}
