import 'package:flutter/material.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class ProjectHomeIntegralInfoWidget extends StatefulWidget {
  final int integralCount;
  final bool isNeedPurchase; // 是否需要购买积分按钮
  final VoidCallback? onBuyIntegralClick;

  const ProjectHomeIntegralInfoWidget({
    super.key,
    this.integralCount = 2, // 默认积分值
    this.isNeedPurchase = true, // 默认需要购买积分按钮
    this.onBuyIntegralClick,
  });

  @override
  State<ProjectHomeIntegralInfoWidget> createState() =>
      _ProjectHomeIntegralInfoWidgetState();
}

class _ProjectHomeIntegralInfoWidgetState
    extends State<ProjectHomeIntegralInfoWidget> {
  final ValueNotifier<bool> _buyButtonHoveredNotifier =
      ValueNotifier<bool>(false);

  @override
  void dispose() {
    _buyButtonHoveredNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0x1AFFFFFF), // 白色10%的实际颜色，不带透明度
            width: 0.5,
          ),
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 计算文字的固定宽度：当前控件宽度 - icon宽度(20) - 间距(4) - 按钮宽度(56) - 额外间距(2)
          final textWidth = constraints.maxWidth - 20 - 4 - 56 - 2;

          return Row(
            crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
            children: [
              Image.asset(
                'assets/icons/integral_icon.png',
                width: 20,
                height: 20,
              ),
              const SizedBox(width: 4),
              SizedBox(
                width: textWidth,
                child: Text(
                  '${widget.integralCount}',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 14,
                    height: 16 / 14, // lineHeight / fontSize
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.left, // 文字左对齐
                  overflow: TextOverflow.ellipsis, // 显示不下用省略号
                  maxLines: 1, // 单行显示
                ),
              ),
              const SizedBox(width: 2), // 额外间距
              if (widget.isNeedPurchase) _buildBuyIntegralButton(),
            ],
          );
        },
      ),
    );
  }

  /// 构建购买积分按钮
  Widget _buildBuyIntegralButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _buyButtonHoveredNotifier,
      builder: (context, isHovered, _) {
        return MouseRegion(
          onEnter: (_) => _buyButtonHoveredNotifier.value = true,
          onExit: (_) => _buyButtonHoveredNotifier.value = false,
          child: DebounceClickWidget(
            onTap: widget.onBuyIntegralClick,
            child: Container(
              width: 56,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: const Color(0xFF1A1A1A), // 同样使用不带透明度的颜色
                  width: 0.5,
                ),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: isHovered
                      ? [
                          const Color(0x33B3F5FB),
                          const Color(0x1AA9F6FD),
                        ]
                      : [
                          const Color(0x3322EDFF), // rgba(34, 237, 255, 0.2)
                          const Color(0x1A22EDFF), // rgba(34, 237, 255, 0.1)
                        ],
                ),
              ),
              child: Center(
                child: Text(
                  '购买积分',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 11,
                    height: 16 / 11, // lineHeight / fontSize
                    color: const Color(0xFF22EDFF),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
