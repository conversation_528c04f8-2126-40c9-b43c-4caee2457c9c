import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/config/image_cache_config.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_result/cache_cleanup_result.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_server/cache_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 远程图片磁盘缓存服务
/// 基于 ImageCacheConfig 实现，所有操作均在后台线程执行以避免UI卡顿
class RemoteImageDiskService extends CacheService {
  @override
  String get serviceName => 'remoteImage';

  @override
  CacheServiceLevel get level => CacheServiceLevel.low;

  @override
  Future<ServiceCleanupResult> clearCache({int? targetSize}) async {
    return await compute(_clearCacheInBackground, targetSize);
  }

  @override
  Future<int> getCacheSize() async {
    final cacheFile = await ImageCacheConfig.getCacheFile();
    if (cacheFile == null) {
      return 0;
    }
    return await compute(_getCacheFilesInBackground, cacheFile.file);
  }

  /// 在后台线程执行清理操作
  static Future<ServiceCleanupResult> _clearCacheInBackground(
    int? targetSize,
  ) async {
    try {
      final timestamp = DateTime.now();
      int deletedSize = 0;

      // 获取清理前的大小和文件数量
      final cacheFile = await ImageCacheConfig.getCacheFile();
      if (cacheFile == null) {
        return ServiceCleanupResult(
          serviceName: 'remoteImage',
          deletedSize: 0,
          timestamp: DateTime.now(),
        );
      }
      final beforeSize = await _getCacheFilesInBackground(cacheFile.file);

      // 执行清理
      await ImageCacheConfig.clearAllCache();

      PGLog.i(
          'RemoteImageDiskService: 清理了 ${ImageCacheConfig.formatCacheSize(deletedSize)}');

      return ServiceCleanupResult(
        serviceName: 'remoteImage',
        deletedSize: beforeSize,
        timestamp: timestamp,
      );
    } catch (e) {
      PGLog.e('RemoteImageDiskService: 清理缓存失败: $e');
      return ServiceCleanupResult(
        serviceName: 'remoteImage',
        deletedSize: 0,
        timestamp: DateTime.now(),
      );
    }
  }

  /// 在后台线程获取缓存文件列表
  static Future<int> _getCacheFilesInBackground(
    File cacheFile,
  ) async {
    try {
      int size = 0;
      await for (final entity
          in cacheFile.parent.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          size += await entity.length();
        }
      }

      return size;
    } catch (e) {
      PGLog.e('RemoteImageDiskService: 获取缓存文件列表失败: $e');
      return 0;
    }
  }
}
