// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feedback_content.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FeedbackContentImpl _$$FeedbackContentImplFromJson(
        Map<String, dynamic> json) =>
    _$FeedbackContentImpl(
      category: json['category'] as String,
      content: json['content'] as String,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      clientInfo: json['clientInfo'] == null
          ? null
          : FeedbackClientInfo.fromJson(
              json['clientInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$FeedbackContentImplToJson(
        _$FeedbackContentImpl instance) =>
    <String, dynamic>{
      'category': instance.category,
      'content': instance.content,
      'attachments': instance.attachments,
      'clientInfo': instance.clientInfo,
    };
