import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/feedback/use_case/upload_feedback_use_case.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/ui/unity/use_case/generate_get_current_cache_rule_message_usecase.dart';
import 'package:turing_art/ui/unity/use_case/generate_setup_control_config_unity_message_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_abnormal_feedback_usecase.dart';
import 'package:turing_art/ui/unity/use_case/get_disk_type_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_dynamic_encryption_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_encrypted_lut_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_task_update_state_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_token_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_open_image_picker_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_project_cache_rule_use_case.dart';
import 'package:turing_art/ui/unity/use_case/import_images_to_unity_usecase.dart';
import 'package:turing_art/ui/unity/use_case/process_dropped_files_usecase.dart';
import 'package:turing_art/ui/unity/use_case/sync_workspace_usecase.dart';
import 'package:turing_art/ui/unity/use_case/track_event_use_case.dart';

import 'package:turing_art/datalayer/service/feedback/user_feedback_service.dart';
import 'generate_exit_workspace_unity_message.dart';
import 'generate_export_with_preset_unity_message_use_case.dart';
import 'generate_fetch_device_information_unity_message_use_case.dart';
import 'generate_import_preset_from_history_unity_message_use_case.dart';
import 'generate_import_workspace_unity_message_usecase.dart';
import 'generate_login_unity_message_usecase.dart';
import 'generate_logout_unity_message_use_case.dart';
import 'generate_rename_workspace_unity_message_use_case.dart';
import 'generate_set_n8_select_filter_on_unity_message_usecase.dart';
import 'generate_set_n8_select_unity_message_usecase.dart';
import 'generate_setup_export_config_unity_message_usecase.dart';
import 'generate_setup_resolution_unity_message_use_case.dart';
import 'generate_switch_workspace_unity_message_usecase.dart';
import 'get_cache_disk_use_cache.dart';
import 'handle_current_device_information_use_case.dart';
import 'handle_export_failed_use_case.dart';
import 'handle_export_project_use_case.dart';
import 'handle_local_key_usecase.dart';
import 'workspace_changed_usecase.dart';

class UnityUseCaseProvider {
  // 消息生成相关的UseCase
  final GenerateLoginUnityMessageUseCase generateLoginUnityMessage;
  final GenerateFetchDeviceInformationUnityMessageUseCase
      fetchDeviceInformationMessage;
  final GenerateSwitchWorkspaceUnityMessageUseCase switchWorkspace;
  final GenerateExitWorkspaceUnityMessageUseCase exitWorkspace;

  final GenerateSetupExportConfigUnityMessageUseCase setupExportConfig;
  final GenerateImportWorkspacesUnityMessageUseCase importWorkspaces;
  final GenerateImportPresetFromHistoryUnityMessageUseCase
      importPresetFromHistory;
  final GenerateLoginUnityMessageUseCase login;
  final GenerateLogoutUnityMessageUseCase logout;

  final GenerateGetCurrentCacheRuleMessageUseCase getCurrentCacheRule;

  final GenerateExportWithPresetUnityMessageUseCase exportWithPreset;
  final GenerateRenameWorkspaceUnityMessageUseCase renameWorkspace;

  // N8选版相关UseCase
  final GenerateSetN8SelectUnityMessageUseCase setN8Select;
  final GenerateSetN8SelectFilterOnUnityMessageUseCase setN8SelectFilterOn;

  // 工作区相关UseCase
  final SyncWorkspaceUseCase syncWorkspace;
  final WorkspaceChangedUseCase workspaceChanged;

  // 拖拽文件处理相关的UseCase
  final ProcessDroppedFilesUseCase processDroppedFiles;
  final ImportImagesToUnityUseCase importImagesToUnity;

  // 上报反馈相关UseCase
  final UploadFeedbackUseCase uploadFeedbackUseCase;

  // 保留原有的依赖注入
  final CurrentUserRepository _currentUserRepository;
  final ProjectRepository _projectRepository;
  final AccountRepository _accountRepository;
  final NetworkProvider _networkProvider;
  final CurrentDeviceInformationProvider _currentDeviceInformationProvider;
  final OpsCustomTableRepository _opsCustomTableRepository;

  UnityUseCaseProvider(
    this._currentUserRepository,
    this._projectRepository,
    this._accountRepository,
    this._networkProvider,
    this._currentDeviceInformationProvider,
    this._opsCustomTableRepository,
    UserFeedbackService _feedbackService,
    SettingRepository settingRepository,
  )   : generateLoginUnityMessage =
            GenerateLoginUnityMessageUseCase(_currentUserRepository),
        switchWorkspace =
            GenerateSwitchWorkspaceUnityMessageUseCase(_projectRepository),
        exitWorkspace = GenerateExitWorkspaceUnityMessageUseCase(),
        setupExportConfig = GenerateSetupExportConfigUnityMessageUseCase(
          _projectRepository,
          settingRepository,
        ),
        syncWorkspace = SyncWorkspaceUseCase(_projectRepository),
        importWorkspaces =
            GenerateImportWorkspacesUnityMessageUseCase(_projectRepository),
        importPresetFromHistory =
            GenerateImportPresetFromHistoryUnityMessageUseCase(),
        login = GenerateLoginUnityMessageUseCase(_currentUserRepository),
        logout = GenerateLogoutUnityMessageUseCase(_currentUserRepository),

        // 初始化新的UseCase
        processDroppedFiles = ProcessDroppedFilesUseCase(),
        importImagesToUnity = ImportImagesToUnityUseCase(),
        exportWithPreset = GenerateExportWithPresetUnityMessageUseCase(
          _projectRepository,
        ),
        renameWorkspace = GenerateRenameWorkspaceUnityMessageUseCase(
          _projectRepository,
        ),
        fetchDeviceInformationMessage =
            GenerateFetchDeviceInformationUnityMessageUseCase(),
        workspaceChanged = WorkspaceChangedUseCase(_projectRepository),

        // 初始化N8选版相关UseCase
        setN8Select = GenerateSetN8SelectUnityMessageUseCase(),
        setN8SelectFilterOn = GenerateSetN8SelectFilterOnUnityMessageUseCase(),
        getCurrentCacheRule = GenerateGetCurrentCacheRuleMessageUseCase(),
        uploadFeedbackUseCase = UploadFeedbackUseCase(_feedbackService);

  /// 创建导出Token消息处理器
  HandleExportTokenUseCase createExportTokenHandler(
    UnityController unityController,
    ExportUseCaseProvider exportUseCaseProvider,
  ) {
    return HandleExportTokenUseCase(
      unityController,
      exportUseCaseProvider,
      _projectRepository,
    );
  }

  /// 创建动态加密参数消息处理器
  HandleDynamicEncryptionParamsUseCase createDynamicEncryptionHandler(
    UnityController unityController,
  ) {
    return HandleDynamicEncryptionParamsUseCase(
      unityController,
      _currentUserRepository,
    );
  }

  /// 创建加密LUT参数消息处理器
  HandleEncryptedLutParamsUseCase createEncryptedLutHandler(
    UnityController unityController,
    ExportUseCaseProvider exportUseCaseProvider,
  ) {
    return HandleEncryptedLutParamsUseCase(
      unityController,
      exportUseCaseProvider,
    );
  }

  /// 创建导出完成消息处理器
  HandleExportCompletedUseCase createExportCompletedHandler(
    ExportUseCaseProvider exportUseCaseProvider,
  ) {
    return HandleExportCompletedUseCase(
      exportUseCaseProvider,
      _currentUserRepository,
      _accountRepository,
      _networkProvider,
      _projectRepository,
    );
  }

  /// 创建获取缓存磁盘空间消息处理器
  GetCacheDiskSpaceUseCase createGetCacheDiskSpaceHandler(
    UnityController unityController,
  ) {
    return GetCacheDiskSpaceUseCase(unityController);
  }

  /// 创建获取磁盘类型消息处理器
  GetDiskTypeUseCase createGetDiskTypeHandler(
    UnityController unityController,
  ) {
    return GetDiskTypeUseCase(unityController);
  }

  /// 创建导出失败消息处理器
  HandleExportFailedUseCase createExportFailedHandler(
    ExportProjectProvider exportProjectProvider,
  ) {
    return HandleExportFailedUseCase(exportProjectProvider);
  }

  /// 创建获取设备信息消息处理器
  HandleCurrentDeviceInformationUseCase
      createCurrentDeviceInformationHandler() {
    return HandleCurrentDeviceInformationUseCase(
      _currentDeviceInformationProvider,
    );
  }

  /// 创建导出项目消息处理器
  HandleExportProjectUseCase createExportProjectHandler(
    ExportProjectProvider exportProjectProvider,
  ) {
    return HandleExportProjectUseCase(exportProjectProvider);
  }

  GenerateSetupResolutionUnityMessageUseCase createSetupResolutionHandler() {
    return GenerateSetupResolutionUnityMessageUseCase();
  }

  /// 创建新手引导消息处理器
  HandleNoviceGuideUseCase createNoviceGuideHandler(
    UnityController unityController,
    NoviceGuideManager noviceGuideManager,
  ) {
    return HandleNoviceGuideUseCase(
      unityController,
      noviceGuideManager,
    );
  }

  /// 创建新手引导完成状态消息处理器
  HandleNoviceGuideCompletedUseCase createGuideCompletedHandler(
    NoviceGuideManager noviceGuideManager,
  ) {
    return HandleNoviceGuideCompletedUseCase(
      noviceGuideManager,
    );
  }

  /// 创建打开图片选择器消息处理器
  HandleOpenImagePickerUseCase createOpenImagePickerHandler(
    UnityController unityController,
  ) {
    return HandleOpenImagePickerUseCase(
      ImageSelectionService.forPlatform(),
      unityController,
      importImagesToUnity,
    );
  }

  /// 创建本地密钥消息处理器
  HandleLocalKeyUseCase createLocalKeyHandler(
    UnityController unityController,
  ) {
    return HandleLocalKeyUseCase(unityController, _opsCustomTableRepository);
  }

  /// 创建导出任务状态更新消息处理器
  HandleExportTaskUpdateStateUseCase createExportTaskUpdateStateHandler(
    ExportTaskStateProvider exportTaskStateProvider,
  ) {
    return HandleExportTaskUpdateStateUseCase(exportTaskStateProvider);
  }

  /// 创建当前缓存规则消息处理器
  HandleProjectCacheRuleUseCase createProjectCacheRuleHandler(
    ProjectCacheRuleProvider projectCacheRuleProvider,
  ) {
    return HandleProjectCacheRuleUseCase(projectCacheRuleProvider);
  }

  /// 创建设置控制配置消息处理器
  GenerateSetupControlConfigUnityMessageUseCase
      createSetupControlConfigHandler() {
    return GenerateSetupControlConfigUnityMessageUseCase();
  }

  /// 创建跟踪事件消息处理器
  HandleTrackEventUseCase createTrackEventHandler() {
    return HandleTrackEventUseCase();
  }

  /// 创建异常反馈消息处理器
  HandleAbnormalFeedbackUseCase createAbnormalFeedbackHandler() {
    return HandleAbnormalFeedbackUseCase(uploadFeedbackUseCase);
  }
}
