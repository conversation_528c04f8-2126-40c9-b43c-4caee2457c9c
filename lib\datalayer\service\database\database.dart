// database.dart
//引入自动生成的代码，刚开始会报错，运行 flutter pub run build_runner build 即可
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'entity/creator_info_entity.dart';
import 'entity/export_task_entity.dart';
import 'entity/export_task_file_entity.dart';
import 'entity/export_token_entity.dart';
import 'entity/file_operation_history_entity.dart';
import 'entity/project_entity.dart';
import 'entity/user_entity.dart';
import 'entity/workspace_entity.dart';
import 'entity/workspace_file_entity.dart';
import 'migrations/database_migrations.dart';
import 'schema_versions.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  ProjectEntity,
  UserEntity,
  WorkspaceEntity,
  WorkspaceFileEntity,
  ExportTokenEntity,
  CreatorInfoEntity,
  FileOperationHistoryEntity,
  ExportTaskEntity,
  ExportTaskFileEntity,
])
class DataBase extends _$DataBase {
  // 使用固定路径的构造函数
  DataBase(String dbPath) : super(_openConnection(File(dbPath)));

  // 静态私有变量保存唯一实例
  // static final DataBase _instance = DataBase._();

  // 公有静态方法，外部可以通过此方法访问唯一实例
  // static DataBase get instance => _instance;

  @override
  int get schemaVersion => 6;

  //数据库迁移方法
  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onUpgrade: stepByStep(
        from1To2: (m, schema) async {
          // v1.1.0 版本数据库升级
          await DatabaseMigrations.migrateFrom1To2(m, schema);
        },
        from2To3: (m, schema) async {
          // v1.3.0 版本数据库升级,Unity使用数据库存储方案，扩充工程表字段同Unity侧保持一致
          await DatabaseMigrations.migrateFrom2To3(m, schema);
        },
        from3To4: (m, schema) async {
          // v1.4.0 版本数据库升级,新增工程类型字段
          await DatabaseMigrations.migrateFrom3To4(m, schema);
        },
        from4To5: (m, schema) async {
          // v1.5.0 版本数据库升级,新增文件操作历史表
          await DatabaseMigrations.migrateFrom4To5(m, schema);
        },
        from5To6: (m, schema) async {
          // v1.8.0 版本数据库升级,新增导出任务表
          await DatabaseMigrations.migrateFrom5To6(m, schema);
        },
      ),
      beforeOpen: (details) async {},
    );
  }
}

LazyDatabase _openConnection(File dbFile) {
  // 通过LazyDatabase util可以异步的找到文件
  return LazyDatabase(() async {
    try {
      // 确保文件管理器初始化完成
      PGLog.d('数据库路径: $dbFile');
      PGLog.d('数据库目录是否存在: ${dbFile.parent.existsSync()}');
      PGLog.d('数据库文件是否存在: ${dbFile.existsSync()}');
      final db = NativeDatabase(dbFile);
      PGLog.d('数据库连接成功');
      return db;
    } catch (e, stack) {
      PGLog.d('数据库初始化过程发生错误: $e\n$stack');
      rethrow;
    }
  });
}
