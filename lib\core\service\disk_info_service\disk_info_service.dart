import 'dart:async';
import 'dart:io';

import 'win_disk_info_service_impl.dart';
import 'mac_disk_info_service_impl.dart';
import 'package:turing_art/core/service/disk_info_service/models/disk_info_models.dart';

/// 磁盘信息服务接口 (保持不变)
abstract class DiskInfoService {
  bool get isInitialized;
  bool get isInitializing;

  Future<bool> initialize();
  Future<DiskType?> getDiskType(String path);
  Future<DiskSpaceInfo?> getDiskSpace(String path);
  void reset();
  void dispose();
}

/// 磁盘信息工具类 - 简化版本
/// 内部自动管理服务实例，调用者只需要调用静态方法即可
class DiskInfo {
  static DiskInfoService? _service;

  /// 获取服务实例（内部使用）
  static DiskInfoService get _internalService {
    if (_service == null) {
      throw StateError(
          'DiskInfoService not initialized. Call DiskInfo.initialize() first.');
    }
    return _service!;
  }

  /// 初始化磁盘信息服务
  /// 这是唯一需要在main.dart中调用的方法
  static Future<bool> initialize() async {
    if (_service != null && _service!.isInitialized) {
      return true;
    }

    // 如果服务未创建，自动创建默认服务
    _service ??= _createDefaultService();

    return await _service!.initialize();
  }

  /// 创建默认服务实例（内部方法）
  static DiskInfoService _createDefaultService() {
    try {
      // 动态导入具体实现，避免硬编码依赖
      final factory = DiskInfoServiceFactory();
      return factory.createDefault();
    } catch (e) {
      throw StateError('Failed to create default DiskInfoService: $e');
    }
  }

  // === 公开的API方法 ===

  /// 获取指定路径的磁盘类型
  static Future<DiskType?> getDiskType(String path) async {
    return await _internalService.getDiskType(path);
  }

  /// 获取指定路径的磁盘空间信息
  /// 返回包含总空间、可用空间、已用空间的详细信息
  static Future<DiskSpaceInfo?> getDiskSpace(String path) async {
    final spaceInfo = await _internalService.getDiskSpace(path);
    if (spaceInfo != null) {
      return spaceInfo;
    }

    return const DiskSpaceInfo(
      totalSpace: 1000000000000000000,
      freeSpace: 1000000000000000000,
      usedSpace: 0, // 这里应该返回实际的已用空间
    );
  }
  // === 测试和高级用法 ===

  /// 设置自定义服务实例（主要用于测试）
  static void setCustomService(DiskInfoService service) {
    _service = service;
  }

  /// 重置服务状态（主要用于测试）
  static void reset() {
    _service?.dispose();
    _service = null;
  }

  /// 检查服务是否已初始化
  static bool get isInitialized => _service?.isInitialized ?? false;

  /// 检查服务是否正在初始化
  static bool get isInitializing => _service?.isInitializing ?? false;
}

/// 简化的工厂类
class DiskInfoServiceFactory {
  DiskInfoService createDefault() {
    // 这里导入具体实现
    // 为了避免循环依赖，使用延迟加载
    if (Platform.isMacOS) {
      return _loadMacImplementation();
    }
    return _loadWinImplementation();
  }

  DiskInfoService _loadWinImplementation() {
    return WinDiskInfoServiceImpl.instance;
  }

  DiskInfoService _loadMacImplementation() {
    return MacDiskInfoServiceImpl();
  }
}
