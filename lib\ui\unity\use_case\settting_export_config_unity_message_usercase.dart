import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class SettingExportConfigUnityMessageUseCase {
  final SettingRepository _settingRepository;
  final UnityController _unityController;

  SettingExportConfigUnityMessageUseCase(
    this._settingRepository,
    this._unityController,
  );

  Future<void> invoke() async {
    try {
      final exportConfigObj = await _getExportConfigJson();
      String json = jsonEncode(exportConfigObj);
      MessageToUnity msg = MessageToUnity(
        method: 'SetupExportConfig',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );
      await _unityController.sendMessage(msg);
    } catch (e) {
      PGLog.e('setting export config message use case error : $e');
    }
  }

  // 获取导出配置的Json字符串
  Future<Map<String, dynamic>> _getExportConfigJson() async {
    final exportFileSetting = await _settingRepository.getExportFileSetting();
    final exportConfig =
        await _settingRepository.getExportConfig(ExportConfig.defaultConfig());
    Map<String, dynamic> exportConfigObj = exportConfig.toJson();
    exportConfigObj['folderSuffixName'] =
        exportFileSetting.exportFolderSuffix.isEmpty ||
                exportFileSetting.exportFolderSuffix.trim().isEmpty
            ? '_TuringArt'
            : exportFileSetting.exportFolderSuffix;
    exportConfigObj['isSameLevelExportPath'] =
        exportFileSetting.exportToOriginalFolder;
    exportConfigObj['exportFileType'] = ExportFileType.original.index;
    return exportConfigObj;
  }
}
