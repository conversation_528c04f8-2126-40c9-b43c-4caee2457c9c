import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/workspace_db_operater.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

// 文件资源相关的常量
class MediaResourceConstants {
  // 目录名常量
  static const String resourceDir = 'resource';
  static const String thumbDir = 'thumb';

  // 文件名常量（不含后缀）
  static const String universalMaskName = 'universal_mask';
  static const String interactiveMaskName = 'interactive_mask';
  static const String psMinIconName = 'ps_min_icon';
  static const String psMidIconName = 'ps_mid_icon';
  static const String psPreviewName = 'ps_preview';
  static const String psCoverName = 'ps_cover';
  static const String psLargeName = 'ps_large';
  static const String psUploadMaskName = 'ps_upload_mask';

  // 文件后缀常量
  static const String pngExtension = '.png';
  static const String jpgExtension = '.jpg';

  // 获取完整文件名的静态方法
  static String getUniversalMaskFileName() => '$universalMaskName$pngExtension';
  static String getInteractiveMaskFileName() =>
      '$interactiveMaskName$pngExtension';
  static String getPsMinIconFileName() => '$psMinIconName$jpgExtension';
  static String getPsMidIconFileName() => '$psMidIconName$pngExtension';
  static String getPsPreviewFileName() => '$psPreviewName$jpgExtension';
  static String getPsCoverFileName() => '$psCoverName$jpgExtension';
  static String getPsLargeFileName() => '$psLargeName$jpgExtension';
  static String getPsUploadMaskFileName() => '$psUploadMaskName$pngExtension';
}

enum IconType {
  minIcon,
  midIcon,
}

// 文件信息结构
class FileInfo {
  final String name; // 文件名（不含扩展名）
  final String extension; // 文件扩展名（包含点号）
  final String fullName; // 文件名（包含扩展名）

  FileInfo({
    required this.name,
    required this.extension,
    required this.fullName,
  });
}

class MediaRepositoryImpl implements MediaRepository {
  final DbOperater _dbOperater;
  final FileManager _fileManager;

  MediaRepositoryImpl({
    required DbOperater dbOperater,
    required FileManager fileManager,
  })  : _dbOperater = dbOperater,
        _fileManager = fileManager;

  @override
  Future<File?> addOrUpdateFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
    File resourceFile,
  ) async {
    try {
      // 确保照片目录存在
      await _fileManager.createPhotoDirectory(projectId, fileId);

      // 获取目标文件路径
      final targetFile = getResourceFilePath(projectId, fileId, resourceType);

      // 确保目标目录存在（更严格的检查）
      await _ensureDirectoryExists(targetFile.parent);

      return targetFile;
    } catch (e) {
      PGLog.e('添加/更新文件资源失败: $e');
      return null;
    }
  }

  @override
  Future<bool> deleteFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  ) async {
    try {
      final targetFile = getResourceFilePath(projectId, fileId, resourceType);

      // 确保父目录存在（如果不存在说明文件也不存在）
      if (!targetFile.parent.existsSync()) {
        PGLog.d('目录不存在，文件也不存在: ${targetFile.parent.path}');
        return true;
      }

      if (targetFile.existsSync()) {
        targetFile.deleteSync();
        return true;
      }
      return false;
    } catch (e) {
      PGLog.e('删除文件资源失败: $e');
      return false;
    }
  }

  @override
  File? getFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  ) {
    try {
      if (resourceType == MediaResourceType.originalResource) {
        throw Exception('原始资源需要异步获取，请使用 getFileResourceAsync');
      }

      final targetFile = getResourceFilePath(projectId, fileId, resourceType);

      // 先检查父目录是否存在
      if (!targetFile.parent.existsSync()) {
        PGLog.d('目录不存在，文件也不存在: ${targetFile.parent.path}');
        return null;
      }

      if (targetFile.existsSync()) {
        return targetFile;
      } else {
        PGLog.d('文件资源不存在: ${targetFile.path}');
        return null;
      }
    } catch (e) {
      PGLog.e('获取文件资源失败: $e');
      return null;
    }
  }

  @override
  Future<File?> getFileResourceAsync(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  ) async {
    try {
      if (resourceType == MediaResourceType.originalResource) {
        final workspaceFile =
            await _dbOperater.getWorkspaceFile(projectId, fileId);
        if (workspaceFile == null) {
          PGLog.d(
              'getFileResourceAsync workspaceFile is null, projectId: $projectId, fileId: $fileId');
          return null;
        }
        if (workspaceFile.orgPath.isEmpty) {
          PGLog.d(
              'getFileResourceAsync workspaceFile.orgPath is empty, projectId: $projectId, fileId: $fileId, workspaceFile: $workspaceFile');
          return null;
        }
        return File(FileManager().getFullPath(workspaceFile.orgPath));
      }

      final targetFile = getResourceFilePath(projectId, fileId, resourceType);

      // 先检查父目录是否存在
      if (!targetFile.parent.existsSync()) {
        PGLog.d('目录不存在，文件也不存在: ${targetFile.parent.path}');
        return null;
      }

      if (targetFile.existsSync()) {
        return targetFile;
      } else {
        PGLog.d('文件资源不存在: ${targetFile.path}');
        return null;
      }
    } catch (e) {
      PGLog.e('获取文件资源失败: $e');
      return null;
    }
  }

  // 根据资源类型获取对应的文件路径
  @override
  File getResourceFilePath(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  ) {
    final userRootDir = FileManager().getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }

    final photoDir = path.join(userRootDir.path, projectId, fileId);
    String subDir;
    String fileName;

    switch (resourceType) {
      case MediaResourceType.mask:
        subDir = MediaResourceConstants.resourceDir;
        fileName = MediaResourceConstants.getUniversalMaskFileName();
        break;
      case MediaResourceType.minIcon:
        subDir = MediaResourceConstants.thumbDir;
        fileName = MediaResourceConstants.getPsMinIconFileName();
        break;
      case MediaResourceType.midIcon:
        subDir = MediaResourceConstants.thumbDir;
        fileName = MediaResourceConstants.getPsMidIconFileName();
        break;
      case MediaResourceType.previewResource:
        subDir = MediaResourceConstants.thumbDir;
        fileName = MediaResourceConstants.getPsPreviewFileName();
        break;
      case MediaResourceType.largeResource:
        subDir = MediaResourceConstants.thumbDir;
        fileName = MediaResourceConstants.getPsLargeFileName();
        break;
      case MediaResourceType.coverResource:
        subDir = MediaResourceConstants.thumbDir;
        fileName = MediaResourceConstants.getPsCoverFileName();
        break;
      default:
        throw Exception('Invalid resource type: $resourceType');
    }

    final filePath = path.join(photoDir, subDir, fileName);
    return File(filePath);
  }

  // 根据文件生成工作区文件
  @override
  Future<List<WorkspaceFile>> generateWorkspaceFiles(
    String projectId,
    List<File> files,
  ) async {
    final workspaceFiles = <WorkspaceFile>[];

    for (int index = 0; index < files.length; index++) {
      final file = files[index];
      if (!file.existsSync()) {
        continue;
      }
      // 避免生成相同的时间戳
      final now = DateTime.now().millisecondsSinceEpoch + index;
      final fileStats = file.statSync();
      final fileInfo = _getFileInfo(file.path);

      if (fileInfo.extension.isEmpty) {
        continue;
      }
      final photoId = const Uuid().v4();
      // 这个流程暂时不关注文件夹的，所以异步调用不需要等结果
      FileManager().createPhotoDirectory(projectId, photoId);

      final workspaceFile = WorkspaceFile(
        fileId: photoId,
        workspaceId: projectId,
        edited: false,
        stars: 0,
        exported: false,
        orgPath: file.path,
        exportTime: 0,
        format: fileInfo.extension,
        broken: false,
        lastEditTime: now,
        createTime: now,
        size: fileStats.size,
        width: 0,
        height: 0,
        orientation: ExifOrientation.normal,
        iccType: ExifIccProfileType.unknown,
        isRaw: false,
        rawPath: file.path,
        converted: false,
        fileName: fileInfo.fullName,
        iconized: false,
        midIconized: false,
        isOverSize: false,
        faceCount: -1,
        binFormat: '',
        rawAutoExpose: false,
        rawAutoAdjustType: 0,
      );
      workspaceFiles.add(workspaceFile);
    }
    return workspaceFiles;
  }

  // 获取文件信息（文件名和后缀）
  FileInfo _getFileInfo(String filePath) {
    // 使用官方的 path 包函数
    final fileName = path.basename(filePath); // 获取文件名（包含扩展名）
    final extension = path.extension(filePath); // 获取扩展名（包含点号）
    final name = path.basenameWithoutExtension(filePath); // 获取文件名（不含扩展名）

    // 特殊处理以点开头的文件名（如 .jpg）
    // 这种情况下 path.extension() 返回空字符串，但我们需要提取扩展名
    if (extension.isEmpty && fileName.startsWith('.')) {
      return FileInfo(
        name: '', // 没有实际文件名
        extension: fileName, // 整个文件名就是扩展名（包含点号）
        fullName: fileName,
      );
    }

    return FileInfo(
      name: name,
      extension: extension,
      fullName: fileName,
    );
  }

  // 确保目录存在并且可写
  Future<void> _ensureDirectoryExists(Directory directory) async {
    try {
      if (!directory.existsSync()) {
        await directory.create(recursive: true);
        PGLog.d('创建目录: ${directory.path}');
      }

      // 验证目录是否真正创建成功并且可写
      if (!directory.existsSync()) {
        throw Exception('目录创建失败: ${directory.path}');
      }
    } catch (e) {
      PGLog.e('确保目录存在失败: ${directory.path}, 错误: $e');
      rethrow;
    }
  }
}
