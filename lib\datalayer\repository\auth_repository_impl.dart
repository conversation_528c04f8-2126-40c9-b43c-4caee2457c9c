import 'package:turing_art/datalayer/domain/models/login_info/login_info.dart';
import 'package:turing_art/datalayer/domain/models/token/token.dart';
import 'package:turing_art/datalayer/domain/models/user/creator.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/api_error_service.dart';
import 'package:turing_art/datalayer/service/api/header_tool.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/datalayer/service/database/dao/creator_info_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/user_dao.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../domain/models/account/account.dart';
import '../service/database/database.dart';
import 'auth_repository.dart';

/// 认证仓库实现类，负责处理用户认证相关的业务逻辑
///
/// 主要功能：
/// - 用户登录/登出
/// - Token 刷新
/// - 验证码发送
/// - 用户状态管理
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;
  final DataBase _db;

  /// 创建 [AuthRepositoryImpl] 实例
  ///
  /// [apiClient] API 客户端，用于网络请求
  /// [db] 数据库实例，用于本地数据存储
  AuthRepositoryImpl({
    required ApiClient apiClient,
    required DataBase db,
  })  : _apiClient = apiClient,
        _db = db;

  /// 用户登录
  ///
  /// [phoneNumber] 手机号码
  /// [code] 验证码
  ///
  /// 返回登录成功的用户信息，失败抛出 LoginException
  @override
  Future<LoginInfo?> logIn(
    String phoneNumber,
    String? cc,
    String code,
    String storeId,
  ) async {
    final data = {
      'mobile': phoneNumber,
      'verifyCode': code,
      'cc': cc ?? '86',
      'storeId': storeId,
    };

    final sortedData = Map.fromEntries(
        data.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));

    try {
      final headers = RequestHeader.getSignedHeaders(
        '/v1/account/login/verify-code',
        HttpMethod.post,
        sortedData,
        forBody: true,
      );
      final response = await _apiClient.post<dynamic>(
          '/v1/account/login/verify-code',
          headers: headers,
          data: sortedData);

      if (response.success && response.data != null) {
        final userMap = response.data as Map<String, dynamic>;
        if (storeId.isNotEmpty && userMap['user'] != null) {
          userMap['user']['lastLoginStoreId'] = storeId;
        }
        final loginInfo = LoginInfo.fromJson(userMap);
        await _saveCreatorInfo(loginInfo.creator, loginInfo.user.userId);

        await _saveUser(loginInfo.user);
        PGLog.d(
            'AuthRepositoryImpl - logIn 登录成功,isfirstlogin: ${loginInfo.user.firstLogin} storeId: ${loginInfo.store.id}');
        return loginInfo;
      } else {
        ApiErrorService.handleLoginError(response);
        return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
      }
    } catch (e, stack) {
      ApiErrorService.handleException(e, stack, 'Login failed');
      return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
    }
  }

  /// 获取账户列表
  @override
  Future<List<Account>> getAccountList(String phoneNumber) async {
    final data = {'mobile': phoneNumber};
    final headers = RequestHeader.getSignedHeaders(
      '/v1/account/available-store',
      HttpMethod.get,
      data,
      forBody: false,
    );
    final response = await _apiClient.get<dynamic>(
      '/v1/account/available-store',
      headers: headers,
      queryParameters: data,
    );
    if (response.success) {
      List<Map<String, dynamic>> data = (response.data['items'] as List)
          .map((item) => item as Map<String, dynamic>)
          .toList();
      final accountList = Account.fromJsonList(data);
      return accountList;
    }
    return [];
  }

  /// 刷新用户 Token
  ///
  /// 当 Token 即将过期时调用此方法刷新
  /// 返回刷新是否成功
  @override
  Future<User?> refreshUserToken(User user) async {
    try {
      final headers = RequestHeader.getSignedHeaders(
        '/v1/account/login/refresh-token',
        HttpMethod.post,
        {},
        forBody: true,
      );
      final response = await _apiClient.post<dynamic>(
          '/v1/account/login/refresh-token',
          headers: headers,
          data: {});

      if (response.success && response.data != null) {
        final tokenData = Token.fromJson(response.data as Map<String, dynamic>);
        final currentUser = user;
        final updatedUser = currentUser.copyWith(
          token: tokenData.token,
          tokenEnd: tokenData.tokenEnd,
        );
        await _saveUser(updatedUser);
        return updatedUser;
      } else {
        ApiErrorService.handleLoginError(response);
        return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
      }
    } catch (e, stack) {
      ApiErrorService.handleException(e, stack, 'Refresh token failed');
      return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
    }
  }

  /// 用户登出
  ///
  /// 清除本地用户状态并调用登出接口
  @override
  Future<void> logOut(String userId) async {
    if (userId.isEmpty) {
      return;
    }
    try {
      final data = {'cid': userId};
      final headers = RequestHeader.getSignedHeaders(
        '/v1/account/login/logout',
        HttpMethod.post,
        data,
        forBody: true,
      );
      await _apiClient.post('/v1/account/login/logout',
          headers: headers, data: data);
    } catch (e, stack) {
      PGLog.e('Logout API call failed: $e\n$stack');
    }
  }

  /// 发送登录验证码
  ///
  /// [phoneNumber] 手机号码
  /// 返回发送是否成功
  @override
  Future<bool> sendVerificationCode(String phoneNumber, String? cc) async {
    final data = {
      'cc': cc ?? '86',
      'isVoice': '0',
      'mobile': phoneNumber,
      'type': 'login',
    };

    try {
      final headers = RequestHeader.getSignedHeaders(
        '/v1/account/mobile/verify-code/send',
        HttpMethod.post,
        data,
        forBody: true,
      );
      final response = await _apiClient.post<dynamic>(
        '/v1/account/mobile/verify-code/send',
        headers: headers,
        data: data,
      );

      if (response.success) {
        return true;
      } else {
        ApiErrorService.handleLoginError(response);
        return false; // 这行代码实际上不会执行，因为上面的方法会抛出异常
      }
    } catch (e, stack) {
      ApiErrorService.handleException(
          e, stack, 'Send verification code failed');
      return false; // 这行代码实际上不会执行，因为上面的方法会抛出异常
    }
  }

  /// 获取最后登录的门店ID
  @override
  Future<String> getLastLoginStoreIdByPhoneNumber(String phoneNumber) async {
    final userList = await _db.getUserList(phoneNumber: phoneNumber);
    return userList
            ?.where((element) =>
                element.id == UserPreferencesService.getLastLoginEmployeeId())
            .firstOrNull
            ?.lastLoginStoreId ??
        '';
  }

  /// 保存用户信息
  ///
  /// [user] 要保存的用户信息
  /// 同时更新认证状态和数据库
  Future<void> _saveUser(User user) async {
    await _addOrUpdateUserToDB(user);
  }

  /// 更新或插入用户信息到数据库
  ///
  /// [user] 要保存的用户信息
  Future<void> _addOrUpdateUserToDB(User user) async {
    // 插入数据库
    await _db.upsertUser(user.toEntity());
  }

  /// 保存主账号信息
  Future<void> _saveCreatorInfo(Creator creator, String uid) async {
    await _db.upsertCreatorInfo(creator.toEntity(uid));
  }
}
