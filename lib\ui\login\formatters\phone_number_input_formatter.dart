import 'package:flutter/services.dart';

/// 手机号码输入格式化器
/// 支持输入数字和一个唯一特殊字符"+"
/// 输入中有特殊字符后取消输入位数限制，否则按原有逻辑有位数限制
/// 输入字符开始和结尾都不能为"+"，否则输入无效
/// 在输入过程中检查特殊字符串"+"个数，大于1则不能继续输入"+"
class PhoneNumberInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;

    // 如果新文本为空，允许
    if (newText.isEmpty) {
      return newValue;
    }

    // 检查是否只包含数字和"+"
    final validChars = RegExp(r'^[0-9+]*$');
    if (!validChars.hasMatch(newText)) {
      // 如果包含无效字符，返回旧值
      return oldValue;
    }

    // 检查"+"的个数，最多只能有1个
    final plusCount = '+'.allMatches(newText).length;
    if (plusCount > 1) {
      // 如果"+"超过1个，返回旧值
      return oldValue;
    }

    // 如果包含"+"，则为海外格式，不限制长度
    if (newText.contains('+')) {
      return newValue;
    } else {
      // // 大陆地区手机号，限制11位数字
      // if (newText.length > 11) {
      //   // 超过11位，返回旧值
      //   return oldValue;
      // }
      return newValue;
    }
  }
}
