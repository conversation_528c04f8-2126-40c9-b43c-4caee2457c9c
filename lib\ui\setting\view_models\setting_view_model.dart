import 'package:flutter/material.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_category.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/setting/use_case/fetch_settings_use_case.dart';
import 'package:turing_art/ui/setting/use_case/save_resolution_use_case.dart';
import 'package:turing_art/ui/setting/use_case/save_setting_use_case.dart';
import 'package:turing_art/ui/unity/use_case/settting_export_config_unity_message_usercase.dart';
import 'package:turing_art/utils/pg_log.dart';

class SettingViewModel extends ChangeNotifier {
  final FetchSettingsUseCase _fetchSettingsUseCase;
  final SaveSettingUseCase _saveSettingUseCase;
  final SaveResolutionUseCase _saveResolutionUseCase;

  final SettingExportConfigUnityMessageUseCase
      _settingExportConfigUnityMessageUseCase;
  final DiskCacheManager _diskCacheManager;
  SettingConfig _settingConfig = const SettingConfig();
  bool _isLoading = true; // 添加加载状态标志
  bool _isClearingCache = false; // 清理缓存状态标志
  bool _isGettingCacheSize = false; // 获取缓存大小状态标志

  bool get isLoading => _isLoading;
  bool get isClearingCache => _isClearingCache;
  bool get isGettingCacheSize => _isGettingCacheSize;
  List<SettingCategory> get categories => _settingConfig.categories
      .where((category) => category.isShowForUser)
      .toList();

  // 获取所有分类（包括隐藏的），用于内部操作后保存，以免下次升级
  List<SettingCategory> get _allCategories => _settingConfig.categories;

  SettingViewModel(
    this._fetchSettingsUseCase,
    this._saveSettingUseCase,
    this._saveResolutionUseCase,
    this._settingExportConfigUnityMessageUseCase,
    this._diskCacheManager,
  ) {
    _init();
    _listenToCacheManagerState();
  }

  Future<void> _init() async {
    _isLoading = true;
    // notifyListeners();
    await _loadSettingConfig();
    _isLoading = false;
    notifyListeners();
  }

  /// 监听缓存管理器状态变化
  void _listenToCacheManagerState() {
    _diskCacheManager.stateStream.listen((state) {
      switch (state) {
        case CacheManagerState.idle:
          _isClearingCache = false;
          _isGettingCacheSize = false;
          break;
        case CacheManagerState.clearingCache:
          _isClearingCache = true;
          _isGettingCacheSize = false;
          break;
        case CacheManagerState.gettingCacheSize:
          _isClearingCache = false;
          _isGettingCacheSize = true;
          break;
      }
      notifyListeners();
    });
  }

  // 加载设置
  Future<void> _loadSettingConfig() async {
    try {
      _settingConfig = await _fetchSettingsUseCase.invoke();
    } catch (e) {
      PGLog.e('Error loading setting config: $e');
    }
  }

  // 保存设置
  Future<void> saveSetting() async {
    try {
      await _saveSettingUseCase.invoke(_settingConfig);
    } catch (e) {
      PGLog.e('Error saving settings: $e');
      rethrow;
    }
  }

  Future<void> saveExportConfigToUnity() async {
    try {
      await _settingExportConfigUnityMessageUseCase.invoke();
    } catch (e) {
      PGLog.e('Error saving export config: $e');
    }
  }

  // 保存分辨率设置
  Future<void> saveResolution() async {
    try {
      await _saveResolutionUseCase.invoke(_settingConfig);
    } catch (e) {
      PGLog.e('Error saving settings: $e');
      rethrow;
    }
  }

  List<SettingCategory> getCategories() {
    return categories;
  }

  // 获取特定类别的设置项
  List<SettingItemModel> getItemsByCategory(String categoryKey) {
    try {
      final category = categories.firstWhere(
        (category) => category.key == categoryKey,
        orElse: () => const SettingCategory(
          title: '',
          key: 'default_category',
          items: [],
        ),
      );
      return category.items;
    } catch (e) {
      PGLog.e('Error getting items for category $categoryKey: $e');
      return []; // Return empty list instead of throwing
    }
  }

  // 更新选项的选中状态
  void updateChoiceSelection(
      String categoryKey, String itemKey, String choiceValue) {
    try {
      // 使用所有分类（包括隐藏的）进行更新，确保隐藏分类不丢失
      final categoryIndex =
          _allCategories.indexWhere((c) => c.key == categoryKey);
      if (categoryIndex == -1) {
        PGLog.w('Category not found: $categoryKey');
        return;
      }

      final category = _allCategories[categoryIndex];
      final itemIndex = category.items.indexWhere((i) => i.key == itemKey);
      if (itemIndex == -1) {
        PGLog.w('Item not found: $itemKey in category $categoryKey');
        return;
      }

      final item = category.items[itemIndex];

      // 更新选中状态
      final updatedChoices = item.choices.map((choice) {
        return choice.copyWith(isSelected: choice.value == choiceValue);
      }).toList();

      // 更新item的value
      final updatedItem = item.copyWith(
        value: choiceValue,
        choices: updatedChoices,
      );

      // 更新整个配置
      final updatedItems = List<SettingItemModel>.from(category.items);
      updatedItems[itemIndex] = updatedItem;

      final updatedCategory = category.copyWith(items: updatedItems);

      // 使用所有分类进行更新，保持隐藏分类不丢失
      final updatedCategories = List<SettingCategory>.from(_allCategories);
      updatedCategories[categoryIndex] = updatedCategory;

      _settingConfig = _settingConfig.copyWith(categories: updatedCategories);

      notifyListeners();
    } catch (e) {
      PGLog.e('Error updating choice selection: $e');
    }
  }

  Future<bool> clearCache() async {
    try {
      await _diskCacheManager.cleanupFull();
      return true;
    } catch (e) {
      PGLog.e('Error clearing cache: $e');
      return false;
    }
  }

  Future<String> getCurrentCacheSize() async {
    final sizeInBytes = await _diskCacheManager.getCacheSize();
    final sizeInMB = sizeInBytes / (1024 * 1024); // Convert bytes to MB

    if (sizeInMB < 1024) {
      return '${sizeInMB.toStringAsFixed(2)} MB';
    } else {
      final sizeInGB = sizeInMB / 1024; // Convert MB to GB
      return '${sizeInGB.toStringAsFixed(2)} GB';
    }
  }

  // 私有方法：根据分类和项目键获取设置项
  SettingItemModel? _findSettingItem(String categoryKey, String itemKey) {
    try {
      final items = getItemsByCategory(categoryKey);
      final item = items.firstWhere(
        (item) => item.key == itemKey,
        orElse: () => const SettingItemModel(
          title: '',
          key: '',
          value: '',
          type: '',
          choices: [],
        ),
      );

      return item.key.isNotEmpty ? item : null;
    } catch (e) {
      PGLog.e('Error finding setting item for $categoryKey.$itemKey: $e');
      return null;
    }
  }

  // 获取设置项的选中索引
  int getSelectedIndex(String categoryKey, String itemKey) {
    final item = _findSettingItem(categoryKey, itemKey);
    if (item == null) {
      return 0;
    }

    // 查找选中的索引
    int index = item.choices.indexWhere((choice) => choice.isSelected);
    return index >= 0 ? index : 0;
  }

  int getSelectedIndexByItem(SettingItemModel settingItem) {
    final index = settingItem.choices.indexWhere((choice) => choice.isSelected);
    return index >= 0 ? index : 0;
  }

  // 根据设置项获取SettingItemModel
  SettingItemModel? getSettingItem(String categoryKey, String itemKey) {
    return _findSettingItem(categoryKey, itemKey);
  }

  SettingCategory? getCategory(String categoryKey) {
    return categories.firstWhere(
      (category) => category.key == categoryKey,
      orElse: () => const SettingCategory(title: '', key: '', items: []),
    );
  }

  // 更新设置
  void updateSetting(String itemKey, String value) {
    // Find the category that contains this item
    final category = categories.firstWhere(
      (category) => category.items.any((item) => item.key == itemKey),
      orElse: () => const SettingCategory(title: '', key: '', items: []),
    );

    if (category.key.isEmpty) {
      PGLog.w('Category not found for item: $itemKey');
      return;
    }

    updateChoiceSelection(category.key, itemKey, value);
  }
}
