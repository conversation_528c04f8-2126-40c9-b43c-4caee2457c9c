import 'dart:async';

import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_result/cache_cleanup_result.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_config/cache_config.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_server/cache_service.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缓存管理器
/// 负责管理所有注册的缓存服务，提供统一的清理接口
class DiskCacheManagerImpl implements DiskCacheManager {
  DiskCacheManagerImpl({
    CacheConfig? cacheConfig,
  }) : _config = cacheConfig ?? const CacheConfig() {
    _loadCleanupTimestamps();
  }

  /// 注册的缓存服务
  final Map<String, CacheService> _services = {};

  /// 当前配置
  CacheConfig _config;

  /// 当前工作状态
  CacheManagerState _currentState = CacheManagerState.idle;

  /// 工作状态控制器
  final StreamController<CacheManagerState> _stateController =
      StreamController<CacheManagerState>.broadcast();

  /// 最后一次清理结果
  CacheCleanupResult? _lastCleanupResult;

  /// 最后一次清理的时间戳
  DateTime _lastCleanup = DateTime.now();

  /// 获取当前配置
  CacheConfig get config => _config;

  /// 获取当前配置
  @override
  CacheConfig get currentCacheConfig => _config;

  /// 当前工作状态
  @override
  CacheManagerState get currentState => _currentState;

  /// 工作状态变化流
  @override
  Stream<CacheManagerState> get stateStream => _stateController.stream;

  /// 是否正在清理缓存
  @override
  bool get isClearingCache => _currentState == CacheManagerState.clearingCache;

  /// 是否正在获取缓存大小
  @override
  bool get isGettingCacheSize =>
      _currentState == CacheManagerState.gettingCacheSize;

  /// 最后一次清理结果
  CacheCleanupResult? get lastCleanupResult => _lastCleanupResult;

  /// 注册的缓存服务列表
  List<CacheService> get services => _services.values.toList();

  /// 关闭条件清理功能仅保留手动清理
  final bool _enableConditionalCleanup = false;

  /// 注册缓存服务
  @override
  void registerService(CacheService service) {
    if (_services.containsKey(service.serviceName)) {
      PGLog.w('CacheManager: 服务 ${service.serviceName} 已存在，将被覆盖');
    }

    _services[service.serviceName] = service;
    PGLog.i('CacheManager: 注册缓存服务 ${service.serviceName}，等级: ${service.level}');
  }

  /// 注销缓存服务
  @override
  void unregisterService(String serviceName) {
    if (_services.remove(serviceName) != null) {
      PGLog.i('CacheManager: 注销缓存服务 $serviceName');
    }
  }

  /// 获取指定服务
  @override
  CacheService? getService(String serviceName) {
    return _services[serviceName];
  }

  /// 更新配置
  @override
  void updateCacheConfig(CacheConfig newConfig,
      {bool tryCleanupWithConfig = false}) async {
    try {
      // 如果配置发生变化，触发一次条件清理检查
      if (_enableConditionalCleanup &&
          (_config != newConfig || (await _shouldCleanup(newConfig)))) {
        _config = newConfig;
        PGLog.i('CacheManager: 配置变化触发条件清理检查');
        if (tryCleanupWithConfig) {
          await tryCleanupConditional();
        }
      }
    } catch (e) {
      PGLog.e('CacheManager: 更新配置失败: $e');
    }
  }

  /// 检查是否需要触发清理
  @override
  Future<CacheCleanupResult> tryCleanupConditional() async {
    if (_currentState == CacheManagerState.clearingCache) {
      return CacheCleanupResult(
        timestamp: DateTime.now(),
        mode: CacheCleanupMode.full,
        totalDeletedSize: 0,
        serviceResults: {},
        errorMessage: '清理操作正在进行中',
      );
    }

    bool shouldCleanup = await _shouldCleanup(_config);
    if (!shouldCleanup) {
      return CacheCleanupResult(
        timestamp: DateTime.now(),
        mode: CacheCleanupMode.full,
        totalDeletedSize: 0,
        serviceResults: {},
        errorMessage: '无需清理',
      );
    }

    _currentState = CacheManagerState.clearingCache;
    _stateController.add(_currentState);

    try {
      return await _executeFullCleanup();
    } finally {
      _currentState = CacheManagerState.idle;
      _stateController.add(_currentState);
    }
  }

  /// 全部清除模式
  /// 清理所有注册服务的缓存
  @override
  Future<CacheCleanupResult> cleanupFull() async {
    if (_currentState == CacheManagerState.clearingCache) {
      return CacheCleanupResult(
        timestamp: DateTime.now(),
        mode: CacheCleanupMode.full,
        totalDeletedSize: 0,
        serviceResults: {},
        errorMessage: '清理操作正在进行中',
      );
    }

    _currentState = CacheManagerState.clearingCache;
    _stateController.add(_currentState);
    // 这里的代码非常垃圾，暂时只能这样实现，后续需要进一步优化
    // 这里需要暂停所有导出任务，然后执行清理，清理完成后恢复导出任务，意味着存在一定的时间成本，而从客户端的角度无法准确获取导出任务的变更，只能暂时以delay的方式去赌，后期在Unity中添加淘汰资源后再对其进行优化
    await Future.delayed(const Duration(seconds: 5));

    try {
      final result = await _executeFullCleanup();
      _lastCleanupResult = result;
      return result;
    } finally {
      _currentState = CacheManagerState.idle;
      _stateController.add(_currentState);
    }
  }

  @override
  Future<int> getCacheSize() async {
    if (_currentState == CacheManagerState.gettingCacheSize) {
      // 如果已经在获取缓存大小，直接返回当前结果
      return await _getTotalCacheSize();
    }

    _currentState = CacheManagerState.gettingCacheSize;
    _stateController.add(_currentState);

    try {
      return await _getTotalCacheSize();
    } finally {
      _currentState = CacheManagerState.idle;
      _stateController.add(_currentState);
    }
  }

  @override
  Future<int> getCacheSizeByService(String serviceName) async {
    final service = services.firstWhere(
      (service) => service.serviceName == serviceName,
      orElse: () => throw Exception('服务 $serviceName 不存在'),
    );
    return await service.getCacheSize();
  }

  /// 执行全部清理
  Future<CacheCleanupResult> _executeFullCleanup() async {
    final timestamp = DateTime.now();
    final serviceResults = <String, ServiceCleanupResult>{};
    int totalDeletedSize = 0;
    String? errorMessage;

    try {
      for (final service in services) {
        try {
          final deletedResult = await service.clearCache();
          if (deletedResult.deletedSize > 0) {
            serviceResults[service.serviceName] = deletedResult;
            totalDeletedSize += deletedResult.deletedSize;

            PGLog.i(
                'CacheManager: 服务 ${service.serviceName} 清理了 ${(deletedResult.deletedSize / 1024 / 1024).toStringAsFixed(2)}MB');
          }
        } catch (e) {
          PGLog.e('CacheManager: 清理服务 ${service.serviceName} 时出错: $e');
          errorMessage = '清理服务 ${service.serviceName} 时出错: $e';
        }
      }

      PGLog.i(
          'CacheManager: 全部清理完成，总共清理了 ${(totalDeletedSize / 1024 / 1024).toStringAsFixed(2)}MB');
    } catch (e) {
      PGLog.e('CacheManager: 全部清理执行出错: $e');
      errorMessage = '全部清理执行出错: $e';
    }

    if (totalDeletedSize > 0) {
      _lastCleanup = timestamp;
      await _saveCleanupTimestamps();
      PGLog.i('CacheManager: 更新清理时间戳');
    }

    return CacheCleanupResult(
      timestamp: timestamp,
      mode: CacheCleanupMode.full,
      totalDeletedSize: totalDeletedSize,
      serviceResults: serviceResults,
      errorMessage: errorMessage,
    );
  }

  /// 检查是否需要清理
  /// 返回清理类型，如果不需要清理则返回null
  Future<bool> _shouldCleanup(CacheConfig config) async {
    if (!_enableConditionalCleanup) {
      return false;
    }

    PGLog.i(
        'CacheManager: 根据当前配置信息进行清理检查，清理阈值大小: ${(config.cleanupThresholdSize / 1024 / 1024 / 1024).toStringAsFixed(2)}GB, 清理天数: ${config.cleanupDays.duration.inDays} 天');

    // 检查时间条件：距离上次时间清理是否超过配置的天数
    final timeSinceLastCleanup = DateTime.now().difference(_lastCleanup);
    if (timeSinceLastCleanup > config.cleanupDays.duration) {
      PGLog.i(
          'CacheManager: 时间条件触发，距离上次时间清理 ${timeSinceLastCleanup.inDays} 天，超过阈值 ${config.cleanupDays.duration.inDays} 天');
      return true;
    }

    // 检查大小条件：当前缓存大小是否超过阈值
    final totalSize = await _getTotalCacheSize();
    PGLog.i(
        'CacheManager: 当前缓存大小: ${(totalSize / 1024 / 1024 / 1024).toStringAsFixed(2)}GB');
    if (totalSize > config.cleanupThresholdSize) {
      PGLog.i(
          'CacheManager: 大小条件触发，缓存大小 ${(totalSize / 1024 / 1024 / 1024).toStringAsFixed(2)}GB 超过阈值 ${(config.cleanupThresholdSize / 1024 / 1024 / 1024).toStringAsFixed(2)}GB');
      return true;
    }

    return false;
  }

  /// 获取总缓存大小
  Future<int> _getTotalCacheSize() async {
    final startTime = DateTime.now();
    int totalSize = 0;
    PGLog.i(
        'CacheManager: 获取总缓存大小开始，耗时: ${startTime.difference(DateTime.now()).inMilliseconds}ms');
    for (final service in services) {
      try {
        totalSize += await service.getCacheSize();
      } catch (e) {
        PGLog.e('CacheManager: 获取服务 ${service.serviceName} 缓存大小时出错: $e');
      }
    }
    PGLog.i(
        'CacheManager: 获取总缓存大小结束，总大小: $totalSize, 耗时: ${DateTime.now().difference(startTime).inMilliseconds}ms');
    return totalSize;
  }

  /// 获取总缓存大小
  Future<int> getTotalCacheSize() async {
    return await _getTotalCacheSize();
  }

  /// 加载清理时间戳
  Future<void> _loadCleanupTimestamps() async {
    try {
      final lastTimeCleanupStr =
          SharedPreferencesService.getCacheLastTimeCleanup();

      if (lastTimeCleanupStr.isNotEmpty) {
        _lastCleanup = DateTime.parse(lastTimeCleanupStr);
        PGLog.i('CacheManager: 加载时间清理时间戳: $_lastCleanup');
      }
    } catch (e) {
      PGLog.e('CacheManager: 加载清理时间戳失败: $e');
    }
  }

  /// 保存清理时间戳
  Future<void> _saveCleanupTimestamps() async {
    try {
      await SharedPreferencesService.setCacheLastTimeCleanup(
          _lastCleanup.toIso8601String());
      PGLog.i('CacheManager: 保存清理时间戳成功');
    } catch (e) {
      PGLog.e('CacheManager: 保存清理时间戳失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _stateController.close();
  }
}
