/// 兑换次数类型
enum ExchangeCountType {
  /// 精修次数
  exportCount('export_count', '张'),

  /// 小样次数
  sampleCount('sample_count', ''),

  /// AIGC次数
  aigcCount('aigc_count', '个AI积分');

  final String value;
  final String description;
  const ExchangeCountType(this.value, this.description);

  static ExchangeCountType fromString(String value) {
    return ExchangeCountType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ExchangeCountType.exportCount, // 默认返回精修次数
    );
  }
}
