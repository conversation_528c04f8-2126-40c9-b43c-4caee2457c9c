import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/collect/pagecost/page_home_sub_account.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_log.dart';

class EmployeeDialogViewModel extends ChangeNotifier {
  final EmployeeRepository _employeeRepository;
  final AccountRepository _accountRepository;
  final CurrentUserRepository _currentUserRepository;

  int _startTime = 0;

  // 滚动控制器
  final ScrollController scrollController = ScrollController();

  // 子账号汇总信息
  EmployeeSummary? get employeeSummary => _employeeRepository.usedSummary;

  // 子账号列表
  EmployeeList? get employeeList => _employeeRepository.subAcountList;

  // 子账号是否为空
  bool get isEmpty => employeeList == null || employeeList!.users.isEmpty;

  // 子账号变更事件流（服务端返回数据包含了实时的account）
  late StreamSubscription<String> _subAccountChangeSubscription;

  EmployeeDialogViewModel(this._employeeRepository, this._accountRepository,
      this._currentUserRepository) {
    _startTime = DateTimeUtil.getCurrentTimestampSec();
    // 初始化子账号变更事件流
    _initSubAccountChange();

    // 初始化时获取数据
    _loadData();
  }

  // 初始化子账号变更事件流
  void _initSubAccountChange() {
    _subAccountChangeSubscription = _employeeRepository.employeeInfoChange
        .listen(_handleSubAccountChangeEvent);
  }

  // 处理子账号变更事件
  void _handleSubAccountChangeEvent(String event) {
    PGLog.d('子账号变更事件: $event');
    // 记录刷新前滚动位置
    final scrollOffset =
        scrollController.hasClients ? scrollController.offset : 0;
    // 通知UI更新
    notifyListeners();

    // 恢复滚动位置
    if (scrollController.hasClients) {
      // 使用 Future.microtask 确保在布局更新后恢复滚动位置
      Future.microtask(() {
        if (scrollController.hasClients && scrollOffset > 0) {
          scrollController.jumpTo(scrollOffset.toDouble());
        }
      });
    }
  }

  // 刷新一次UI展示数据(只有发生操作的时候刷新一次)
  void _loadData() {
    _accountRepository.refreshAllAccount();
    _employeeRepository.getEmployeeSummary();
    _employeeRepository.getEmployeeList();
  }

  // 修改子账号名称
  Future<bool> updateEmployeeName(User user, String newName) async {
    if (newName.isEmpty) {
      return false;
    }

    try {
      final result = await _employeeRepository.updateEmployee(
        id: user.id,
        nickname: newName,
        enable: user.enable, // 保持当前启用状态
      );

      if (result) {
        // 刷新数据
        _loadData();
        return true;
      }
      return false;
    } catch (e) {
      PGLog.e('修改子账号名称失败: $e');
      return false;
    }
  }

  // 启用/停用子账号
  Future<bool> toggleEmployeeStatus(User user) async {
    try {
      final result = await _employeeRepository.updateEmployee(
        id: user.id,
        enable: !user.enable,
      );

      if (result) {
        // 刷新数据
        _loadData();
        return true;
      }
      return false;
    } catch (e) {
      PGLog.e('修改子账号状态失败: $e');
      return false;
    }
  }

  @override
  void dispose() {
    final duration = DateTimeUtil.getCurrentTimestampSec() - _startTime;
    recordPageHomeSubAccount(
        userId: _currentUserRepository.user?.effectiveId ?? '',
        stTime: _startTime.toString(),
        duration: duration.toString());
    // 取消子账号变更事件流
    _subAccountChangeSubscription.cancel();
    // 释放控制器
    scrollController.dispose();

    super.dispose();
  }
}
