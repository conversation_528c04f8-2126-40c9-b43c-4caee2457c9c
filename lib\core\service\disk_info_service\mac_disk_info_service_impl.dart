import 'dart:async';

import 'package:disk_space_util/disk_space_util.dart';

import '../../../utils/pg_log.dart';
import 'disk_info_service.dart';
import 'models/disk_info_models.dart';

/// Mac 磁盘信息服务的实现
class MacDiskInfoServiceImpl implements DiskInfoService {
  static final MacDiskInfoServiceImpl _instance = MacDiskInfoServiceImpl._();

  factory MacDiskInfoServiceImpl() => _instance;

  MacDiskInfoServiceImpl._();

  @override
  bool get isInitialized => true;

  @override
  bool get isInitializing => false;

  @override
  Future<bool> initialize() async {
    // do nothing
    return true;
  }

  @override
  Future<DiskType?> getDiskType(String path) async {
    return DiskType.ssd;
  }

  @override
  Future<DiskSpaceInfo?> getDiskSpace(String path) async {
    try {
      // 使用DiskInfoNativeClient获取磁盘空间信息
      final spaceInfo = await DiskSpaceUtil.getDiskSpace();
      PGLog.d("MacDiskInfoServiceImpl getDiskSpace  ${spaceInfo?.totalSpace}  ${spaceInfo?.freeSpace}");
      if (spaceInfo != null) {
        return DiskSpaceInfo(
          totalSpace: spaceInfo.totalSpace,
          freeSpace: spaceInfo.freeSpace,
          usedSpace: spaceInfo.totalSpace - spaceInfo.freeSpace,
        );
      }
    } catch (e) {
      PGLog.e('DiskInfoService: 获取磁盘空间失败: $e');
    }
    return null;
  }

  @override
  void reset() {
    // do nothing
  }

  @override
  void dispose() {
    // do nothing
  }
}
