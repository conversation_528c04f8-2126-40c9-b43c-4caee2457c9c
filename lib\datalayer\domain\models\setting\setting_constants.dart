/// 设置类别
class SettingCategoryConstant {
  // 预览设置（新旧版本一致没有改动）
  static const String preview = 'preview_setting';
  // 项目创建设置（新增）
  static const String projectCreation = 'project_creation_setting';
  // 导出设置（创建项目在使用，保留）
  static const String export = 'export_setting';
  // 导出文件设置（新增并展示）
  static const String exportFile = 'export_setting_file';
  // 缓存设置（UI上显示，以前数据里有个cache_setting，现在改成cache_new_setting，这个版本后以前的数据清理，没有地方使用）
  static const String cache = 'cache_new_setting';
  // 触控板模式设置
  static const String touchpadMode = 'touchpad_mode_setting';
}

/// 设置项Key
class SettingKeyConstant {
  // 预览设置
  static const String previewSize = 'preview_size';

  // 项目创建设置
  static const String projectCreationMode = 'project_creation_mode';

  // 导出设置
  static const String exportQuality = 'export_quality';

  // 导出路径
  static const String exportPath = 'export_path';
  // 导出文件设置项
  static const String exportToOriginalFolder = 'export_to_original_folder';
  static const String exportFolderSuffix = 'export_folder_suffix';

  // 缓存设置项
  static const String cacheDays = 'cache_days';
  static const String autoCleanSize = 'auto_clean_size';
  static const String cachePath = 'cache_path';
  static const String cacheClear = 'cache_clear';

  // 触控板模式设置项
  static const String touchpadModeEnabled = 'touchpad_mode_enabled';
}

/// 导出质量
class ExportQualityConstant {
  static const String lossless = 'lossless';
  static const String high = 'high';
  static const String medium = 'medium';
  static const String low = 'low';

  static int toValue(String quality) {
    switch (quality) {
      case lossless:
        return 100;
      case high:
        return 90;
      case medium:
        return 60;
      case low:
        return 30;
      default:
        return 90; // 默认质量
    }
  }
}

/// 开关状态
class SwitchState {
  static const String on = '1';
  static const String off = '0';
}

/// 设置项UI类型
class SettingTypeConstant {
  static const String select = 'select'; // 下拉选择框（如分辨率选择）
  static const String radio = 'radio'; // 单选按钮组（如项目创建模式）
  static const String text = 'text'; // 文本输入框（用户可输入自定义值）
  static const String click = 'click'; // 点击按钮（如清除缓存等操作）
  static const String toggle = 'toggle'; // 开关按钮（如触控板模式开关）
}

/// 项目创建模式枚举
enum ProjectCreationMode {
  single('single'), // 放入一个项目中
  multiple('multiple'); // 分别创建项目

  const ProjectCreationMode(this.value);
  final String value;

  static ProjectCreationMode fromValue(String value) {
    return ProjectCreationMode.values.firstWhere(
      (mode) => mode.value == value,
      orElse: () => ProjectCreationMode.single,
    );
  }
}

/// 缓存天数常量
enum CacheSettingDaysType {
  days3(3, '3天'),
  days7(7, '7天'),
  days15(15, '15天'),
  days30(30, '30天');

  final int value;
  final String displayText;
  const CacheSettingDaysType(this.value, this.displayText);
  static CacheSettingDaysType fromValue(int value) {
    return CacheSettingDaysType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => CacheSettingDaysType.days3,
    );
  }
}

/// 缓存大小限制常量
class CacheSizeConstant {
  static const int minSize = 5; // 最小5GB
  static const int maxSize = 200; // 最大200GB
  static const int defaultSize = 40; // 默认40GB
}

/// 分辨率枚举
enum Resolution {
  low(2048),
  medium(3000),
  high(4000);

  final int value;
  const Resolution(this.value);

  static Resolution fromValue(int value) {
    return Resolution.values.firstWhere(
      (resolution) => resolution.value == value,
      orElse: () => Resolution.medium,
    );
  }
}
