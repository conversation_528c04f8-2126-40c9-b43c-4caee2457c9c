import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:turing_art/utils/pg_log.dart';

// Windows API 常量
const int invalidFileAttributes = 0xFFFFFFFF;
const int fileAttributeHidden = 0x00000002;

// FFI函数签名定义
typedef GetFileAttributesWNative = Uint32 Function(Pointer<Utf16> lpFileName);
typedef GetFileAttributesWDart = int Function(Pointer<Utf16> lpFileName);

/// Windows API FFI绑定类
/// 提供Windows系统API的native接口
class WindowsApiBindings {
  static DynamicLibrary? _kernel32;
  static GetFileAttributesWDart? _getFileAttributesW;
  static bool _initialized = false;

  /// 初始化Windows API绑定
  static bool initialize() {
    if (_initialized) {
      PGLog.i('WindowsApiBindings already initialized');
      return true;
    }

    // 只在Windows平台初始化
    if (!Platform.isWindows) {
      PGLog.w(
          'WindowsApiBindings: Not on Windows platform, skipping initialization');
      return false;
    }

    try {
      // 加载kernel32.dll
      _kernel32 = DynamicLibrary.open('kernel32.dll');

      // 查找GetFileAttributesW函数
      _getFileAttributesW = _kernel32!
          .lookupFunction<GetFileAttributesWNative, GetFileAttributesWDart>(
              'GetFileAttributesW');

      _initialized = true;
      PGLog.i('WindowsApiBindings initialized successfully');
      return true;
    } catch (e) {
      PGLog.e('Failed to initialize WindowsApiBindings: $e');
      return false;
    }
  }

  /// 检查是否已初始化且可用
  static bool get isAvailable => _initialized && _getFileAttributesW != null;

  /// 获取文件属性
  /// [filePath] 文件路径
  /// 返回文件属性值，失败时返回INVALID_FILE_ATTRIBUTES
  static int getFileAttributes(String filePath) {
    if (!isAvailable) {
      if (!initialize()) {
        PGLog.w('WindowsApiBindings not available');
        return invalidFileAttributes;
      }
    }

    Pointer<Utf16>? pathPtr;
    try {
      pathPtr = filePath.toNativeUtf16();
      return _getFileAttributesW!(pathPtr);
    } catch (e) {
      PGLog.e('Error calling GetFileAttributesW: $e');
      return invalidFileAttributes;
    } finally {
      // 释放内存
      if (pathPtr != null) {
        malloc.free(pathPtr);
      }
    }
  }

  /// 检查文件是否为隐藏文件
  /// [filePath] 文件路径
  /// 返回true表示是隐藏文件，false表示不是或检查失败
  static bool isHiddenFile(String filePath) {
    final attributes = getFileAttributes(filePath);

    if (attributes == invalidFileAttributes) {
      return false;
    }

    return (attributes & fileAttributeHidden) != 0;
  }

  /// 清理资源
  static void dispose() {
    _kernel32 = null;
    _getFileAttributesW = null;
    _initialized = false;
  }
}
