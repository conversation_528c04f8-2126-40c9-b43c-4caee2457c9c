import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_status_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_request.dart';
import 'package:turing_art/datalayer/service/aigc_sample/aigc_sample_api_service.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/services/hostname_service.dart';

abstract class AigcSampleService {
  /// 创建打样
  Future<AigcSampleModel> createAigcSample(AigcSampleRequest request);

  /// 重新打样
  Future<AigcSampleModel> regenerateAigcSample(String proofingId);

  /// 获取打样详情
  Future<AigcSampleModel> getAigcSampleDetail(String proofingId);

  /// 获取打样列表
  Future<List<AigcSampleModel>> getAigcSampleList(
    String projectId, {
    int page = 1,
    int pageSize = 10000,
  });

  /// 获取打样导出列表
  Future<List<AigcSampleExportProjectModel>> getAigcSampleExportList({
    int page = 1,
    int pageSize = 10000,
  });

  /// 删除打样导出
  Future<void> deleteAigcSampleExport(
      List<AigcSampleListExportRequest> request);

  /// 打样生成导出
  Future<void> exportAigcSample(String proofingId, String effectCode);

  /// 删除打样
  Future<void> deleteAigcSample(String proofingId);

  /// 删除打样效果图
  Future<void> deleteAigcSampleEffect(String proofingId, String effectCode);

  /// 轮询打样状态
  Future<List<AigcPcPresetsLoopModel>> getAigcSampleProofingStatus(
      List<String> ids);

  /// 轮询打样导出状态
  Future<List<AigcSampleExportStatusModel>> getAigcSampleExportStatus(
      List<AigcSampleListExportRequest> requests,
      {bool isMyExportList = false});

  /// 获取本地项目列表
  Future<List<AigcSampleProjectModel>> getAigcSampleProjectList({
    int page = 1,
    int pageSize = 10000,
  });

  Future<void> updateAigcSampleInfo(String proofingId, String imageUrl);

  /// 打样导出下载状态上报
  Future<void> updateAigcSampleDownloadStatus(
      List<AigcSampleListExportRequest> requests);
}

class AigcSampleServiceImpl extends AigcSampleService {
  final ApiClient _apiClient;
  late final AigcSampleApiService _aigcSampleApiService;

  AigcSampleServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _aigcSampleApiService = AigcSampleApiService(dio);
  }

  @override
  Future<List<AigcSampleModel>> getAigcSampleList(
    String projectId, {
    int page = 1,
    int pageSize = 10000,
  }) async {
    final response = await _aigcSampleApiService.getAigcSampleList(
      page,
      pageSize,
      projectId,
    );
    return response.proofings;
    // return AigcSampleMockService.getAigcSampleMockList(projectId);
  }

  @override
  Future<List<AigcSampleExportProjectModel>> getAigcSampleExportList({
    int page = 1,
    int pageSize = 10000,
  }) async {
    final response = await _aigcSampleApiService.exportAigcSampleList(
      page,
      pageSize,
    );
    return response.projects;
  }

  @override
  Future<void> deleteAigcSampleExport(
      List<AigcSampleListExportRequest> request) async {
    final apiRequest = {
      'ids': request.map((e) => e.toJson()).toList(),
    };
    await _aigcSampleApiService.deleteAigcSampleExport(apiRequest);
  }

  @override
  Future<void> exportAigcSample(String proofingId, String effectCode) async {
    // 获取主机名
    final hostname = await HostnameService.getHostname();

    await _aigcSampleApiService.exportAigcSample(proofingId, effectCode, {
      'hostname': hostname,
    });
  }

  @override
  Future<void> deleteAigcSample(String proofingId) async {
    await _aigcSampleApiService.deleteAigcSample(proofingId);
  }

  @override
  Future<AigcSampleModel> regenerateAigcSample(String proofingId) async {
    // 获取主机名
    final hostname = await HostnameService.getHostname();

    final response =
        await _aigcSampleApiService.regenerateAigcSample(proofingId, {
      'hostname': hostname,
    });
    return response;
  }

  @override
  Future<AigcSampleModel> getAigcSampleDetail(String proofingId) async {
    final response =
        await _aigcSampleApiService.getAigcSampleDetail(proofingId);
    return response;
  }

  @override
  Future<AigcSampleModel> createAigcSample(AigcSampleRequest request) async {
    // 获取主机名
    final hostname = await HostnameService.getHostname();

    // 在原有请求数据基础上添加hostname字段
    final requestData = request.toJson();
    requestData['hostname'] = hostname;

    final response = await _aigcSampleApiService.createAigcSample(
      requestData,
    );
    return response;
  }

  @override
  Future<void> deleteAigcSampleEffect(
      String proofingId, String effectCode) async {
    await _aigcSampleApiService.deleteAigcSampleEffect(proofingId, effectCode);
  }

  @override
  Future<List<AigcPcPresetsLoopModel>> getAigcSampleProofingStatus(
      List<String> ids) async {
    final response = await _aigcSampleApiService.getAigcSampleProofingStatus(
      {
        'ids': ids,
      },
    );
    return response;
  }

  @override
  Future<List<AigcSampleExportStatusModel>> getAigcSampleExportStatus(
      List<AigcSampleListExportRequest> requests,
      {bool isMyExportList = false}) async {
    final response = await _aigcSampleApiService.getAigcSampleExportStatus(
      {
        'ids': requests.map((e) => e.toJson()).toList(),
      },
    );
    return response;
  }

  @override
  Future<List<AigcSampleProjectModel>> getAigcSampleProjectList({
    int page = 1,
    int pageSize = 10000,
  }) async {
    final response = await _aigcSampleApiService.getAigcSampleProjectList(
      page,
      pageSize,
    );
    return response.projects;
  }

  @override
  Future<void> updateAigcSampleInfo(String proofingId, String imageUrl) async {
    await _aigcSampleApiService.updateAigcSampleInfo(
      proofingId,
      {
        'large_origin_photo_url': imageUrl,
      },
    );
  }

  @override
  Future<void> updateAigcSampleDownloadStatus(
      List<AigcSampleListExportRequest> requests) async {
    final apiRequest = {
      'ids': requests.map((e) => e.toJson()).toList(),
    };
    await _aigcSampleApiService.updateAigcSampleDownloadStatus(apiRequest);
  }
}
