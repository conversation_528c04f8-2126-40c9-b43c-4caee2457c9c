import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/exchange_count_type.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 兑换成功弹窗
class CouponSuccessDialog extends StatefulWidget {
  /// 兑换成功金额
  final String couponAmount;

  /// 兑换成功过期时间
  final String expireDate;

  /// 兑换成功描述(xx"过期"或永久"有效")
  final String lastDes;

  /// 是否是永久有效
  final bool isForever;

  /// 兑换码类型
  final String? couponType;

  const CouponSuccessDialog({
    super.key,
    required this.couponAmount,
    required this.expireDate,
    required this.lastDes,
    required this.isForever,
    this.couponType,
  });

  @override
  State<CouponSuccessDialog> createState() => _CouponSuccessDialogState();

  /// 创建对话框内容
  static Widget _createDialogContent(BuildContext context, String couponAmount,
      String expireDate, String lastDes, String? couponType, bool isForever) {
    return CouponSuccessDialog(
      couponAmount: couponAmount,
      expireDate: expireDate,
      lastDes: lastDes,
      isForever: isForever,
      couponType: couponType,
    );
  }

  /// public方法：显示对话框
  static Future<void> show(
      String couponAmount, String expireDate, String lastDes,
      {bool isForever = false, String? couponType}) async {
    PGLog.d('显示兑换成功弹窗');

    PGDialog.showCustomDialog(
      width: 400,
      height: 306,
      needBlur: false,
      tag: DialogTags.couponSuccess,
      child: Builder(
        builder: (context) {
          return _createDialogContent(context, couponAmount, expireDate,
              lastDes, couponType, isForever);
        },
      ),
    );
  }
}

class _CouponSuccessDialogState extends State<CouponSuccessDialog> {
  Timer? _autoCloseTimer;

  @override
  void initState() {
    super.initState();
    // 3秒后自动关闭
    // _autoCloseTimer = Timer(const Duration(seconds: 3), () {
    //   _closeDialog();
    // });
  }

  void _closeDialog() {
    PGDialog.dismiss(tag: DialogTags.couponSuccess);
  }

  @override
  void dispose() {
    _autoCloseTimer?.cancel();
    super.dispose();
  }

  String _getSuccessTitle() {
    final exportType =
        ExchangeCountType.fromString(widget.couponType ?? 'export_count');
    switch (exportType) {
      case ExchangeCountType.sampleCount:
        return '兑换成功，恭喜你获得小样照片导出资格';
      case ExchangeCountType.exportCount:
      case ExchangeCountType.aigcCount:
      default:
        return '兑换成功';
    }
  }

  bool _shouldShowSuccessInfo() {
    final exportType =
        ExchangeCountType.fromString(widget.couponType ?? 'export_count');
    return exportType != ExchangeCountType.sampleCount;
  }

  List<TextSpan> _buildSuccessInfoSpans() {
    final exportType =
        ExchangeCountType.fromString(widget.couponType ?? 'export_count');
    final spans = <TextSpan>[];

    spans.add(TextSpan(
      text: widget.couponAmount,
      style: const TextStyle(
        color: Color(0xFFF72561),
      ),
    ));

    spans.add(TextSpan(text: exportType.description));
    spans.add(TextSpan(text: widget.isForever ? '已到账，' : '已到账，于'));

    spans.add(TextSpan(
      text: widget.expireDate,
      style: const TextStyle(
        color: Color(0xFFF72561),
      ),
    ));

    spans.add(TextSpan(text: widget.lastDes));
    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      height: 306,
      decoration: BoxDecoration(
        color: const Color(0xFF121315),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x33000000),
            blurRadius: 40,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 关闭按钮
          Positioned(
            top: 16,
            right: 16,
            child: GestureDetector(
              onTap: _closeDialog,
              child: Image.asset(
                "assets/icons/home_window_close.png",
                width: 24,
                height: 24,
              ),
            ),
          ),

          // 成功图标
          Positioned(
            top: 28,
            left: 0,
            right: 0,
            child: Center(
              child: Image.asset(
                "assets/icons/new_user_card_success.png",
                width: 148,
                height: 148,
              ),
            ),
          ),

          // "兑换成功"文字
          Positioned(
            top: 185,
            left: 20,
            right: 20,
            child: Center(
              child: Text(
                _getSuccessTitle(),
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 24,
                  height: 32 / 24, // line-height / font-size
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // 兑换成功信息
          if (_shouldShowSuccessInfo())
            Positioned(
              top: 230,
              left: 0,
              right: 0,
              child: Center(
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                      fontSize: 18,
                      height: 1.0,
                      color: const Color(0xA6EBEDF5),
                    ),
                    children: _buildSuccessInfoSpans(),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
