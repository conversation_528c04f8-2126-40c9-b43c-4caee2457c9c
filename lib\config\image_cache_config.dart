import 'dart:io';

import 'package:flutter/painting.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 基于 cached_network_image 库实现的图片缓存配置管理器，
/// 使用文档可参考：https://pub.dev/packages/flutter_cache_manager
class ImageCacheConfig {
  ImageCacheConfig._();

  static const String cacheName = 'turingArtImageCache';

  /// 自定义缓存管理器
  static CacheManager? _customCacheManager;

  /// 获取自定义缓存管理器
  static CacheManager get cacheManager =>
      _customCacheManager ?? DefaultCacheManager();

  /// 初始化图片缓存配置
  static Future<void> initialize() async {
    try {
      // 使用 FileManager 获取图片缓存目录
      final Directory imageCacheDir =
          await FileManager().getImageCacheDirectory();
      final String cachePath = imageCacheDir.path;

      // 创建自定义缓存管理器
      _customCacheManager = CacheManager(
        Config(
          cacheName, // 缓存键名
          stalePeriod: const Duration(days: 7),
          // 缓存过期时间：7天
          maxNrOfCacheObjects: 1000,
          // 最大缓存对象数量
          repo: JsonCacheInfoRepository(databaseName: cacheName),
          // 使用 FileManager 管理的缓存路径
          fileSystem: IOFileSystem(cachePath),
          fileService: HttpFileService(),
        ),
      );

      // 设置全局图片缓存配置
      PaintingBinding.instance.imageCache.maximumSize = 500; // 内存中最大图片数量
      PaintingBinding.instance.imageCache.maximumSizeBytes =
          500 * 1024 * 1024; // 内存中最大缓存大小：500MB
    } catch (e) {
      // 如果初始化失败，使用默认缓存管理器
      _customCacheManager = DefaultCacheManager();
      PGLog.e('图片缓存初始化失败，使用默认配置: $e');
    }
  }

  /// 清除所有图片缓存
  static Future<void> clearAllCache() async {
    try {
      await cacheManager.emptyCache();
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
    } catch (e) {
      PGLog.e('清除图片缓存失败: $e');
    }
  }

  /// 清除指定URL的缓存
  static Future<void> clearCacheForUrl(String url) async {
    try {
      await cacheManager.removeFile(url);
    } catch (e) {
      PGLog.e('清除指定URL缓存失败: $e');
    }
  }

  /// 获取缓存文件
  static Future<FileInfo?> getCacheFile() async {
    try {
      return await cacheManager.getFileFromCache(cacheName);
    } catch (e) {
      PGLog.e('获取缓存文件失败: $e');
      return null;
    }
  }

  /// 获取缓存大小（字节）
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await getCacheFile();
      if (cacheDir?.file.parent != null) {
        return await _calculateDirectorySize(cacheDir!.file.parent);
      }
      return 0;
    } catch (e) {
      PGLog.e('获取缓存大小失败: $e');
      return 0;
    }
  }

  /// 计算目录大小
  static Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      if (directory.existsSync()) {
        await for (var entity
            in directory.list(recursive: true, followLinks: false)) {
          if (entity is File) {
            size += await entity.length();
          }
        }
      }
    } catch (e) {
      PGLog.e('计算目录大小失败: $e');
    }
    return size;
  }

  /// 格式化缓存大小显示
  static String formatCacheSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 预加载图片到缓存
  static Future<void> preloadImage(String imageUrl) async {
    try {
      await cacheManager.downloadFile(imageUrl);
    } catch (e) {
      PGLog.e('预加载图片失败: $e');
    }
  }

  /// 批量预加载图片
  static Future<void> preloadImages(List<String> imageUrls) async {
    final futures = imageUrls.map((url) => preloadImage(url));
    await Future.wait(futures);
  }

  /// 根据图片URL获取缓存文件信息
  ///
  /// [imageUrl] 图片URL
  /// [ignoreMemCache] 是否忽略内存缓存，只检查磁盘缓存，默认为 false
  /// 返回 FileInfo 对象，如果不存在则返回 null
  static Future<FileInfo?> getFileFromCache(String imageUrl,
      {bool ignoreMemCache = false}) async {
    try {
      return await cacheManager.getFileFromCache(imageUrl,
          ignoreMemCache: ignoreMemCache);
    } catch (e) {
      PGLog.e('获取缓存文件信息失败: $e');
      return null;
    }
  }
}
