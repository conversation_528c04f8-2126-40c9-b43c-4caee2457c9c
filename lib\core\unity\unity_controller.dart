import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/widgets/unity/helper/mac_unity_helper.dart';
import 'package:turing_art/widgets/unity/helper/windows_unity_helper.dart';
import 'package:uuid/uuid.dart';

import 'unity_exception.dart';
import 'unity_message_sender.dart';
import 'unity_messages.dart';

enum UnityStatus {
  uninitialized,
  initialized,
}

class _RequestContext {
  final UnityMessage responseMethod;
  final Completer<dynamic> completer;
  final String requestId;

  _RequestContext({
    required this.responseMethod,
    required this.completer,
    required this.requestId,
  });
}

class UnityController {
  static final Map<String, UnityMessage> _unityMessageMap = {
    for (var e in UnityMessage.values) e.value: e
  };

  final UnityMessageSender _messageSender;

  // 基于请求ID的映射
  final Map<String, _RequestContext> _targetRequests = {};
  final Map<String, Completer<bool>> _activeRequests = {};

  /// Unity 状态
  final _initCompleter = Completer<void>();
  final _loginCompleter = Completer<void>();

  UnityStatus get status => _unityStatus;

  bool get isInitialized => status == UnityStatus.initialized && _loginStatus;
  UnityStatus _unityStatus = UnityStatus.uninitialized;
  bool _loginStatus = false;
  Future<void> get initialized => _initCompleter.future;
  Future<void> get isReady => _loginCompleter.future;

  UnityController({
    required UnityMessageSender messageSender,
  }) : _messageSender = messageSender {
    // 初始化拖拽文件监听
    _initDropFilesListener();
  }

  // 拖拽文件监听
  StreamController<List<File>>? _dropFilesController;
  Stream<List<File>>? _dropFilesStream;

  // 拖拽文件的流
  Stream<List<File>> get onDropFiles {
    _dropFilesStream ??= _dropFilesController?.stream.asBroadcastStream();
    return _dropFilesStream!;
  }

  // 初始化拖拽文件监听
  void _initDropFilesListener() {
    _dropFilesController = StreamController<List<File>>.broadcast();

    // 设置监听处理，当从原生层收到拖拽文件信息时触发
    ServicesBinding.instance.defaultBinaryMessenger.setMessageHandler(
      'dev.turingart/unity_dropped_files',
      (ByteData? message) async {
        if (message == null) {
          return null;
        }

        try {
          final List<dynamic> filePathsJson = const StandardMessageCodec()
              .decodeMessage(message) as List<dynamic>;

          final List<File> files =
              filePathsJson.map((path) => File(path.toString())).toList();

          PGLog.d('收到拖拽文件：${files.length} 个文件');

          // 将文件列表发送到流
          _dropFilesController?.add(files);
        } catch (e) {
          PGLog.e('处理拖拽文件出错: $e');
        }

        return null;
      },
    );
  }

  // 释放资源
  void dispose() {
    // 清理拖拽文件监听
    _dropFilesController?.close();
    _dropFilesController = null;
    _dropFilesStream = null;
  }

  void login() {
    _loginStatus = true;
    _loginCompleter.complete();
    PGLog.i('Unity engine login successfully');
  }

  /// 引擎初始化完成
  void _onEngineInitialized() {
    if (!_initCompleter.isCompleted) {
      _setStatus(UnityStatus.initialized);
      _initCompleter.complete();
      PGLog.i('Unity engine initialized successfully');
    }
  }

  void _setStatus(UnityStatus newStatus) {
    if (_unityStatus != newStatus) {
      _unityStatus = newStatus;
    }
  }

  Future<void> setUnityVisibility({required bool visible}) async {
    if (Platform.isWindows) {
      await WindowsUnityHelper.setUnityVisibility(visible: visible);
    } else if (Platform.isMacOS) {
      return MacUnityHelper.setUnityVisibility(visible: visible);
    }
  }

  /// 发送消息到Unity
  /// 返回值标识收到对应匹配函数的回调情况
  Future<T> sendMessageWithResponse<T>(
    MessageToUnity message,
    UnityMessage responseMethod,
  ) async {
    try {
      final requestId = message.completed;
      final completer = Completer<T>();

      _targetRequests[responseMethod.value] = _RequestContext(
        responseMethod: responseMethod,
        completer: completer,
        requestId: requestId,
      );

      Timer(const Duration(seconds: 10), () {
        // 约定时间内未响应该事件，直接报错
        _targetRequests
            .remove(responseMethod.value)
            ?.completer
            .completeError(TimeoutException('未收到 ${responseMethod.value} 响应'));
      });

      await sendMessage(message);
      return completer.future;
    } catch (e) {
      throw UnityException('send message to unity error : $e');
    }
  }

  /// 发送消息到Unity,返回值标识是否调用成功
  Future<bool> sendMessage(MessageToUnity message) async {
    try {
      final requestId = message.completed;
      final completer = Completer<bool>();
      _activeRequests[requestId] = completer;
      Timer(const Duration(seconds: 10), () {
        // 约定时间内未响应该事件，直接报错
        _activeRequests
            .remove(requestId)
            ?.completeError(TimeoutException('未收到 ${message.method} 响应'));
      });
      await _messageSender.sendMessage(jsonEncode(message.toJson()));
      return await completer.future;
    } catch (e) {
      throw UnityException('send message to unity error : $e');
    }
  }

  /// 向Unity发送触发回调消息
  ///
  /// 此方法用于触发Unity中的回调函数，用于完成Unity端异步回调客户端的方法
  /// 如Unity端await CallNativeWin.GetDynamicEncryptionParamsAsync（）方法后
  /// 需要注意的是该方法需要再接受到unity异步消息后在任意情况下都要回调，否则Unity端只有等到超时
  /// 才会继续运行
  /// 参数:
  /// - [taskId]: 任务的唯一标识符，用于在Unity端识别对应的任务进行解除等待
  /// - [returnArgs]: 返回给Unity的参数，通常是JSON格式的字符串
  ///
  /// 内部实现:
  /// 1. 将taskId和result封装为JSON格式
  /// 2. 创建MessageToUnity对象，method设为"TriggerCallback"
  /// 3. 通过UnityMessageSender发送消息到Unity
  ///
  /// 异常:
  /// - 抛出[UnityException]如果消息发送失败
  Future<void> sendTriggerCallback(String taskId, String returnArgs) async {
    final jsonParams = jsonEncode({
      'taskId': taskId,
      'result': returnArgs,
    });
    final message = MessageToUnity(
      method: "TriggerCallback",
      args: jsonParams,
      completed: const Uuid().v4(),
    );
    try {
      await _messageSender.sendMessage(jsonEncode(message.toJson()));
    } catch (e) {
      throw UnityException('send message to unity error : $e');
    }
  }

  bool handleResponse(MessageFromUnity response) {
    /// 校验是否为约定的函数名
    final unityMessage = _unityMessageMap[response.method];
    if (unityMessage == null) {
      PGLog.e('Unknown Unity message: ${response.method}');
      return false;
    }

    final suspended = _targetRequests[response.method];
    if (suspended != null) {
      // 表明这个回调是有用户发起的
      _targetRequests.remove(suspended.responseMethod.value);
      suspended.completer.complete(response);
      return true;
    }

    switch (unityMessage) {
      case UnityMessage.initialized:
        _onEngineInitialized();
        return true;
      case UnityMessage.error:
        PGLog.e('Unity error: $response');
        _handleActiveResponse(response)?.complete(false);
        return true;
      case UnityMessage.completed:
        PGLog.d('Unity completed: $response');
        _handleActiveResponse(response)?.complete(true);
        return true;
      default:
        return false;
    }
  }

  /// 处理成功或失败
  Completer<bool>? _handleActiveResponse(MessageFromUnity response) {
    try {
      final args = response.args ?? [];
      PGLog.d('_handleActiveResponse: $args');

      if (args.isEmpty) {
        return null;
      }

      final requestId = args.first;
      if (requestId is String) {
        return _activeRequests.remove(requestId);
      }
    } catch (e) {
      PGLog.e('OnSyncWorkspace handle error: $e');
    }
    return null;
  }
}
