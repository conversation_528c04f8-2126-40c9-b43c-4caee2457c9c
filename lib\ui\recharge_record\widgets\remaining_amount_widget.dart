import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class RemainingAmountWidget extends StatelessWidget {
  final String totalRemaining;
  final String totalAmount;
  final bool isAIGCType;

  const RemainingAmountWidget({
    super.key,
    required this.totalRemaining,
    required this.totalAmount,
    this.isAIGCType = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 剩余张数/总张数数值
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            // 剩余张数
            Text(
              totalRemaining,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                fontSize: 24,
                height: 32 / 24,
                color: const Color(0xFFE1E2E5),
              ),
            ),
            const SizedBox(width: 2),
            // 总张数
            Text(
              '/ $totalAmount',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                fontSize: 12,
                height: 16 / 12,
                color: const Color(0xFFE1E2E5),
              ),
            ),
          ],
        ),

        // 描述文本
        Text(
          isAIGCType ? '剩余积分/总积分' : '剩余张数/总张数',
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 10,
            height: 16 / 10,
            color: const Color(0xA6EBEDF5),
          ),
        ),
      ],
    );
  }
}
