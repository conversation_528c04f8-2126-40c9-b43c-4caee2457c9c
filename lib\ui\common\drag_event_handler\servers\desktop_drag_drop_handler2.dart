import 'dart:io';

import 'package:cross_file/cross_file.dart';
import 'package:flutter/foundation.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/permission_util.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'drag_drop_interface.dart';

class DesktopDragDropHandler2 implements DragDropInterface {
  @override
  Future<List<DealImageFilesResult>> processDroppedFilesForMultiProject(
      List<XFile> files) async {
    // service里面使用的DesktopDragDropHandler，提交记录是测试代码，这里不处理
    return [];
  }

  @override
  Future<DealImageFilesResult?> processDroppedFilesForSingleProject(
      List<XFile> files) async {
    try {
      PGLog.d('DesktopDragDropHandler2 - processDroppedFiles');
      if (Platform.isMacOS) {
        final permissionGranted =
            await PermissionUtil.requestPermission(PermissionType.file);
        if (!permissionGranted) {
          PGLog.d('用户未授予文件权限');
          return null;
        }
      }

      // 在IO线程中执行文件处理（service里面使用的DesktopDragDropHandler，提交记录是测试代码，这里projectName就不处理）
      final result = await compute(_processFilesInIsolate, files);
      return result != null
          ? DealImageFilesResult.fromImages(result, '')
          : null;
    } catch (e) {
      PGLog.e('处理拖放文件时出错: $e');
      return null;
    }
  }

  // 静态方法用于isolate执行
  static List<File>? _processFilesInIsolate(List<XFile> files) {
    final validFiles = <File>[];
    const supportedExts = ImageConstants.supportedExtensions;

    for (final xFile in files) {
      final entity = File(xFile.path);
      final stat = entity.statSync();

      if (stat.type == FileSystemEntityType.directory) {
        // 使用递归参数简化目录遍历
        final dir = Directory(xFile.path);
        final entities = dir.listSync(recursive: true);

        for (final e in entities) {
          if (e is File) {
            final ext = e.path.split('.').last.toLowerCase();
            if (supportedExts.contains(ext)) {
              validFiles.add(e);
            }
          }
        }
      } else if (stat.type == FileSystemEntityType.file) {
        final ext = xFile.name.split('.').last.toLowerCase();
        if (supportedExts.contains(ext)) {
          validFiles.add(entity);
        }
      }
    }
    return validFiles.isNotEmpty ? validFiles : null;
  }
}
