import 'package:turing_art/ui/use_case/generate_workspace_disk_space_guard_use_case.dart';
import 'package:turing_art/ui/use_case/open_workspace_disk_space_guard_use_case.dart';

class WorkspaceUseCaseProvider {
  final GenerateWorkspaceDiskSpaceGuardUseCase generateWorkspaceDiskSpaceGuard;
  final OpenWorkspaceDiskSpaceGuardUseCase openWorkspaceDiskSpaceGuard;

  WorkspaceUseCaseProvider()
      : generateWorkspaceDiskSpaceGuard =
            GenerateWorkspaceDiskSpaceGuardUseCase(),
        openWorkspaceDiskSpaceGuard = OpenWorkspaceDiskSpaceGuardUseCase();
}
