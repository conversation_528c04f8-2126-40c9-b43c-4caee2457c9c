import '../../task_queue_system/generic_scheduler.dart';
import 'aigc_queue.dart';
import 'aigc_task.dart';

/// AIGC调度策略基类
abstract class AigcSchedulingStrategy
    extends SchedulingStrategy<AigcTask, AigcQueueType> {
  AigcSchedulingStrategy();
}

/// 默认策略：图片库页面（预览优先）
class DefaultAigcStrategy extends AigcSchedulingStrategy {
  DefaultAigcStrategy();

  @override
  String get name => '默认模式';

  @override
  Map<AigcQueueType, int> get queuePriorities => const {
        AigcQueueType.foreground: 1, // 前台队列优先级最高
        AigcQueueType.background: 2, // 后台队列次之
      };

  @override
  Map<Type, AigcQueueType> get taskTypeToQueue => const {
        AigcImageAssetsTask: AigcQueueType.foreground, // 图像资源任务 → 前台队列
        AigcMaskTask: AigcQueueType.foreground, // 蒙版任务 → 前台队列
        AigcInteractiveMaskTask: AigcQueueType.foreground, // 交互式蒙版任务 → 前台队列
        AigcImageTask: AigcQueueType.background, // 图像生成任务 → 后台队列
        AigcRawConversionTask: AigcQueueType.background, // 智能调色任务 → 后台队列
        AigcThumbnailTask: AigcQueueType.foreground, // 缩略图任务 → 后台队列
        AigcCoverTask: AigcQueueType.background, // 封面任务 → 后台队列
      };
}

/// 导出策略：导出页面（导出优先）
class ExportAigcStrategy extends AigcSchedulingStrategy {
  ExportAigcStrategy();

  @override
  String get name => '导出模式';

  @override
  Map<AigcQueueType, int> get queuePriorities => const {
        AigcQueueType.foreground: 1, // 前台队列优先级最高
        AigcQueueType.background: 2, // 后台队列次之
      };

  @override
  Map<Type, AigcQueueType> get taskTypeToQueue => const {
        AigcImageAssetsTask: AigcQueueType.background, // 图像资源任务 → 后台队列
        AigcMaskTask: AigcQueueType.foreground, // 蒙版任务 → 前台队列
        AigcInteractiveMaskTask: AigcQueueType.foreground, // 交互式蒙版任务 → 前台队列
        AigcImageTask: AigcQueueType.foreground, // 图像生成任务 → 前台队列
        AigcRawConversionTask: AigcQueueType.foreground, // 智能调色任务 → 前台队列
        AigcThumbnailTask: AigcQueueType.background, // 缩略图任务 → 后台队列
        AigcCoverTask: AigcQueueType.background, // 封面任务 → 后台队列
      };
}

/// 预定义的调度策略常量
class AigcSchedulingStrategies {
  static final defaultStrategy = DefaultAigcStrategy();
  static final exportStrategy = ExportAigcStrategy();
}
