import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:synchronized/synchronized.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_queue.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../task_queue_system/generic_scheduler.dart';
import '../task_queue_system/worker_channel.dart';
import 'models/aigc_scheduling_strategies.dart';
import 'models/aigc_task.dart';
import 'models/aigc_task_priority.dart';
import 'processors/aigc_processor_factory.dart';

/// AIGC预处理服务 - 基于通用任务队列系统的业务实现
///
/// # 并发安全设计
///
/// ## 锁使用规范
/// 1. **主锁 (_mainLock)**: 保护工作器分配和任务跟踪的原子性操作
///    - 用于需要同时访问工作器状态和任务跟踪的场景
///    - 确保任务分配过程的一致性
///
/// 2. **任务跟踪锁 (_taskTrackingLock)**: 仅保护 _processingTasks 的读写操作
///    - 用于独立的任务状态查询和统计操作
///    - 粒度更细，性能更好
///
/// ## 锁获取原则
/// - 避免嵌套锁：使用 Unsafe 后缀的内部方法避免重复加锁
/// - 锁的作用域尽可能小：只保护必要的临界区
/// - 原子操作：相关的操作应在同一个锁保护下完成
///
/// ## 方法命名约定
/// - `*Unsafe`: 内部方法，调用者需要确保已获取适当的锁
/// - 公共方法：自动处理锁的获取和释放
class AigcService extends ChangeNotifier {
  final int _maxWorkers;

  final List<WorkerChannel> _workers = [];

  /// 专门处理蒙版任务的工作器通道
  WorkerChannel? _maskWorker;

  late final GenericScheduler<AigcTask, AigcQueueType> _taskScheduler;

  // 分类消息流 - 使用类型安全的消息
  late final StreamController<AigcTaskResultMessage> _resultController;
  late final StreamController<AigcTaskProgressMessage> _progressController;

  // 正在处理的任务跟踪 - 防止重复提交
  final Map<String, AigcTask> _processingTasks = {};

  // 同步锁 - 保护并发安全
  /// 主锁 - 保护工作器分配和任务跟踪的原子性操作
  /// 锁获取顺序规范：始终先获取 _mainLock，避免嵌套锁
  final Lock _mainLock = Lock();

  /// 任务跟踪锁 - 仅保护_processingTasks的读写操作
  /// 注意：当需要同时访问工作器状态和任务跟踪时，使用 _mainLock
  final Lock _taskTrackingLock = Lock();

  // 对外暴露的流
  Stream<AigcTaskResultMessage> get resultStream => _resultController.stream;
  Stream<AigcTaskProgressMessage> get progressStream =>
      _progressController.stream;

  Completer<void>? _initializeCompleter;

  /// 生成任务ID - 确保不同类型任务有不同ID
  static String generateTaskId(String pathIdentifier, AigcTaskType taskType) {
    return '${pathIdentifier.hashCode}_${taskType.name}';
  }

  /// 根据CPU核心数计算最佳工作器数量
  static int _calculateOptimalWorkerCount() {
    // try {
    //   final processorCount = Platform.numberOfProcessors;
    //   if (Platform.isWindows || Platform.isMacOS) {
    //     // 对于Windows和macOS桌面平台，使用更保守的策略
    //     // 留出一些核心给UI线程和系统进程
    //     if (processorCount <= 2) {
    //       return 1; // 双核或以下保持单工作器
    //     } else if (processorCount <= 4) {
    //       return 2; // 四核使用2个工作器
    //     } else if (processorCount <= 8) {
    //       return 3; // 八核使用3个工作器
    //     } else {
    //       return 4; // 八核以上最多使用4个工作器
    //     }
    //   } else {
    //     // 其他平台的默认策略
    //     return (processorCount * 0.5).ceil().clamp(1, 4);
    //   }
    // } catch (e) {
    //   PGLog.d('❌ 获取CPU核心数失败，使用默认工作器数量: $e');
    //   return 1; // 如果获取失败，返回默认值
    // }
    return 1;
  }

  AigcService({int? maxWorkers})
      : _maxWorkers = maxWorkers ?? _calculateOptimalWorkerCount() {
    _resultController = StreamController<AigcTaskResultMessage>.broadcast();
    _progressController = StreamController<AigcTaskProgressMessage>.broadcast();

    _taskScheduler = GenericScheduler<AigcTask, AigcQueueType>(
      initialStrategy: AigcSchedulingStrategies.defaultStrategy,
    );
  }

  /// 初始化处理器
  Future<void> initialize() async {
    if (_initializeCompleter != null) {
      return _initializeCompleter!.future;
    }

    // 开始新的初始化
    _initializeCompleter = Completer<void>();

    try {
      PGLog.d('🏭 初始化AIGC服务，创建 $_maxWorkers 个通用工作器 + 1个蒙版专用工作器...');

      // 创建通用工作器
      for (int i = 0; i < _maxWorkers; i++) {
        final workerId = 'aigc_worker_$i';
        final channel = await WorkerChannel.create(
          workerId,
          initializer: AigcProcessorFactory.initialize,
        );

        // 监听工作器消息
        channel.messageStream.listen((message) {
          _handleWorkerMessage(channel, message);
        });

        _workers.add(channel);
      }

      // 创建蒙版专用工作器
      _maskWorker = await WorkerChannel.create(
        'aigc_mask_worker',
        initializer: AigcProcessorFactory.initialize,
      );

      // 监听蒙版工作器消息
      _maskWorker!.messageStream.listen((message) {
        _handleMaskWorkerMessage(_maskWorker!, message);
      });

      final totalWorkers = _workers.length + (_maskWorker != null ? 1 : 0);
      PGLog.d(
          '✅ AIGC服务初始化完成，$totalWorkers 个工作器就绪（${_workers.length}个通用 + 1个蒙版专用）');
      _initializeCompleter!.complete();
    } catch (e) {
      PGLog.d('❌ 初始化失败: $e');
      _initializeCompleter!.completeError(e);
      rethrow;
    }
  }

  /// 处理通用工作器消息
  void _handleWorkerMessage(WorkerChannel channel, dynamic message) {
    if (message is AigcTaskResultMessage) {
      final inputPath = message.payload.inputPath;
      PGLog.d(
          '✅ 通用工作器 ${channel.workerId} 完成任务: $inputPath (${message.processingTime.inMilliseconds}ms)');

      // 🔒 使用主锁保护任务完成和下一任务分配的原子性操作
      _mainLock.synchronized(() {
        // 从正在处理的跟踪中移除任务
        final taskId = message.taskId;
        _processingTasks.remove(taskId);
        PGLog.d(
            '📋 任务处理完成移除跟踪: $taskId - 剩余正在处理任务数: ${_processingTasks.length}');

        // 检查是否有更多任务需要分配（非蒙版任务）
        _assignTaskToWorkerUnsafe(channel);
      });

      // 转发类型安全的结果消息到业务层
      _resultController.add(message);
    } else if (message is AigcTaskProgressMessage) {
      final currentStep = message.status;
      PGLog.d(
          '📊 通用工作器 ${channel.workerId} 进度: $currentStep - ${message.progress}%');
      _progressController.add(message);
    } else {
      PGLog.d('❌ 通用工作器 ${channel.workerId} 发送了未知消息类型: ${message.runtimeType}');
    }
  }

  /// 处理蒙版专用工作器消息
  void _handleMaskWorkerMessage(WorkerChannel channel, dynamic message) {
    if (message is AigcTaskResultMessage) {
      final inputPath = message.payload.inputPath;
      PGLog.d(
          '✅ 蒙版专用工作器 ${channel.workerId} 完成任务: $inputPath (${message.processingTime.inMilliseconds}ms)');

      // 🔒 使用主锁保护任务完成和下一任务分配的原子性操作
      _mainLock.synchronized(() {
        // 从正在处理的跟踪中移除任务
        final taskId = message.taskId;
        _processingTasks.remove(taskId);
        PGLog.d(
            '📋 蒙版任务处理完成移除跟踪: $taskId - 剩余正在处理任务数: ${_processingTasks.length}');

        // 检查是否有更多蒙版任务需要分配
        _assignMaskTaskToWorkerUnsafe(channel);
      });

      // 转发类型安全的结果消息到业务层
      _resultController.add(message);
    } else if (message is AigcTaskProgressMessage) {
      final currentStep = message.status;
      PGLog.d(
          '📊 蒙版专用工作器 ${channel.workerId} 进度: $currentStep - ${message.progress}%');
      _progressController.add(message);
    } else {
      PGLog.d(
          '❌ 蒙版专用工作器 ${channel.workerId} 发送了未知消息类型: ${message.runtimeType}');
    }
  }

  /// 分配非蒙版任务给通用工作器（内部方法，调用者需要确保已获取适当的锁）
  void _assignTaskToWorkerUnsafe(WorkerChannel channel) {
    // 获取下一个非蒙版任务（包括封面、缩略图等）
    final task = _taskScheduler.getNextTaskWhere((task) =>
        task.taskType != AigcTaskType.mask &&
        task.taskType != AigcTaskType.interactiveMask);
    if (task == null) {
      PGLog.d('😴 无非蒙版任务可分配，通用工作器 ${channel.workerId} 进入空闲状态');
      return;
    }

    // 将任务加入正在处理的跟踪（调用者需要确保已获取锁）
    _processingTasks[task.taskId] = task;
    PGLog.d(
        '🔄 非蒙版任务开始处理: ${task.inputPath} (${task.taskType}) - 正在处理任务数: ${_processingTasks.length}');

    // 为兼容通用任务处理器，发送Legacy消息
    final taskMessage = AigcTaskMessage(
      taskId: task.taskId,
      processorKey: task.taskType.name,
      payload: task,
    );

    channel.sendTask(taskMessage);
  }

  /// 分配蒙版任务给蒙版专用工作器（内部方法，调用者需要确保已获取适当的锁）
  void _assignMaskTaskToWorkerUnsafe(WorkerChannel channel) {
    // 获取蒙版或交互式蒙版任务，优先处理交互式蒙版（优先级更高）
    final task = _taskScheduler.getNextTaskWhere((task) =>
        task.taskType == AigcTaskType.mask ||
        task.taskType == AigcTaskType.interactiveMask);
    if (task == null) {
      PGLog.d('😴 无蒙版任务可分配，蒙版专用工作器 ${channel.workerId} 进入空闲状态');
      return;
    }

    // 将任务加入正在处理的跟踪（调用者需要确保已获取锁）
    _processingTasks[task.taskId] = task;
    PGLog.d(
        '🔄 蒙版任务开始处理: ${task.inputPath} (${task.taskType}) - 正在处理任务数: ${_processingTasks.length}');

    // 为兼容通用任务处理器，发送Legacy消息
    final taskMessage = AigcTaskMessage(
      taskId: task.taskId,
      processorKey: task.taskType.name,
      payload: task,
    );

    channel.sendTask(taskMessage);
  }

  /// 创建具体类型的任务
  AigcTask _createTask(
      {required String inputPath,
      required String outputPath,
      required String fileId,
      required AigcTaskType taskType,
      required int priority}) {
    final taskId = generateTaskId(inputPath, taskType);
    final now = DateTime.now();

    switch (taskType) {
      case AigcTaskType.imageAssets:
        return AigcImageAssetsTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.cover:
        return AigcCoverTask.create(
          taskId: taskId,
          inputPath: inputPath,
          outputPath: outputPath,
          fileId: fileId,
          submittedAt: now,
          priority: priority,
        );
      case AigcTaskType.thumbnail:
        return AigcThumbnailTask.create(
          taskId: taskId,
          inputPath: inputPath,
          outputPath: outputPath,
          fileId: fileId,
          submittedAt: now,
          priority: priority,
        );
      case AigcTaskType.mask:
        return AigcMaskTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.interactiveMask:
        // 注意：交互式蒙版任务需要额外的参数，这里直接抛出异常
        throw Exception('交互式蒙版任务需要额外的参数，请使用 submitInteractiveMaskTask 方法');
      case AigcTaskType.image:
        return AigcImageTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.rawConversion:
        return AigcRawConversionTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
    }
  }

  /// 统一的图像处理请求 - 智能处理新任务/优先级更新/忽略重复
  Future<void> submitTask({
    required String inputPath,
    required String outputPath,
    required String fileId,
    required AigcTaskType taskType,
    int sortBy = 0, // 图片索引，从0开始，用于按顺序处理
    bool executeNow = false, // 是否立即执行
  }) async {
    // 确保已初始化
    await initialize();

    final taskId = generateTaskId(inputPath, taskType);

    // 🔒 使用锁保护任务跟踪状态检查
    final isProcessing = await _taskTrackingLock.synchronized(() {
      return _processingTasks.containsKey(taskId);
    });

    if (isProcessing) {
      PGLog.d('⚠️ 任务正在处理中，跳过重复提交: $inputPath ($taskType)');
      return;
    }

    // 根据任务类型和索引计算内部优先级
    final priority = _calculatePriority(taskType, sortBy, executeNow);

    // 尝试更新已存在任务的优先级
    if (_taskScheduler.updateTaskPriorityById(taskId, AigcTask, priority)) {
      PGLog.d('🔄 任务排序已更新: $inputPath (index: $sortBy, priority: $priority)');
      return;
    }
    // 添加新任务
    final task = _createTask(
        inputPath: inputPath,
        outputPath: outputPath,
        fileId: fileId,
        taskType: taskType,
        priority: priority);

    _taskScheduler.addTask(task, executeNow: executeNow);

    final executeNowText = executeNow ? ' [立即执行]' : '';
    PGLog.d(
        '➕ 新任务已加入队列: $inputPath (index: $sortBy, priority: $priority)$executeNowText');

    // 🔒 使用主锁保护工作器分配逻辑，防止竞态条件
    await _mainLock.synchronized(() async {
      // 根据任务类型分配给对应的工作器
      if (taskType == AigcTaskType.mask ||
          taskType == AigcTaskType.interactiveMask) {
        // 蒙版任务分配给专用工作器
        if (_maskWorker != null && _maskWorker!.isIdle) {
          PGLog.d('📲 通知空闲蒙版专用工作器处理新任务');
          _assignMaskTaskToWorkerUnsafe(_maskWorker!);
        } else {
          PGLog.d('⏳ 蒙版专用工作器忙碌中，蒙版任务已按排序加入队列');
        }
      } else {
        // 非蒙版任务（包括封面、缩略图等）分配给通用工作器
        final idleWorker = _workers.where((w) => w.isIdle).firstOrNull;
        if (idleWorker != null) {
          PGLog.d('📲 通知空闲通用工作器处理新任务');
          _assignTaskToWorkerUnsafe(idleWorker);
        } else {
          PGLog.d('⏳ 所有通用工作器忙碌中，非蒙版任务已按排序加入队列');
        }
      }
    });
  }

  /// 根据任务类型、索引和执行模式计算内部优先级
  int _calculatePriority(AigcTaskType taskType, int sortBy, bool executeNow) {
    // 如果是立即执行，使用特殊优先级（负数）
    if (executeNow) {
      switch (taskType) {
        case AigcTaskType.imageAssets:
          return AigcTaskSortBy.selectedPreview;
        case AigcTaskType.cover:
          return AigcTaskSortBy.selectedCover;
        case AigcTaskType.thumbnail:
          return AigcTaskSortBy.selectedThumbnail;
        case AigcTaskType.mask:
          return AigcTaskSortBy.selectedMask;
        case AigcTaskType.interactiveMask:
          return AigcTaskSortBy.selectedInteractiveMask;
        case AigcTaskType.image:
          return AigcTaskSortBy.selectedImage;
        case AigcTaskType.rawConversion:
          return AigcTaskSortBy.selectedImage; // 使用图像处理类似的优先级
      }
    }

    // 普通任务根据类型和索引计算优先级
    switch (taskType) {
      case AigcTaskType.imageAssets:
        return AigcTaskSortBy.preview(sortBy);
      case AigcTaskType.cover:
        return AigcTaskSortBy.cover(sortBy);
      case AigcTaskType.thumbnail:
        return AigcTaskSortBy.thumbnail(sortBy);
      case AigcTaskType.mask:
        return AigcTaskSortBy.mask(sortBy);
      case AigcTaskType.interactiveMask:
        return AigcTaskSortBy.interactiveMask(sortBy);
      case AigcTaskType.image:
        return AigcTaskSortBy.image(sortBy);
      case AigcTaskType.rawConversion:
        return AigcTaskSortBy.image(sortBy); // 使用图像处理类似的优先级
    }
  }

  /// 专门用于提交交互式蒙版任务的方法
  Future<void> submitInteractiveMaskTask({
    required String inputPath,
    required String outputPath,
    required String fileId,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
    String? modelName,
    double? threshold,
    int sortBy = 0,
    bool executeNow = false,
  }) async {
    // 确保已初始化
    await initialize();

    final taskId = generateTaskId(inputPath, AigcTaskType.interactiveMask);

    // 🔒 使用锁保护任务跟踪状态检查
    final isProcessing = await _taskTrackingLock.synchronized(() {
      return _processingTasks.containsKey(taskId);
    });

    if (isProcessing) {
      PGLog.d('⚠️ 交互式蒙版任务正在处理中，跳过重复提交: $inputPath');
      return;
    }

    // 计算优先级
    final priority =
        _calculatePriority(AigcTaskType.interactiveMask, sortBy, executeNow);

    // 尝试更新已存在任务的优先级
    if (_taskScheduler.updateTaskPriorityById(taskId, AigcTask, priority)) {
      PGLog.d(
          '🔄 交互式蒙版任务排序已更新: $inputPath (index: $sortBy, priority: $priority)');
      return;
    }

    // 创建交互式蒙版任务
    final task = AigcInteractiveMaskTask.create(
      taskId: taskId,
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      submittedAt: DateTime.now(),
      priority: priority,
      mattingMaskDataInfo: mattingMaskDataInfo,
      modelName: modelName,
      threshold: threshold,
    );

    _taskScheduler.addTask(task, executeNow: executeNow);

    final executeNowText = executeNow ? ' [立即执行]' : '';
    PGLog.d(
        '➕ 新交互式蒙版任务已加入队列: $inputPath (index: $sortBy, priority: $priority)$executeNowText');

    // 交互式蒙版任务使用蒙版专用工作器
    await _mainLock.synchronized(() async {
      if (_maskWorker != null && _maskWorker!.isIdle) {
        PGLog.d('📲 通知空闲蒙版专用工作器处理新交互式蒙版任务');
        _assignMaskTaskToWorkerUnsafe(_maskWorker!);
      } else {
        PGLog.d('⏳ 蒙版专用工作器忙碌中，交互式蒙版任务已按排序加入队列');
      }
    });
  }

  /// 移除指定的任务
  Future<bool> removeTask({
    required String inputPath,
    required AigcTaskType taskType,
  }) async {
    final taskId = generateTaskId(inputPath, taskType);

    // 从队列中移除
    final removed = _taskScheduler.removeTaskById(taskId, AigcTask);

    // 🔒 使用锁保护任务跟踪状态 - 从正在处理的跟踪中移除（如果存在）
    final processingRemoved = await _taskTrackingLock.synchronized(() {
      return _processingTasks.remove(taskId) != null;
    });

    if (removed || processingRemoved) {
      PGLog.d(
          '🗑️ 已移除任务: $inputPath ($taskType) - 队列移除:$removed, 处理中移除:$processingRemoved');
    } else {
      PGLog.d('⚠️ 任务不存在，无法移除: $inputPath ($taskType)');
    }

    return removed || processingRemoved;
  }

  /// 根据输入路径移除所有相关任务
  Future<int> removeTasksByPath(String inputPath) async {
    // 从队列中移除
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      return task.inputPath == inputPath;
    });

    // 🔒 使用锁保护任务跟踪状态 - 从正在处理的跟踪中移除
    final processingRemovedCount = await _taskTrackingLock.synchronized(() {
      final processingToRemove = _processingTasks.entries
          .where((entry) => entry.value.inputPath == inputPath)
          .map((entry) => entry.key)
          .toList();

      for (final taskId in processingToRemove) {
        _processingTasks.remove(taskId);
      }

      return processingToRemove.length;
    });

    final totalRemoved = removedCount + processingRemovedCount;
    if (totalRemoved > 0) {
      PGLog.d(
          '🗑️ 已移除 $totalRemoved 个任务，路径: $inputPath (队列:$removedCount, 处理中:$processingRemovedCount)');
    } else {
      PGLog.d('⚠️ 未找到路径相关的任务: $inputPath');
    }

    return totalRemoved;
  }

  /// 根据任务类型移除所有任务
  Future<int> removeTasksByType(AigcTaskType taskType) async {
    // 从队列中移除
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      return task.taskType == taskType;
    });

    // 🔒 使用锁保护任务跟踪状态 - 从正在处理的跟踪中移除
    final processingRemovedCount = await _taskTrackingLock.synchronized(() {
      final processingToRemove = _processingTasks.entries
          .where((entry) => entry.value.taskType == taskType)
          .map((entry) => entry.key)
          .toList();

      for (final taskId in processingToRemove) {
        _processingTasks.remove(taskId);
      }

      return processingToRemove.length;
    });

    final totalRemoved = removedCount + processingRemovedCount;
    if (totalRemoved > 0) {
      PGLog.d(
          '🗑️ 已移除 $totalRemoved 个 ${taskType.displayName} 任务 (队列:$removedCount, 处理中:$processingRemovedCount)');
    } else {
      PGLog.d('⚠️ 未找到 ${taskType.displayName} 类型的任务');
    }

    return totalRemoved;
  }

  /// 根据优先级范围移除任务
  int removeTasksByPriorityRange({
    int? minPriority,
    int? maxPriority,
  }) {
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      final priority = task.priority;

      if (minPriority != null && priority < minPriority) {
        return false;
      }

      if (maxPriority != null && priority > maxPriority) {
        return false;
      }

      return true;
    });

    if (removedCount > 0) {
      final rangeText = '${minPriority ?? '∞'} - ${maxPriority ?? '∞'}';
      PGLog.d('🗑️ 已移除 $removedCount 个优先级在 $rangeText 范围内的任务');
    }

    return removedCount;
  }

  /// 清空所有任务
  Future<void> clearAllTasks() async {
    _taskScheduler.clearAll();

    // 🔒 使用锁保护任务跟踪状态
    await _taskTrackingLock.synchronized(() {
      _processingTasks.clear();
    });

    PGLog.d('🧹 已清空所有任务队列和正在处理的任务');
  }

  /// 获取指定路径的任务数量（所有类型）
  Future<int> getTaskCountByPath(String inputPath) async {
    int count = 0;

    // 计算每种任务类型下指定路径的任务数量（队列中的）
    for (final taskType in AigcTaskType.values) {
      if (await hasTask(inputPath: inputPath, taskType: taskType)) {
        count++;
      }
    }

    // 🔒 使用锁保护任务跟踪状态 - 计算正在处理中的相同路径任务
    final processingCount = await _taskTrackingLock.synchronized(() {
      return _processingTasks.values
          .where((task) => task.inputPath == inputPath)
          .length;
    });

    return count + processingCount;
  }

  /// 获取指定任务类型的任务数量
  Future<int> getTaskCountByType(AigcTaskType taskType) async {
    // 队列中的任务数量
    final queueCount = _taskScheduler.getTaskCount(_getTaskTypeClass(taskType));

    // 🔒 使用锁保护任务跟踪状态 - 正在处理的任务数量
    final processingCount = await _taskTrackingLock.synchronized(() {
      return _processingTasks.values
          .where((task) => task.taskType == taskType)
          .length;
    });

    return queueCount + processingCount;
  }

  /// 检查指定任务是否存在（队列中或正在处理中）
  Future<bool> hasTask({
    required String inputPath,
    required AigcTaskType taskType,
  }) async {
    final taskId = generateTaskId(inputPath, taskType);

    // 检查队列中是否存在
    final inQueue = _taskScheduler.hasTask(taskId, AigcTask);

    // 🔒 使用锁保护任务跟踪状态 - 检查是否正在处理中
    final inProcessing = await _taskTrackingLock.synchronized(() {
      return _processingTasks.containsKey(taskId);
    });

    return inQueue || inProcessing;
  }

  /// 获取所有任务的统计信息
  Future<Map<String, dynamic>> getDetailedStats() async {
    final queueStats = getQueueStats();
    final typeStats = <String, int>{};

    for (final taskType in AigcTaskType.values) {
      typeStats[taskType.displayName] = await getTaskCountByType(taskType);
    }

    // 🔒 使用锁保护任务跟踪状态
    final processingTasksCount = await _taskTrackingLock.synchronized(() {
      return _processingTasks.length;
    });

    return {
      'totalTasks': totalTaskCount,
      'queueStats':
          queueStats.map((key, value) => MapEntry(key.displayName, value)),
      'typeStats': typeStats,
      'currentStrategy': currentStrategyName,
      'workerCount': _workers.length,
      'processingTasks': processingTasksCount, // 新增：正在处理的任务数
      // 'activeWorkers': _workers.where((w) => !w.isIdle).length,
    };
  }

  /// 根据任务类型获取对应的类类型
  Type _getTaskTypeClass(AigcTaskType taskType) {
    switch (taskType) {
      case AigcTaskType.imageAssets:
        return AigcImageAssetsTask;
      case AigcTaskType.cover:
        return AigcCoverTask;
      case AigcTaskType.thumbnail:
        return AigcThumbnailTask;
      case AigcTaskType.mask:
        return AigcMaskTask;
      case AigcTaskType.interactiveMask:
        return AigcInteractiveMaskTask;
      case AigcTaskType.image:
        return AigcImageTask;
      case AigcTaskType.rawConversion:
        return AigcRawConversionTask;
    }
  }

  void setPriorityStrategy(
      SchedulingStrategy<AigcTask, AigcQueueType> strategy) {
    _taskScheduler.setStrategy(strategy);
  }

  /// 获取当前策略名称
  String get currentStrategyName => _taskScheduler.currentStrategyName;

  /// 获取队列统计信息
  Map<AigcQueueType, int> getQueueStats() => _taskScheduler.getQueueStats();

  /// 获取总任务数量
  int get totalTaskCount => _taskScheduler.totalTaskCount;

  /// 清理资源
  @override
  void dispose() {
    // 清理通用工作器
    for (final worker in _workers) {
      worker.dispose();
    }
    _workers.clear();

    // 清理蒙版专用工作器
    _maskWorker?.dispose();
    _maskWorker = null;

    // 🔒 使用锁保护任务跟踪状态 - 清理正在处理的任务跟踪
    _taskTrackingLock.synchronized(() {
      _processingTasks.clear();
    });

    _resultController.close();
    _progressController.close();
    super.dispose();
  }
}
