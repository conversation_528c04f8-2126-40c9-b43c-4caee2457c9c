import 'dart:math' as Math;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_theme_image_preview_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/model/aigc_batch_sample_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/view_model/aigc_batch_sample_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/widgets/aigc_batch_sample_grid_view.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/widgets/aigc_batch_sample_preset_list.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class AigcBatchSampleDialog extends StatefulWidget {
  const AigcBatchSampleDialog(
      {super.key,
      required this.imageProvider,
      required this.multiSelectProvider,
      required this.themeListProvider,
      required this.currentEditingProjectRepository,
      required this.singleCost,
      required this.onBatchSample});
  final AigcEditingImageProvider imageProvider;
  final AigcEditingMultiSelectProvider multiSelectProvider;
  final AigcEditingThemeListProvider themeListProvider;
  final CurrentEditingProjectRepository currentEditingProjectRepository;
  final int singleCost;

  final Future<List<(bool, String?)>> Function({
    required List<AigcBatchSampleModel> sampleModels,
    Function(int current, int total, {required bool success, String? error})?
        onProgress,
  }) onBatchSample;

  @override
  State<AigcBatchSampleDialog> createState() => _AigcBatchSampleDialogState();

  static void show(
      BuildContext context,
      AigcEditingImageProvider imageProvider,
      AigcEditingMultiSelectProvider multiSelectProvider,
      AigcEditingThemeListProvider themeListProvider,
      CurrentEditingProjectRepository currentEditingProjectRepository,
      int singleCost,
      Future<List<(bool, String?)>> Function({
        required List<AigcBatchSampleModel> sampleModels,
        Function(int current, int total,
                {required bool success, String? error})?
            onProgress,
      }) onBatchSample) {
    PGDialog.showCustomDialog(
      child: AigcBatchSampleDialog(
        imageProvider: imageProvider,
        multiSelectProvider: multiSelectProvider,
        themeListProvider: themeListProvider,
        currentEditingProjectRepository: currentEditingProjectRepository,
        singleCost: singleCost,
        onBatchSample: onBatchSample,
      ),
      width: 1280,
      height: 800,
      radius: 16,
      tag: DialogTags.aigcBatchSample,
    );
  }
}

class _AigcBatchSampleDialogState extends State<AigcBatchSampleDialog>
    with SingleTickerProviderStateMixin {
  late AigcBatchSampleViewModel viewModel;
  // 加载动画控制器
  late AnimationController _loadingController;
  bool _startButtonHovered = false;

  // 预览相关状态
  AigcPresetsEffect? _previewCreativeItem;
  bool _showPreview = false;
  late AigcEditingThemeListProvider _themeListProvider;

  // 预览组件位置信息
  double _previewLeft = 0;
  double _previewTop = 0; // 顶部对齐时使用
  double _previewBottom = 0; // 底部对齐时使用
  bool _previewOnLeftSide = false; // 标记预览组件是否在预设列表左侧
  bool _previewUseBottomAlignment = false; // 标记是否使用底部对齐

  @override
  void initState() {
    super.initState();
    viewModel = AigcBatchSampleViewModel(
      imageProvider: widget.imageProvider,
      multiSelectProvider: widget.multiSelectProvider,
      themeListProvider: widget.themeListProvider,
      currentEditingProjectRepository: widget.currentEditingProjectRepository,
      aigcPresetsRepository: context.read<AigcPresetsRepository>(),
      singleCost: widget.singleCost,
    );

    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();

    // 初始化预览相关逻辑
    _themeListProvider = widget.themeListProvider;
    _themeListProvider.addListener(_onPreviewImageHoverChanged);

    // 检查是否有蒙版为空的图片，如果有则显示提示
    if (viewModel.noMaskImageCount > 0) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          PGDialog.showToast('已为你过滤主体区域为空的图片');
        }
      });
    }
  }

  @override
  void dispose() {
    viewModel.disposed = true;
    viewModel.dispose();
    _loadingController.dispose();
    _themeListProvider.removeListener(_onPreviewImageHoverChanged);
    super.dispose();
  }

  /// 处理预览图片悬停变化
  void _onPreviewImageHoverChanged() {
    if (_themeListProvider.hoverEffect != null) {
      setState(() {
        _previewCreativeItem = _themeListProvider.hoverEffect;
        _showPreview = true;
        // 计算预览组件位置
        _calculatePreviewPosition();
      });
    } else {
      setState(() {
        _showPreview = false;
      });
    }
  }

  /// 计算预览组件位置
  void _calculatePreviewPosition() {
    if (!viewModel.showPresetList) {
      return;
    }
    const double dialogWidth = 1280;
    const double dialogHeight = 800; // 对话框高度
    const double presetListWidth = 285;
    const double presetListHeight = 398; // AigcBatchSamplePresetList的高度
    const double spacing = 4;
    const double minMargin = 16;
    const double minPreviewWidth = 500;
    const double maxPreviewWidth = 500;
    const double maxPreviewHeight = 500; // 预览组件的最大高度

    final double presetListLeft = viewModel.presetListLeft;
    final double presetListTop = viewModel.presetListTop;
    final double presetListRight = presetListLeft + presetListWidth;

    // 计算左侧和右侧的可用空间
    final double leftAvailableSpace = presetListLeft - minMargin;
    final double rightAvailableSpace =
        dialogWidth - presetListRight - spacing - minMargin;

    // 计算X轴位置
    if (leftAvailableSpace >= minPreviewWidth + spacing) {
      // 左侧有足够空间，在左侧显示
      _previewOnLeftSide = true;
      _previewLeft =
          Math.max(minMargin, presetListLeft - spacing - maxPreviewWidth);
    } else if (rightAvailableSpace >= minPreviewWidth) {
      // 左侧空间不够，但右侧有足够空间，在右侧显示
      _previewOnLeftSide = false;
      _previewLeft = presetListRight + spacing;
    } else {
      // 两侧空间都有限，选择空间更大的一侧
      if (leftAvailableSpace >= rightAvailableSpace &&
          leftAvailableSpace >= 100) {
        // 左侧空间相对更大，强制在左侧显示
        _previewOnLeftSide = true;
        _previewLeft =
            Math.max(minMargin, presetListLeft - spacing - leftAvailableSpace);
      } else {
        // 右侧空间更大或左侧空间太小，在右侧显示
        _previewOnLeftSide = false;
        _previewLeft = presetListRight + spacing;
      }
    }

    // 计算Y轴位置 - 确保预览组件不超出对话框边界
    _calculatePreviewTopPosition(
      dialogHeight: dialogHeight,
      presetListTop: presetListTop,
      presetListHeight: presetListHeight,
      maxPreviewHeight: maxPreviewHeight,
      minMargin: minMargin,
    );
  }

  /// 计算预览组件的Y轴位置
  void _calculatePreviewTopPosition({
    required double dialogHeight,
    required double presetListTop,
    required double presetListHeight,
    required double maxPreviewHeight,
    required double minMargin,
  }) {
    // 优先尝试顶部对齐
    double topAlignedPosition = presetListTop;
    double previewBottomIfTopAligned = topAlignedPosition + maxPreviewHeight;
    double dialogBottomLimit = dialogHeight - minMargin;

    if (previewBottomIfTopAligned <= dialogBottomLimit) {
      // 顶部对齐不会超出，使用顶部对齐
      _previewUseBottomAlignment = false;
      _previewTop = topAlignedPosition;
    } else {
      // 顶部对齐会超出，使用底部对齐
      _previewUseBottomAlignment = true;

      // 计算预设列表底部位置
      double presetListBottom = presetListTop + presetListHeight;

      // 计算预览组件应该距离对话框底部的距离
      // 使预览组件底部与预设列表底部对齐
      double dialogBottomToPresetBottom = dialogHeight - presetListBottom;
      _previewBottom = dialogBottomToPresetBottom;

      // 确保预览组件不会超出对话框顶部
      double previewTopIfBottomAligned =
          dialogHeight - _previewBottom - maxPreviewHeight;
      if (previewTopIfBottomAligned < minMargin) {
        // 如果底部对齐会导致超出顶部，则调整bottom值
        _previewBottom = dialogHeight - minMargin - maxPreviewHeight;
      }
    }
  }

  /// 构建左侧预览组件（右对齐到预设列表）
  Widget _buildLeftSidePreview() {
    return Container(
      width: viewModel.presetListLeft - _previewLeft - 4,
      alignment: Alignment.centerRight,
      child: AigcThemeImagePreviewWidget(
        creativeItem: _previewCreativeItem!,
        visible: _showPreview,
        onMouseExit: () {
          setState(() {
            _showPreview = false;
          });
        },
      ),
    );
  }

  /// 构建右侧预览组件（左对齐到预设列表）
  Widget _buildRightSidePreview() {
    return AigcThemeImagePreviewWidget(
      creativeItem: _previewCreativeItem!,
      visible: _showPreview,
      onMouseExit: () {
        setState(() {
          _showPreview = false;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AigcBatchSampleViewModel>.value(
      value: viewModel,
      child: Consumer<AigcBatchSampleViewModel>(
        builder: (context, viewModel, child) {
          return Container(
            width: 1280,
            height: 800,
            decoration: BoxDecoration(
              color: const Color(0xFF1F1F1F),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Stack(
              children: [
                GestureDetector(
                  onTap: () {
                    // 点击其他区域时隐藏预设列表
                    if (viewModel.showPresetList && mounted) {
                      viewModel.hidePresetList();
                    }
                  },
                  child: Column(
                    children: [
                      // 标题区域
                      _buildTitleArea(),

                      // 列表区域
                      Expanded(
                        child: _buildListArea(viewModel),
                      ),
                      if (viewModel.sampleList.length > 12)
                        SizedBox(
                          width: double.infinity,
                          height: 1,
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFFFFF).withOpacity(0.1),
                            ),
                          ),
                        ),
                      // 操作区域
                      _buildActionArea(viewModel),
                    ],
                  ),
                ),

                // 预设列表 - 最顶层
                if (viewModel.showPresetList &&
                    viewModel.currentThemeListViewModel != null)
                  Positioned(
                    left: viewModel.presetListLeft,
                    top: viewModel.presetListTop,
                    child: Material(
                      elevation: 8,
                      borderRadius: BorderRadius.circular(8),
                      color: const Color(0xFF2E2E2E),
                      child: AigcBatchSamplePresetList(
                        viewModel: viewModel.currentThemeListViewModel!,
                        onClose: () {
                          if (mounted) {
                            viewModel.hidePresetList();
                          }
                        },
                      ),
                    ),
                  ),

                // 预览组件 - 显示在预设列表旁边
                if (_showPreview &&
                    _previewCreativeItem != null &&
                    viewModel.showPresetList)
                  Positioned(
                    left: _previewLeft,
                    top: _previewUseBottomAlignment ? null : _previewTop,
                    bottom: _previewUseBottomAlignment ? _previewBottom : null,
                    child: _previewOnLeftSide
                        ? _buildLeftSidePreview()
                        : _buildRightSidePreview(),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建标题区域
  Widget _buildTitleArea() {
    return Container(
      width: double.infinity,
      height: 68,
      padding: const EdgeInsets.only(left: 24, right: 16),
      child: Row(
        children: [
          Text(
            '批量处理打样预设',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),
          const Spacer(),
          PlatformMouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                _onCancel();
              },
              child: const SizedBox(
                width: 24,
                height: 24,
                child: Center(
                  child: Icon(
                    Icons.close,
                    color: Color(0xFFE1E2E5),
                    size: 24,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表区域
  Widget _buildListArea(AigcBatchSampleViewModel viewModel) {
    return Container(
      width: double.infinity,
      padding: viewModel.sampleList.length > 12
          ? const EdgeInsets.only(left: 24, right: 16)
          : const EdgeInsets.only(left: 24, right: 24),
      child: AigcBatchSampleGridView(
        viewModel: viewModel,
        onItemReselectPressed: _onItemReselectPressed,
      ),
    );
  }

  /// 构建操作区域
  Widget _buildActionArea(AigcBatchSampleViewModel viewModel) {
    return Container(
      width: double.infinity,
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          // 左侧文案
          Text(
            '已选 ${viewModel.sampleList.length} 张照片，将打样 ${viewModel.sampleList.length * 3} 张',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),

          const Spacer(),

          // // 取消按钮
          // _buildActionButton(
          //   text: '取消',
          //   backgroundColor: const Color(0xFF383838),
          //   textColor: Colors.white,
          //   borderColor: const Color(0xFFFFFFFF).withOpacity(0.1),
          //   onPressed: _onCancel,
          // ),

          // const SizedBox(width: 12),

          // 开始打样按钮
          viewModel.isLoading
              ? _buildLoadingButton()
              : _buildStartButton(viewModel),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required Color borderColor,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 88,
      height: 36,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextButton(
        style: TextButton.styleFrom(
          backgroundColor: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: borderColor, width: 0),
          ),
        ),
        onPressed: onPressed,
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建开始打样按钮
  Widget _buildStartButton(AigcBatchSampleViewModel viewModel) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        if (mounted) {
          setState(() {
            _startButtonHovered = true;
          });
        }
      },
      onExit: (_) {
        if (mounted) {
          setState(() {
            _startButtonHovered = false;
          });
        }
      },
      child: Container(
        width: 160,
        height: 40,
        decoration: BoxDecoration(
          gradient: _startButtonHovered
              ? const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Color(0xFFFFFFFF), // 白色透明度1
                    Color(0x00FFFFFF), // 白色透明度0
                  ],
                )
              : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          margin: _startButtonHovered ? const EdgeInsets.all(1) : null,
          decoration: BoxDecoration(
            color: _startButtonHovered
                ? const Color(0xFFCCCCCC)
                : const Color(0xFF323232),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFF72561).withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // 渐变背景 - 根据悬停状态改变透明度
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFFF72561)
                            .withOpacity(!_startButtonHovered ? 0.3 : 0.8),
                        const Color(0xFFF72561),
                      ],
                    ),
                  ),
                ),
              ),
              // 按钮内容
              Center(
                child: _createSampleButtonContent(viewModel),
              ),
              // 可点击区域
              // 可点击区域
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: GestureDetector(
                    onTap: () async {
                      _onStartSample();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建开始打样按钮内容
  Widget _createSampleButtonContent(AigcBatchSampleViewModel viewModel,
      {bool isRedo = false}) {
    final themeColor =
        isRedo ? const Color(0xFFFF9EB9) : const Color(0xFFFFFFFF);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '开始打样',
          style: TextStyle(
            color: themeColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 18 / 14,
            fontFamily: Fonts.defaultFontFamily,
          ),
        ),
        const SizedBox(width: 5),
        Image.asset(
          'assets/icons/coin_score_style.png',
          width: 20,
          height: 20,
          color: themeColor,
        ),
        Text(
          viewModel.allCostValue.toString(),
          style: TextStyle(
            color: themeColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 18 / 14,
            fontFamily: Fonts.defaultFontFamily,
          ),
        ),
      ],
    );
  }

  /// 构建加载中按钮
  Widget _buildLoadingButton() {
    return Container(
      width: 160,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFF333333),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: RotationTransition(
          turns: _loadingController,
          child: Image.asset(
            'assets/icons/icon_loading.png',
            width: 24,
            height: 24,
          ),
        ),
      ),
    );
  }

  /// 取消操作
  void _onCancel() {
    if (mounted) {
      PGDialog.dismiss(tag: DialogTags.aigcBatchSample);
    }
  }

  /// 开始打样操作
  void _onStartSample() async {
    // 防止重复点击
    if (viewModel.isLoading) {
      return;
    }
    // 获取当前可用积分
    final availableAIGCCount =
        context.read<AccountRepository>().accountAll?.aigcInfo?.available ?? 0;
    final cost = viewModel.allCostValue;
    // 检查积分是否足够
    if (availableAIGCCount < cost) {
      PGDialog.showToast('积分不足, 请购买积分后再试');
      return;
    }
    // 设置加载状态
    viewModel.setLoading(loading: true);

    try {
      // 调用批量打样方法，结果处理已移到AigcEditingOperationWidget中统一处理
      final results = await widget.onBatchSample(
        sampleModels: viewModel.sampleList,
        onProgress: (current, total, {required success, String? error}) {
          // TODO: 可以在这里更新进度显示
        },
      );
      // 获取创建失败的个数
      int failedCount = results.where((element) => !element.$1).length;
      if (failedCount > 0) {
        if (mounted) {
          PGDialog.showToast('$failedCount个打样创建失败，请稍后再试');
        }
      }
    } catch (e) {
      if (mounted) {
        PGDialog.showToast('批量打样失败: $e');
      }
    } finally {
      // 重置加载状态
      viewModel.setLoading(loading: false);
      _onCancel();
    }
  }

  /// 处理item重新选择按钮点击
  void _onItemReselectPressed(int index, GlobalKey itemKey) {
    // 先调用ViewModel处理业务逻辑，返回值表示是否需要显示预设列表
    final shouldShowPresetList = viewModel.onItemReselect(index);

    // 如果不需要显示预设列表（比如隐藏操作），直接返回
    if (!shouldShowPresetList) {
      return;
    }

    // 获取item的位置
    final RenderBox? renderBox =
        itemKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return;
    }

    // 获取对话框容器的位置
    final RenderBox? dialogRenderBox = context.findRenderObject() as RenderBox?;
    if (dialogRenderBox == null) {
      return;
    }

    final Offset itemGlobalPosition = renderBox.localToGlobal(Offset.zero);
    final Offset dialogGlobalPosition =
        dialogRenderBox.localToGlobal(Offset.zero);
    final Size itemSize = renderBox.size;

    // 计算item相对于对话框的位置
    final double itemRelativeX =
        itemGlobalPosition.dx - dialogGlobalPosition.dx;
    final double itemRelativeY =
        itemGlobalPosition.dy - dialogGlobalPosition.dy;

    // 计算并设置预设列表位置
    viewModel.calculatePresetListPosition(
      itemRelativeX: itemRelativeX,
      itemRelativeY: itemRelativeY,
      itemWidth: itemSize.width,
      itemHeight: itemSize.height,
    );
  }
}
