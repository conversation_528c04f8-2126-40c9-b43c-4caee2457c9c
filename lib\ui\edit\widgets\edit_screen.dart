import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/edit/services/edit_action_service.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/ui/edit/view_model/edit_view_model.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/unity/widgets/turing_unity_widget.dart';
import 'package:turing_art/utils/screenshot_protection.dart';

class EditScreen extends StatefulWidget {
  const EditScreen({
    super.key,
    required this.projectId,
    this.title = 'EditScreen',
  });

  final String? projectId;
  final String title;

  @override
  State<EditScreen> createState() => _EditScreenState();
}

class _EditScreenState extends State<EditScreen> {
  late final ProjectStateProvider _projectStateProvider;

  @override
  void initState() {
    super.initState();
    _enableScreenshotProtection();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _projectStateProvider = context.read<ProjectStateProvider>();
  }

  @override
  void dispose() {
    _disableScreenshotProtection();
    _clearProjectState();
    super.dispose();
  }

  Future<void> _enableScreenshotProtection() async {
    await ScreenshotProtection.enable();
  }

  Future<void> _disableScreenshotProtection() async {
    await ScreenshotProtection.disable();
  }

  // 清除项目状态
  void _clearProjectState() {
    _projectStateProvider.exitEdit();
  }

  @override
  Widget build(BuildContext context) {
    String projectId = widget.projectId ?? '';
    if (projectId.isEmpty) {
      return const Center(child: Text('项目ID不能为空'));
    }
    return ChangeNotifierProvider(
      create: (context) {
        return EditViewModel(
          projectId,
          context.read<ProjectRepository>(),
          context.read<UnityController>(),
          context.read<UnityUseCaseProvider>(),
          context.read<ExportUseCaseProvider>(),
          context.read<ExportProjectProvider>(),
          context.read<ExportTaskStateProvider>(),
          context.read<NoviceGuideManager>(),
          EditActionService(
            context.read<ProjectRepository>(),
            context.read<CurrentUserRepository>(),
            context.read<ProjectStateProvider>(),
            GoRouterNavigatorService(context),
          ),
        );
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          title: Text(widget.title),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Consumer<EditViewModel>(
      builder: (context, viewModel, child) {
        return FutureBuilder<ProjectInfo?>(
          future: viewModel.getProjectInfo(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('加载错误: ${snapshot.error}'));
            }

            final project = snapshot.data;
            if (project == null) {
              return const Center(child: Text('项目不存在'));
            }

            return Stack(
              children: [
                TuringUnityWidget(
                  onCreatedCallback: (manager) {},
                ),
                // 处理外部消息的监听器
                if (viewModel.hasPendingExternalMessages)
                  Positioned.fill(
                    child: Builder(
                      builder: (context) {
                        WidgetsBinding.instance.addPostFrameCallback((_) async {
                          if (mounted) {
                            await viewModel
                                .processPendingExternalMessages(context);
                          }
                        });
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
