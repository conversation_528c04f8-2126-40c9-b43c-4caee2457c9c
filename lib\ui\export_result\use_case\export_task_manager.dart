import 'package:flutter/material.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/ui/export_result/use_case/export_task_events.dart';
import 'package:turing_art/ui/export_result/use_case/export_use_case_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出任务管理器
/// 基于事件系统工作，只负责处理导出任务事件，不直接依赖任何外部状态提供者
class ExportTaskManager extends ChangeNotifier
    implements ExportTaskEventListener {
  final ExportUseCaseProvider _exportUseCaseProvider;
  final ExportTaskEventBus _eventBus;

  // 保存暂停的导出任务ID列表
  List<String> _pausedTaskIds = [];

  /// 获取当前暂停的任务ID列表
  List<String> get pausedTaskIds => List.unmodifiable(_pausedTaskIds);

  ExportTaskManager({
    required UnityController unityController,
    required ExportTaskEventBus eventBus,
  })  : _exportUseCaseProvider =
            ExportUseCaseProvider(unityController: unityController),
        _eventBus = eventBus {
    // 注册为事件监听器
    _eventBus.addEventListener(this);
    PGLog.d('ExportTaskManager 初始化成功，已注册为事件监听器');
  }

  /// 实现ExportTaskEventListener接口 - 处理导出任务事件
  @override
  Future<void> onExportTaskEvent(ExportTaskEvent event) async {
    try {
      PGLog.d('ExportTaskManager: 收到事件: $event');

      switch (event.type) {
        case ExportTaskEventType.pauseAll:
          await _handlePauseAllEvent(event);
          break;
        case ExportTaskEventType.resumePaused:
          await _handleResumePausedEvent(event);
          break;
      }
    } catch (e) {
      PGLog.e('ExportTaskManager: 处理事件失败: $e');
    }
  }

  /// 处理暂停所有导出任务事件
  Future<void> _handlePauseAllEvent(ExportTaskEvent event) async {
    try {
      PGLog.d('ExportTaskManager: 处理暂停事件，来源: ${event.source}');

      // 暂停所有任务
      final result = await _exportUseCaseProvider.pauseAllExportTasks.invoke();
      _pausedTaskIds = result.map((task) => task.guid).toList();

      PGLog.d('成功暂停所有导出任务，来源: ${event.source}，暂停任务数: ${_pausedTaskIds.length}');

      // 通知UI更新
      notifyListeners();
    } catch (e) {
      PGLog.e('暂停所有导出任务失败: $e');
      _pausedTaskIds = [];
    }
  }

  /// 处理恢复暂停的导出任务事件
  Future<void> _handleResumePausedEvent(ExportTaskEvent event) async {
    if (_pausedTaskIds.isEmpty) {
      PGLog.d('ExportTaskManager: 没有需要恢复的导出任务');
      return;
    }

    try {
      PGLog.d(
          'ExportTaskManager: 处理恢复事件，来源: ${event.source}，恢复任务数: ${_pausedTaskIds.length}');

      // 恢复指定的任务
      await _exportUseCaseProvider.resumeExportTasks.invoke(_pausedTaskIds);
      PGLog.d('成功恢复导出任务: $_pausedTaskIds，来源: ${event.source}');

      // 清空保存的任务ID
      _pausedTaskIds = [];

      // 通知UI更新
      notifyListeners();
    } catch (e) {
      PGLog.e('恢复导出任务失败: $e');
    }
  }

  /// 手动暂停所有导出任务
  Future<void> pauseAllExportTasks() async {
    await _eventBus.pauseAllExportTasks(
      source: ExportTaskEventSource.manualOperation,
      metadata: {
        'reason': 'manual_pause',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 手动恢复所有导出任务
  Future<void> resumeAllExportTasks() async {
    await _eventBus.resumePausedExportTasks(
      source: ExportTaskEventSource.manualOperation,
      metadata: {
        'reason': 'manual_resume',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 获取事件总线实例（供外部使用）
  ExportTaskEventBus get eventBus => _eventBus;

  @override
  void dispose() {
    try {
      _eventBus.removeEventListener(this);
      PGLog.d('ExportTaskManager 已销毁');
    } catch (e) {
      PGLog.e('销毁 ExportTaskManager 失败: $e');
    }
    super.dispose();
  }
}
