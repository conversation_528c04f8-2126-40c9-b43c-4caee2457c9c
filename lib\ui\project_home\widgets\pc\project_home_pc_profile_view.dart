import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/components/pc_hover_widget.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_integral_info_widget.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_view_model.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'project_home_pc_version_intro_view.dart';

class ProjectHomePcProfileView extends StatefulWidget {
  // 用户账号点击
  final VoidCallback? onUserAccountClick;
  // 完善信息按钮点击
  final VoidCallback? onCompleteAccountInfoClick;
  // 购买按钮点击
  final Function(PurchaseTabType?)? onPurchaseClick;
  // 全部项目按钮点击
  final VoidCallback? onAllProjectsClick;
  // 快捷指南点击
  final VoidCallback? onGuideClick;
  // 快捷键点击
  final VoidCallback? onShortKeyClick;
  // 联系客服点击
  final VoidCallback? onCustomerServiceClick;
  // 检查更新点击
  final VoidCallback? onCheckUpdateClick;
  // 版本介绍点击
  final VoidCallback? onVersionIntroduceClick;
  // 导入预设点击
  final VoidCallback? onImportPreset;
  // 用户卡片的 GlobalKey，如果不提供则使用默认的
  final GlobalKey? userCardKey;

  static final GlobalKey _defaultUserCardKey = GlobalKey();

  // 获取用户卡片的 GlobalKey
  GlobalKey get effectiveUserCardKey => userCardKey ?? _defaultUserCardKey;

  const ProjectHomePcProfileView({
    super.key,
    this.onUserAccountClick,
    this.onCompleteAccountInfoClick,
    this.onPurchaseClick,
    this.onAllProjectsClick,
    this.onGuideClick,
    this.onShortKeyClick,
    this.onCustomerServiceClick,
    this.onCheckUpdateClick,
    this.onVersionIntroduceClick,
    this.onImportPreset,
    this.userCardKey,
  });

  @override
  State<ProjectHomePcProfileView> createState() =>
      _ProjectHomePcProfileViewState();
}

class _ProjectHomePcProfileViewState extends State<ProjectHomePcProfileView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 318,
      color: const Color(0xFF0D0D0D),
      child: Stack(
        children: [
          // 相对布局
          Padding(
            padding: const EdgeInsets.only(top: 22, left: 24, right: 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息栏
                Consumer<ProfileDialogViewModel>(
                  builder: (context, viewModel, child) {
                    return _buildUserInfoBarColumn(viewModel);
                  },
                ),
                // 主账号信息栏（只在子账号情况下显示）
                Consumer<ProfileDialogViewModel>(
                  builder: (context, viewModel, child) {
                    final isSubAccount = viewModel.isSubAccount;
                    if (isSubAccount) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: _buildMainUserInfoBarColumn(viewModel),
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  },
                ),
                const SizedBox(height: 8),
                // 完善信息按钮
                Consumer<ProfileDialogViewModel>(
                  builder: (context, viewModel, child) {
                    // 只有当按钮需要显示时，才添加间距和按钮
                    if (!viewModel.isFinishInit) {
                      // 未完成初始化，依赖数据没有准备完不展示按钮，防止从有到无，初始化完成会刷新 UI
                      return const SizedBox.shrink();
                    }
                    return viewModel.getNeedShowWechatGiftDialog()
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildCompleteInfoButtonColumn(),
                              const SizedBox(height: 8),
                            ],
                          )
                        : const SizedBox.shrink(); // 不显示按钮时不添加任何间距
                  },
                ),
                const SizedBox(height: 8),
                // 积分卡片容器（包含背景图片、剩余次数和积分信息）
                _buildIntegralCardColumn(),
                const SizedBox(height: 16),

                // 购买套餐卡片
                Consumer<ProfileDialogViewModel>(
                  builder: (context, viewModel, child) {
                    return viewModel.isSubAccount
                        ? const SizedBox.shrink()
                        : _buildPackageCardColumn();
                  },
                ),
                Consumer<ProfileDialogViewModel>(
                  builder: (context, viewModel, child) {
                    return viewModel.isSubAccount
                        ? const SizedBox.shrink()
                        : const SizedBox(height: 16);
                  },
                ), // 全部项目按钮
                _buildAllProjectsButtonColumn(),
              ],
            ),
          ),

          // 左下角版本介绍相关 view (保持原有的定位方式)
          Positioned(
            left: 0,
            bottom: 0,
            child: ProjectHomePcVersionIntroView(
              onGuideClick: () => widget.onGuideClick?.call(),
              onShortKeyClick: () => widget.onShortKeyClick?.call(),
              onCustomerServiceClick: () =>
                  widget.onCustomerServiceClick?.call(),
              onCheckUpdateClick: () => widget.onCheckUpdateClick?.call(),
              onVersionIntroduceClick: () =>
                  widget.onVersionIntroduceClick?.call(),
            ),
          ),
        ],
      ),
    );
  }

  // 用户信息栏
  Widget _buildUserInfoBarColumn(ProfileDialogViewModel viewModel) {
    final isSubAccount = viewModel.role == UserRole.employee;

    return PlatformMouseRegion(
      key: widget.effectiveUserCardKey,
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: widget.onUserAccountClick,
        child: PcHoverWidget(
          builder: (context, isHovered) => SizedBox(
            width: 296,
            height: 48,
            child: Container(
              decoration: BoxDecoration(
                color: isHovered
                    ? Colors.white.withOpacity(0.05)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(width: 8),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF121315),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/icons/home_profile_avatar.png',
                        width: 16,
                        height: 16,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    viewModel.mobile ?? "",
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: Fonts.medium,
                      color: const Color(0xFFE1E2E5),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // 子账号标签
                  if (isSubAccount)
                    Container(
                      width: 40,
                      height: 20,
                      margin: const EdgeInsets.only(top: 2),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                          width: 0.5,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 2, vertical: 2),
                      child: Center(
                        child: Text(
                          '子账号',
                          style: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: FontWeight.w400,
                            fontSize: 10,
                            height: 1.0,
                            color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                          ),
                        ),
                      ),
                    ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      size: 20,
                      color: Colors.white.withOpacity(0.4),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 主账号信息栏
  Widget _buildMainUserInfoBarColumn(ProfileDialogViewModel viewModel) {
    return Container(
      width: 280,
      height: 32,
      margin: const EdgeInsets.only(left: 8), // 30px - 22px = 8px
      decoration: BoxDecoration(
        color: const Color(0xFF121315),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          const SizedBox(width: 6),
          Container(
            width: 44,
            height: 20,
            decoration: BoxDecoration(
              color: const Color(0xFF1B1C1F),
              borderRadius: BorderRadius.circular(2),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
            alignment: Alignment.center,
            child: Text(
              '主账号',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: FontWeight.w400,
                fontSize: 12,
                height: 16 / 12, // 行高 16px 除以字体大小 12px
                color: const Color(0xA6EBEDF5), // #EBEDF5 65%
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            viewModel.mainAccountMobile ?? "",
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: FontWeight.w500,
              fontSize: 12,
              height: 18 / 12, // 行高 18px 除以字体大小 12px
              color: const Color(0xA6EBEDF5), // #EBEDF5 65%
            ),
          ),
        ],
      ),
    );
  }

  // 完善信息按钮
  Widget _buildCompleteInfoButtonColumn() {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: widget.onCompleteAccountInfoClick,
        child: PcHoverWidget(
          builder: (context, isHovered) => Container(
            width: 280,
            height: 32,
            decoration: BoxDecoration(
              color: isHovered
                  ? const Color(0xFFF72561).withOpacity(0.15)
                  : const Color(0xFFF72561).withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(
                    '完善信息，免费获得张数', // 目前服务端下发是否显示
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      color: const Color(0xFFF72561),
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.only(right: 6),
                  child: Image.asset(
                    'assets/icons/home_profile_editInfoNext.png',
                    width: 20,
                    height: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 积分卡片
  Widget _buildIntegralCardColumn() {
    return FutureBuilder<bool>(
      future: context.read<AigcEntranceManager>().isAigcUser(),
      builder: (context, snapshot) {
        final bool isAigcUser = snapshot.data ?? false;
        final double cardHeight = isAigcUser ? 112 : 68; // 非AIGC用户减去44px高度

        return Container(
          width: 280,
          height: cardHeight,
          padding:
              const EdgeInsets.only(top: 12, bottom: 0, left: 12, right: 12),
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/icons/integral_account_bg.png'),
              fit: BoxFit.cover,
            ),
            border: Border.all(
              color: const Color(0x1AFFFFFF), // #FFFFFF1A
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Consumer<ProfileDialogViewModel>(
            builder: (context, viewModel, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 剩余次数显示 - 高度28px
                  SizedBox(
                    height: 28,
                    child: _buildRemainingCountColumn(viewModel),
                  ),
                  // 剩余张数标签 - 高度14px
                  SizedBox(
                    height: 14,
                    child: _buildRemainingLabelColumn(),
                  ),
                  // 只有AIGC用户才显示积分信息组件
                  if (isAigcUser) ...[
                    const SizedBox(height: 11), // 间距11px
                    // 积分信息组件 - 高度44px
                    SizedBox(
                      height: 44,
                      child: Consumer<ProfileDialogViewModel>(
                        builder: (context, profileViewModel, child) {
                          if (profileViewModel.aigcInfo == null) {
                            return const SizedBox.shrink();
                          }
                          return ProjectHomeIntegralInfoWidget(
                            integralCount: profileViewModel.integralCount,
                            isNeedPurchase: !profileViewModel.isSubAccount,
                            onBuyIntegralClick: () => widget.onPurchaseClick
                                ?.call(PurchaseTabType.aigcPoint),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        );
      },
    );
  }

  // 购买套餐卡片
  Widget _buildPackageCardColumn() {
    return Container(
      width: 280,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFFFFFF).withOpacity(0.06),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          // 背景 Logo
          Positioned(
            left: 32.51,
            top: 5.36,
            child: Image.asset(
              'assets/icons/profile_back_logo1.png',
              width: 69,
              height: 75,
              fit: BoxFit.cover,
            ),
          ),
          // 购买套餐文字
          Positioned(
            left: 24,
            top: 20,
            child: Text(
              '购买套餐',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 14,
                fontWeight: Fonts.medium,
                color: const Color(0xFFE1E2E5),
                height: 1.4,
              ),
            ),
          ),
          // 获取更多导出张数
          Positioned(
            left: 24,
            top: 43,
            child: Text(
              '获取更多导出张数',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                fontWeight: Fonts.regular,
                color: const Color(0xFFA5A5A7),
                height: 1.4,
              ),
            ),
          ),
          // 立即查看按钮
          Positioned(
            left: 184,
            top: 24,
            child: PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              child: DebounceClickWidget(
                onTap: () => widget.onPurchaseClick?.call(null),
                child: PcHoverWidget(
                  builder: (context, isHovered) => Container(
                    width: 72,
                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: isHovered
                          ? Colors.white.withOpacity(0.05)
                          : Colors.transparent,
                      border: Border.all(
                        color: const Color(0x1AA8F21D),
                        width: 1,
                      ),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x26A8F21D),
                          blurRadius: 16,
                          offset: Offset(0, 4),
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '立即查看',
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontSize: 12,
                        fontWeight: Fonts.medium,
                        color: const Color(0xFFA8F21D),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 全部项目按钮 (Column 版本)
  Widget _buildAllProjectsButtonColumn() {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: widget.onAllProjectsClick,
        child: PcHoverWidget(
          builder: (context, isHovered) => Container(
            width: 280,
            height: 48,
            decoration: BoxDecoration(
              color: isHovered
                  ? Color.alphaBlend(
                      Colors.white.withOpacity(0.05),
                      const Color(0xFF202020),
                    )
                  : const Color(0xFF202020),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                Positioned(
                  left: 12,
                  top: 12,
                  child: Image.asset(
                    'assets/icons/home_profile_allProject.png',
                    width: 24,
                    height: 24,
                  ),
                ),
                Positioned(
                  left: 44,
                  top: 14,
                  child: Text(
                    '全部项目',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                      color: const Color(0xFFFFFFFF),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 剩余次数显示
  Widget _buildRemainingCountColumn(ProfileDialogViewModel viewModel) {
    if (viewModel.editExportInfo == null) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          '${viewModel.editExportInfo?.available ?? 0}',
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 24,
            fontWeight: Fonts.medium,
            color: const Color(0xFFE1E2E5),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '/ ${viewModel.editExportInfo?.total ?? 0}',
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 12,
            color: const Color(0xFFE1E2E5),
          ),
        ),
      ],
    );
  }

  // 剩余张数标签
  Widget _buildRemainingLabelColumn() {
    return Text(
      '剩余张数/总张数',
      style: TextStyle(
        fontFamily: Fonts.defaultFontFamily,
        fontSize: 10,
        fontWeight: Fonts.regular,
        color: const Color(0xFFA5A5A7).withOpacity(0.5),
        height: 16 / 10,
      ),
    );
  }
}
