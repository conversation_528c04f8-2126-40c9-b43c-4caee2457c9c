import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/executor/database_executor.dart';
import 'package:turing_art/datalayer/service/database/operation/workspace_file_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/workspace_operations.dart';

import 'db_operater.dart';

extension WorkspaceDbOperater on DbOperater {
  Future<WorkspaceEntityData?> getWorkspaceById(String workspaceId) async {
    return DatabaseExecutor.execute<WorkspaceEntityData?>(
      GetWorkspaceByIdOperation().operation,
      {'workspaceId': workspaceId},
    );
  }

  Future<List<WorkspaceEntityData>> getWorkspaceByIds(
      List<String> projectIds) async {
    return DatabaseExecutor.execute<List<WorkspaceEntityData>>(
      GetWorkspaceByIdsOperation().operation,
      {'workspaceIds': projectIds},
    );
  }

  Future<void> updateWorkspace(WorkspaceEntityCompanion workspace) async {
    return DatabaseExecutor.execute<void>(
      UpdateWorkspaceOperation().operation,
      {'workspace': workspace},
    );
  }

  Future<void> deleteWorkspace(String workspaceId) async {
    return DatabaseExecutor.execute<void>(
      DeleteWorkspaceOperation().operation,
      {'workspaceId': workspaceId},
    );
  }

  Future<void> deleteWorkspaces(List<String> workspaceIds) async {
    if (workspaceIds.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      DeleteWorkspacesOperation().operation,
      {'workspaceIds': workspaceIds},
    );
  }

  // Workspace file related operations
  /// 加载所有文件，支持排序
  /// [sortField] 排序字段，0=createTime, 1=lastEditTime，默认为 0
  /// [sortOrder] 排序顺序，0=ascending, 1=descending，默认为 0
  ///
  /// 使用示例：
  /// ```dart
  /// // 1. 默认按创建时间正序排列
  /// final files = await dbOperater.loadAllFiles();
  ///
  /// // 2. 按创建时间正序排列
  /// final files = await dbOperater.loadAllFiles(
  ///   sortField: SortField.createTime.value, // 0
  ///   sortOrder: SortOrder.ascending.value,  // 0
  /// );
  ///
  /// // 3. 按最后编辑时间倒序排列（最新的在前）
  /// final files = await dbOperater.loadAllFiles(
  ///   sortField: SortField.lastEditTime.value, // 1
  ///   sortOrder: SortOrder.descending.value,   // 1
  /// );
  ///
  /// // 4. 按创建时间倒序排列
  /// final files = await dbOperater.loadAllFiles(
  ///   sortField: SortField.createTime.value,   // 0
  ///   sortOrder: SortOrder.descending.value,   // 1
  /// );
  /// ```
  Future<List<WorkspaceFileEntityData>> loadAllFiles({
    int sortField = 0,
    int sortOrder = 0,
  }) async {
    return DatabaseExecutor.execute<List<WorkspaceFileEntityData>>(
      GetAllFilesOperation().operation,
      {
        'sortField': sortField,
        'sortOrder': sortOrder,
      },
    );
  }

  Future<List<WorkspaceEntityData>> loadAllWorkspaces({
    int sortField = 0,
    int sortOrder = 0,
  }) async {
    return DatabaseExecutor.execute<List<WorkspaceEntityData>>(
      GetAllWorkspacesOperation().operation,
      {'sortField': sortField, 'sortOrder': sortOrder},
    );
  }

  // Workspace file related operations
  Future<List<WorkspaceFileEntityData>> loadFiles(
    String workspaceId, {
    int sortField = 0,
    int sortOrder = 0,
  }) async {
    return DatabaseExecutor.execute<List<WorkspaceFileEntityData>>(
      GetFilesByWorkspaceIdOperation().operation,
      {
        'workspaceId': workspaceId,
        'sortField': sortField,
        'sortOrder': sortOrder
      },
    );
  }

  Future<WorkspaceFileEntityData?> getWorkspaceFile(
    String workspaceId,
    String fileId,
  ) async {
    return DatabaseExecutor.execute<WorkspaceFileEntityData?>(
      GetFileByWorkspaceIdAndFileIdOperation().operation,
      {'workspaceId': workspaceId, 'fileId': fileId},
    );
  }

  Future<WorkspaceFileEntityData?> getFileByOrgPath(
    String workspaceId,
    String orgPath,
  ) async {
    return DatabaseExecutor.execute<WorkspaceFileEntityData?>(
      GetFileByWorkspaceIdAndOrgPathOperation().operation,
      {'workspaceId': workspaceId, 'orgPath': orgPath},
    );
  }

  Future<List<WorkspaceFileEntityData>> getFilesByOrgPaths(
    String workspaceId,
    List<String> orgPaths,
  ) async {
    if (orgPaths.isEmpty) {
      return [];
    }
    return DatabaseExecutor.execute<List<WorkspaceFileEntityData>>(
      GetFilesByWorkspaceIdAndOrgPathsOperation().operation,
      {'workspaceId': workspaceId, 'orgPaths': orgPaths},
    );
  }

  Future<void> insertFile(WorkspaceFileEntityCompanion file) async {
    return DatabaseExecutor.execute<void>(
      InsertWorkspaceFileOperation().operation,
      {'workspaceFile': file},
    );
  }

  Future<void> insertFiles(List<WorkspaceFileEntityCompanion> files) async {
    if (files.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      InsertWorkspaceFilesOperation().operation,
      {'workspaceFiles': files},
    );
  }

  Future<void> updateFile(WorkspaceFileEntityCompanion file) async {
    return DatabaseExecutor.execute<void>(
      UpdateWorkspaceFileOperation().operation,
      {'workspaceFile': file},
    );
  }

  Future<void> updateFiles(List<WorkspaceFileEntityCompanion> files) async {
    if (files.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      UpdateWorkspaceFilesOperation().operation,
      {'workspaceFiles': files},
    );
  }

  Future<void> deleteFile(String workspaceId, String fileId) async {
    return DatabaseExecutor.execute<void>(
      DeleteWorkspaceFileOperation().operation,
      {'workspaceId': workspaceId, 'fileId': fileId},
    );
  }

  Future<void> deleteFiles(String workspaceId, List<String> fileIds) async {
    if (fileIds.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      DeleteWorkspaceFilesOperation().operation,
      {'workspaceId': workspaceId, 'fileIds': fileIds},
    );
  }

  Future<void> deleteAllWorkspacesFiles(List<String> workspaceIds) async {
    if (workspaceIds.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      DeleteAllWorkspacesFilesOperation().operation,
      {'workspaceIds': workspaceIds},
    );
  }

  // Combined operations
  Future<void> updateWorkspaceAndFiles(
    WorkspaceEntityCompanion workspace,
    List<WorkspaceFileEntityCompanion> files,
  ) async {
    await DatabaseExecutor.execute<void>(
      UpdateWorkspaceAndFilesOperation().operation,
      {'workspace': workspace, 'files': files},
    );
  }

  Future<void> deleteWorkspaceAndFiles(
    WorkspaceEntityCompanion workspace,
    List<String> fileIds,
  ) async {
    await DatabaseExecutor.execute<void>(
      DeleteWorkspaceAndFilesOperation().operation,
      {
        'workspace': workspace,
        'workspaceId': workspace.workspaceId.value,
        'fileIds': fileIds,
      },
    );
  }
}
