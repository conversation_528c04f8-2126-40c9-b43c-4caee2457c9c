import 'dart:ffi';

/// 错误码枚举
enum RawConversionErrorCode {
  success(0),
  invalidParams(-1),
  initFailed(-2),
  processingFailed(-3),
  unsupportedFormat(-4),
  fileNotFound(-5),
  outOfMemory(-6),
  unknown(-999);

  const RawConversionErrorCode(this.value);
  final int value;

  static RawConversionErrorCode fromValue(int value) {
    return RawConversionErrorCode.values
        .firstWhere((e) => e.value == value, orElse: () => unknown);
  }
}

/// 调色模式枚举
enum RawConversionAdjustType {
  auto(0),
  normal(1),
  style(2);

  const RawConversionAdjustType(this.value);
  final int value;
}

/// 配置结构体
final class RawConversionConfig extends Struct {
  @Int32()
  external int enableRawLut;

  @Int32()
  external int enableDenoise;

  @Int32()
  external int enableStdLut;

  @Int32()
  external int adjustType;

  @Int32()
  external int threads;

  @Float()
  external double strength;
}

/// Flutter配置类
class RawConversionConfigData {
  final bool enableRawLut;
  final bool enableDenoise;
  final bool enableStdLut;
  final RawConversionAdjustType adjustType;
  final int threads;
  final double strength;

  const RawConversionConfigData({
    this.enableRawLut = false,
    this.enableDenoise = false,
    this.enableStdLut = true,
    this.adjustType = RawConversionAdjustType.style,
    this.threads = -1, // -1表示自动
    this.strength = 1.0, // 效果强度，默认为1.0
  });

  static const RawConversionConfigData defaultConfig =
      RawConversionConfigData();
}

/// 结果类
class RawConversionResult {
  final RawConversionErrorCode errorCode;
  final String? errorMessage;
  final bool isSuccess;
  final int? adjustMode;

  const RawConversionResult({
    required this.errorCode,
    this.errorMessage,
    this.adjustMode,
  }) : isSuccess = errorCode == RawConversionErrorCode.success;

  @override
  String toString() {
    return 'RawConversionResult(errorCode: $errorCode, errorMessage: $errorMessage, isSuccess: $isSuccess, adjustMode: $adjustMode)';
  }
}
