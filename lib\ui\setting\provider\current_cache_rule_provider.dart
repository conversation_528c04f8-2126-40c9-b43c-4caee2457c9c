import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

class ProjectCacheRule {
  final String module;
  final List<String> list;

  ProjectCacheRule({required this.module, required this.list});

  factory ProjectCacheRule.fromJson(Map<String, dynamic> json) {
    return ProjectCacheRule(
      module: json['module'],
      list: List<String>.from(json['list']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'module': module,
      'list': list,
    };
  }
}

class ProjectCacheRuleProvider extends ChangeNotifier {
  List<ProjectCacheRule> _cacheRule = [];

  List<ProjectCacheRule> get cacheRule => _cacheRule;

  void onProjectCacheRuleUpdate(MessageFromUnity message) {
    try {
      // 解析 JSON 字符串
      final String jsonStr = message.args![0] as String;
      final List<dynamic> cacheRule = json.decode(jsonStr) as List<dynamic>;
      final newCacheRule =
          cacheRule.map((e) => ProjectCacheRule.fromJson(e)).toList();
      _cacheRule = newCacheRule;
      notifyListeners();
      PGLog.d(
          'CurrentCacheRuleProvider: 缓存规则已更新，旧规则: $_cacheRule，新规则: $newCacheRule');
    } catch (e) {
      PGLog.e('CurrentCacheRuleProvider.onCurrentCacheRuleUpdate: $e');
    }
  }
}
