import 'package:cross_file/cross_file.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/ui/common/drag_event_handler/servers/drag_drop_service.dart';
import 'package:turing_art/ui/core/widgets/overlay/drag_forbidden_cursor_overlay.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';

class DragEventHandlerViewModel extends ChangeNotifier {
  // 拖拽状态
  Offset _dragPosition = Offset.zero;
  bool _isDragActive = false;
  bool _isDragOverlayVisible = false;
  bool _shouldShowOverlay = false;
  bool _isProcessFiles = false;
  // DropTarget 启用状态
  bool _isEnabled = true;

  // 服务
  final DragDropService _dragDropService = DragDropService.forPlatform();

  // Getters
  Offset get dragPosition => _dragPosition;
  bool get isDragActive => _isDragActive;
  bool get isDragOverlayVisible => _isDragOverlayVisible;
  bool get shouldShowOverlay => _shouldShowOverlay;
  bool get isEnabled => _isEnabled;

  /// 启用 DropTarget
  void enable() {
    if (!_isEnabled) {
      _isEnabled = true;
      PGLog.d('DragEventHandlerViewModel - 启用拖拽功能');
      notifyListeners();
    }
  }

  /// 禁用 DropTarget
  void disable() {
    if (_isEnabled) {
      _isEnabled = false;
      PGLog.d('DragEventHandlerViewModel - 禁用拖拽功能');

      // 如果当前有拖拽活动，清理状态
      if (_isDragActive) {
        _isDragActive = false;
        _hideDragForbiddenOverlay();
      }

      notifyListeners();
    }
  }

  /// 处理拖拽进入事件
  void onDragEntered(Offset position, bool Function()? canProcessFiles) {
    if (!_isEnabled) {
      PGLog.d('DragEventHandlerViewModel - 拖拽功能已禁用，忽略拖拽进入事件');
      return;
    }

    _dragPosition = position;
    _isDragActive = true;
    PGLog.d('拖拽进入: position = $_dragPosition');

    // 处理拖拽禁止图标显示逻辑
    _updateDragForbiddenOverlay(position, canProcessFiles);
    notifyListeners();
  }

  /// 处理拖拽更新事件
  void onDragUpdated(Offset position, bool Function()? canProcessFiles) {
    if (!_isEnabled) {
      return;
    }

    _dragPosition = position;

    // 处理拖拽禁止图标显示逻辑
    _updateDragForbiddenOverlay(position, canProcessFiles);
    notifyListeners();
  }

  /// 处理拖拽离开事件
  void onDragExited() {
    if (!_isEnabled) {
      return;
    }

    _isDragActive = false;
    PGLog.d('拖拽离开');

    // 隐藏拖拽禁止图标
    _hideDragForbiddenOverlay();
    notifyListeners();
  }

  /// 处理拖拽完成事件(默认支持多项目拖拽,编辑页面不支持)
  Future<List<DealImageFilesResult>> onDragDone(
      BuildContext context, List<XFile> files,
      {bool isSupportMultiProject = true}) async {
    if (!_isEnabled) {
      PGLog.d('DragEventHandlerViewModel - 拖拽功能已禁用，忽略拖拽完成事件');
      return [];
    }

    _isDragActive = false;
    PGLog.d('拖拽完成');

    // 隐藏拖拽禁止图标
    _hideDragForbiddenOverlay();
    notifyListeners();

    // 使用 _dragDropService 处理文件，返回 DealImageFilesResult
    try {
      // 编辑里不支持多项目拖拽模式
      if (isSupportMultiProject) {
        // 读取项目创建模式设置（release-1.8.1放开）
        final settingRepository = context.read<SettingRepository>();
        final projectCreationMode =
            await settingRepository.getProjectCategoryConfig();

        List<DealImageFilesResult> result;
        switch (projectCreationMode) {
          case ProjectCreationMode.single:
            // 单项目模式：所有文件放入一个项目
            final singleResult = await _dragDropService
                .processDroppedFilesForSingleProject(files);
            result = singleResult != null ? [singleResult] : [];
            break;
          case ProjectCreationMode.multiple:
            // 多项目模式：按规则分别创建项目
            result = await _dragDropService
                .processDroppedFilesForMultiProject(files);
            break;
        }

        PGLog.d('拖拽处理完成，创建了 ${result.length} 个项目');
        return result;
      } else {
        final singleResult =
            await _dragDropService.processDroppedFilesForSingleProject(files);
        return singleResult != null ? [singleResult] : [];
      }
    } catch (e) {
      PGLog.e('处理拖拽文件失败: $e');
      return [];
    }
  }

  void setIsProcessFilesTag({required bool isProcessFiles}) {
    _isProcessFiles = isProcessFiles;
  }

  /// 隐藏拖拽禁止图标
  void _hideDragForbiddenOverlay() {
    if (_isDragOverlayVisible) {
      _isDragOverlayVisible = false;
      DragForbiddenCursorOverlay.hide();
    }
  }

  /// 更新拖拽禁止图标显示状态
  void _updateDragForbiddenOverlay(
      Offset position, bool Function()? canProcessFiles) {
    // 处理拖拽完成事件时，不处理拖拽禁止图标显示逻辑，以免在loading，就会闪现一下禁用图标
    if (canProcessFiles != null && !_isProcessFiles) {
      _shouldShowOverlay = !canProcessFiles();

      if (_shouldShowOverlay) {
        if (!_isDragOverlayVisible) {
          _isDragOverlayVisible = true;
          DragForbiddenCursorOverlay.show();
        }
        DragForbiddenCursorOverlay.updatePosition(position);
      } else if (_isDragOverlayVisible) {
        _hideDragForbiddenOverlay();
      }
    }
  }

  @override
  void dispose() {
    _hideDragForbiddenOverlay();
    super.dispose();
  }
}
