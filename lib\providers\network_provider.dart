import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:turing_art/utils/pg_log.dart';

class NetworkProvider extends ChangeNotifier {
  final Connectivity _connectivity = Connectivity();
  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  StreamSubscription<List<ConnectivityResult>>? _subscription;

  NetworkProvider() {
    _initConnectivity();
    _setupSubscription();
  }

  ConnectivityResult get connectionStatus => _connectionStatus;

  // 检查是否有网络连接
  Future<bool> isConnected() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
      return result.isNotEmpty && result.first != ConnectivityResult.none;
    } catch (e) {
      PGLog.d('Failed to check connectivity: $e');
      return false;
    }
  }

  // 获取网络状态描述
  String get networkTypeString {
    switch (_connectionStatus) {
      // wifi
      case ConnectivityResult.wifi:
        return 'WIFI';
      // 移动网络
      case ConnectivityResult.mobile:
        return 'MOBILE';
      // 有线网络
      case ConnectivityResult.ethernet:
        return 'ETHERNET';
      // vpn
      case ConnectivityResult.vpn:
        return 'VPN';
      // 蓝牙
      case ConnectivityResult.bluetooth:
        return 'BLUETOOTH';
      // 无网络
      case ConnectivityResult.none:
        return 'NONE';
      // 未知
      default:
        return 'UNKNOWN';
    }
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      PGLog.d('Failed to get connectivity: $e');
    }
  }

  void _setupSubscription() {
    _subscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  void _updateConnectionStatus(List<ConnectivityResult> result) {
    if (_connectionStatus != result.firstOrNull) {
      _connectionStatus = result.firstOrNull ?? ConnectivityResult.none;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
