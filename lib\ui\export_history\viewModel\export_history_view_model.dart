import 'package:flutter/material.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/domain/models/aigc_export_history/aigc_export_history_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_history_model.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_history_repository.dart';
import 'package:turing_art/ui/common/date_range/date_range_util.dart';

enum ExportHistoryType {
  export, // 精修
  sample, // 小样
  aigc, // AI场景增强
}

class ExportHistoryTypeInfo {
  String _title = "";
  ExportHistoryType _exportHistoryType = ExportHistoryType.export;

  String get title => _title;
  ExportHistoryType get exportHistoryType => _exportHistoryType;

  ExportHistoryTypeInfo({
    required String title,
    required ExportHistoryType exportHistoryType,
  }) {
    _title = title;
    _exportHistoryType = exportHistoryType;
  }
}

class ExportHistoryViewModel extends ChangeNotifier {
  final ExportHistoryRepository _repository;
  final AigcEntranceManager _aigcEntranceManager;
  final CurrentUserRepository _currentUserRepository;
  final int pageSize = 20;
  int _page = 1;
  int get page => _page;
  set page(int value) {
    if (value == _page) {
      return;
    }
    _page = value;
    executeFetchExportHistory();
  }

  String? _startedAt;
  String? _endedAt;

  set startedAt(String? value) {
    final timestamp = DateRangeUtil.dateStringToTimestamp(value);
    if (timestamp == null || timestamp.toString() == _startedAt) {
      return;
    }
    _startedAt = timestamp.toString();
  }

  set endedAt(String? value) {
    final timestamp =
        DateRangeUtil.dateStringToTimestamp(value, isEndOfDay: true);
    if (timestamp == null || timestamp.toString() == _endedAt) {
      return;
    }
    _endedAt = timestamp.toString();
  }

  List<ExportHistoryTypeInfo> _exportHistoryTypeInfos = [];

  List<ExportHistoryTypeInfo> get exportHistoryTypeInfos =>
      _exportHistoryTypeInfos;

  ExportHistoryType _exportHistoryType = ExportHistoryType.export;
  ExportHistoryType get exportHistoryType => _exportHistoryType;
  set exportHistoryType(ExportHistoryType value) {
    if (value == _exportHistoryType) {
      return;
    }
    resetFilter();
    _exportHistoryType = value;

    executeFetchExportHistory();
    notifyListeners();
  }

  Future<void> executeFetchExportHistory() async {
    if (exportHistoryType == ExportHistoryType.aigc) {
      await _fetchAIGCExportHistory(page, _startedAt, _endedAt);
    } else {
      await _fetchExportHistory(
          page,
          _startedAt != null ? int.parse(_startedAt!) : null,
          _endedAt != null ? int.parse(_endedAt!) : null,
          null,
          exportHistoryType.index + 1);
    }
  }

  ExportHistoryViewModel(
    this._repository,
    this._aigcEntranceManager,
    this._currentUserRepository, {
    ExportHistoryType initialExportType = ExportHistoryType.export,
  }) {
    // 设置初始导出类型
    _exportHistoryType = initialExportType;
    // 初始化时自动获取数据
    init();
  }

  // 导出历史数据
  ExportHistoryModel? _exportHistoryModel;
  ExportHistoryModel? get exportHistoryModel => _exportHistoryModel;

  AIGCExportHistoryModel? _aigcExportHistoryModel;
  AIGCExportHistoryModel? get aigcExportHistoryModel => _aigcExportHistoryModel;

  // 是否正在加载
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 初始化：获取第一页数据
  Future<void> init() async {
    await _initializeExportHistoryTypes();
    await executeFetchExportHistory();
  }

  // 初始化导出历史类型列表
  Future<void> _initializeExportHistoryTypes() async {
    _exportHistoryTypeInfos = [
      ExportHistoryTypeInfo(
          title: "精修", exportHistoryType: ExportHistoryType.export),
    ];

    // 检查用户是否有小样导出能力，如果有则添加小样选项
    if (_currentUserRepository.hasCapability(UserCapability.exportSample)) {
      _exportHistoryTypeInfos.add(
        ExportHistoryTypeInfo(
            title: "小样", exportHistoryType: ExportHistoryType.sample),
      );
    }

    // 检查是否为AIGC白名单用户，如果是则添加AI场景增强选项
    final isAigcUser = await _aigcEntranceManager.isAigcUser();
    if (isAigcUser) {
      _exportHistoryTypeInfos.add(
        ExportHistoryTypeInfo(
            title: "AI场景增强", exportHistoryType: ExportHistoryType.aigc),
      );
    }

    notifyListeners();
  }

  void resetFilter() {
    _page = 1;
    _startedAt = null;
    _endedAt = null;
    _exportHistoryType = ExportHistoryType.export;
    notifyListeners();
  }

  // 只重置日期和页码，保持当前导出类型
  void resetPageAndDate() {
    _page = 1;
    _startedAt = null;
    _endedAt = null;
    executeFetchExportHistory();
  }

  // 获取导出历史数据
  Future<void> _fetchExportHistory(int page, int? createdAfter,
      int? createdBefore, String? employeeIds, int? type) async {
    _isLoading = true;
    notifyListeners();

    final result = await _repository.fetchExportHistory(
      page: page,
      pageSize: pageSize,
      createdAfter: createdAfter,
      createdBefore: createdBefore,
      employeeIds: employeeIds,
      type: type,
    );

    _exportHistoryModel = result;

    _isLoading = false;
    notifyListeners();
  }

  Future<void> _fetchAIGCExportHistory(
      int page, String? startTime, String? endTime) async {
    _isLoading = true;
    notifyListeners();

    final result = await _repository.fetchAIGCExportHistory(
      page: page,
      pageSize: pageSize,
      startTime: startTime,
      endTime: endTime,
    );

    _aigcExportHistoryModel = result;
    _isLoading = false;
    notifyListeners();
  }
}
