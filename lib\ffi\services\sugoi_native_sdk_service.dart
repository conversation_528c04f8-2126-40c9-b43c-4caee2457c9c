// SugoiNativeSDK服务层封装
// 提供更友好的Flutter接口

import 'package:flutter/foundation.dart';
import 'package:turing_art/ffi/native/sugoi_native_sdk_bindings.dart';

import '../../../utils/pg_log.dart';

/// SugoiNativeSDK服务类
/// 提供高级API来使用SugoiNativeSDK功能
class SugoiNativeSDKService {
  static bool _isInitialized = false;

  /// 初始化SugoiNativeSDK服务
  /// 返回是否初始化成功
  static Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SugoiNativeSDK服务已经初始化');
      return true;
    }

    try {
      // 初始化FFI绑定
      final success = SugoiNativeSDKBindings.initialize();
      if (success) {
        _isInitialized = true;
        PGLog.d('SugoiNativeSDK服务初始化成功');

        // 可选：立即测试SDK_Env_Platform函数
        final platformValue = await getPlatformInfo();
        PGLog.d('SDK平台信息: $platformValue');

        return true;
      } else {
        PGLog.d('SugoiNativeSDK服务初始化失败');
        return false;
      }
    } catch (e) {
      PGLog.d('SugoiNativeSDK服务初始化异常: $e');
      return false;
    }
  }

  /// 获取平台信息
  /// 调用SDK_Env_Platform函数并返回结果
  static Future<int?> getPlatformInfo() async {
    if (!_isInitialized) {
      PGLog.d('SugoiNativeSDK服务未初始化，尝试自动初始化...');
      final initSuccess = await initialize();
      if (!initSuccess) {
        return null;
      }
    }

    try {
      return SugoiNativeSDKBindings.getSdkEnvPlatform();
    } catch (e) {
      PGLog.d('获取平台信息失败: $e');
      return null;
    }
  }

  /// 检查服务是否可用
  static bool get isAvailable =>
      _isInitialized && SugoiNativeSDKBindings.isInitialized;

  /// 清理服务资源
  static void dispose() {
    SugoiNativeSDKBindings.dispose();
    _isInitialized = false;
    PGLog.d('SugoiNativeSDK服务已清理');
  }
}
