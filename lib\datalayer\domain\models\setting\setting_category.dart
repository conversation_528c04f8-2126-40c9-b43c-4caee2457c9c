import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';

part 'setting_category.freezed.dart';
part 'setting_category.g.dart';

@freezed
class SettingCategory with _$SettingCategory {
  const factory SettingCategory({
    required String title,
    required String key,
    @Default([]) List<SettingItemModel> items,
    @Default(true) bool isShowForUser,
  }) = _SettingCategory;

  factory SettingCategory.fromJson(Map<String, dynamic> json) =>
      _$SettingCategoryFromJson(json);
}
