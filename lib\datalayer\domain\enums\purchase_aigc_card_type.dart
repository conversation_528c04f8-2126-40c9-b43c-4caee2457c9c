/// 购买AIGC卡类型
enum PurchaseAigcCardType {
  /// 灰色lv1
  basic('basic', 0),

  /// 蓝色lv2
  pro('pro', 1),

  /// 渐变lv3
  enterprise('enterprise', 2);

  final String value;
  final int level;
  const PurchaseAigcCardType(this.value, this.level);

  static PurchaseAigcCardType fromString(String value) {
    return PurchaseAigcCardType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PurchaseAigcCardType.basic, // 默认返回精修次数
    );
  }
}
