import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';
import 'package:turing_art/ui/setting/widgets/setting_dropdown_widget.dart';

class ResolutionSettingWidget extends StatefulWidget {
  const ResolutionSettingWidget({super.key});

  @override
  State<ResolutionSettingWidget> createState() =>
      _ResolutionSettingWidgetState();
}

class _ResolutionSettingWidgetState extends State<ResolutionSettingWidget> {
  SettingItemModel? _settingItem;

  SettingItemModel? _getResolutionSettingItem(SettingViewModel viewModel) {
    if (_settingItem != null) {
      return _settingItem;
    }

    final items = viewModel.getItemsByCategory(SettingCategoryConstant.preview);
    if (items.isNotEmpty) {
      _settingItem = items.first; // 获取第一个设置项（预览图像尺寸设置）
    }
    return _settingItem;
  }

  void _onSelectionChanged(int index, SettingViewModel viewModel) {
    if (_settingItem == null ||
        index < 0 ||
        index >= _settingItem!.choices.length) {
      return;
    }

    final selectedValue = _settingItem!.choices[index].value;
    viewModel.updateChoiceSelection(
      SettingCategoryConstant.preview,
      _settingItem!.key,
      selectedValue,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 大图预览图像尺寸设置
        Text(
          "大图预览图像尺寸设置（精修）",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 14,
            color: Colors.white.withOpacity(0.7),
            height: 18 / 14,
          ),
        ),

        const SizedBox(height: 16),

        // 预览图像尺寸标签和下拉列表
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 左侧：预览图像尺寸标签
            Text(
              "预览图像尺寸",
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                fontSize: 14,
                color: Colors.white.withOpacity(0.7),
                height: 16 / 14,
              ),
            ),

            const SizedBox(width: 14),

            // 右侧：下拉列表
            Consumer<SettingViewModel>(
              builder: (context, viewModel, child) {
                final settingItem = _getResolutionSettingItem(viewModel);
                final selectedIndex =
                    viewModel.getSelectedIndexByItem(settingItem!);
                return SettingDropdownWidget(
                  choices: settingItem.choices,
                  selectedIndex: selectedIndex,
                  onSelectionChanged: (index) =>
                      _onSelectionChanged(index, viewModel),
                  width: 302,
                  height: 32,
                );
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 提示信息
        Text(
          "预览图像尺寸设置不影响导出画质，导出是按照原图尺寸导出\n预览分辨率设置大于默认值可能会导致您的设备卡顿",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
            height: 20 / 12,
          ),
        ),
      ],
    );
  }
}
