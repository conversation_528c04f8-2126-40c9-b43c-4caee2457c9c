import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/feedback/feedback_client_info.dart';

part 'feedback_content.freezed.dart';
part 'feedback_content.g.dart';

@freezed
class FeedbackContent with _$FeedbackContent {
  const factory FeedbackContent({
    required String category,
    required String content,
    List<String>? attachments,
    FeedbackClientInfo? clientInfo,
  }) = _FeedbackContent;

  factory FeedbackContent.fromJson(Map<String, dynamic> json) =>
      _$FeedbackContentFromJson(json);
}
