import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/update_event.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_chain.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_factory.dart';
import 'package:turing_art/ui/common/project_grid_view/project_grid_item.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/ui/ui_status/select_project_error_type.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class AigcProjectViewModel extends ChangeNotifier {
  AigcProjectViewModel(
    this._navigator,
    this._projectRepository,
    this._projectUseCase,
    this._unityUseCase,
    this._workspaceUseCase,
    this._projectStateProvider,
    this._unityController,
    this._currentUserRepository,
    this._mediaRepository,
  ) {
    _init();
    // 初始化责任链工厂
    _projectHandlerChainFactory = ProjectCreateSelectHandlerFactory(
      projectUseCase: _projectUseCase,
      unityUseCase: _unityUseCase,
      workspaceUseCase: _workspaceUseCase,
      projectStateProvider: _projectStateProvider,
      unityController: _unityController,
      currentUserRepository: _currentUserRepository,
      mediaRepository: _mediaRepository,
      projectRepository: _projectRepository,
    );
  }

  final NavigatorService _navigator;
  final ProjectRepository _projectRepository;
  final ProjectUseCaseProvider _projectUseCase;
  final UnityUseCaseProvider _unityUseCase;
  final WorkspaceUseCaseProvider _workspaceUseCase;
  final ProjectStateProvider _projectStateProvider;
  final UnityController _unityController;
  final CurrentUserRepository _currentUserRepository;
  final MediaRepository _mediaRepository;
  // 图片选择服务
  final ImageSelectionService _imageSelectionService =
      ImageSelectionService.forPlatform();
  // 添加项目处理链工厂
  late final ProjectCreateSelectHandlerFactory _projectHandlerChainFactory;
  bool _isProcessing = false;
  bool get isProcessing => _isProcessing;

  // 批量创建标识
  bool _isBatchCreating = false;
  bool get isBatchCreating => _isBatchCreating;

  // 批量创建相关状态
  int _batchCreateProgress = 0;
  int get batchCreateProgress => _batchCreateProgress;

  int _batchCreateTotal = 0;
  int get batchCreateTotal => _batchCreateTotal;

  final List<String> _batchCreatedProjectIds = <String>[];
  List<String> get batchCreatedProjectIds =>
      List.unmodifiable(_batchCreatedProjectIds);

  final List<String> _batchCreateErrors = <String>[];
  List<String> get batchCreateErrorMessages =>
      List.unmodifiable(_batchCreateErrors);

  // 当前用户的所有项目
  List<ProjectInfo> _projects = <ProjectInfo>[];
  List<ProjectInfo> get projects => _projects;

  // 转换为UI模型的项目列表
  List<ProjectUIModel> get projectUIModels =>
      _projects.map((project) => _convertToUIModel(project)).toList();

  // 当前用户是否有项目
  bool get hasProjects => _projects.isNotEmpty;

  // 数据仓库更新的通知
  StreamSubscription<UpdateEvent<String>>? _dataUpdateSubscription;

  // 项目状态变化的通知
  bool _isProjectStateListenerAdded = false;

  // 初始化
  Future<void> _init() async {
    try {
      await loadProjects();
      _initSubscription();
      _initProjectStateSubscription();
    } on Exception catch (e) {
      PGLog.e('AIGC项目页面初始化失败: $e');
    }
  }

  /// 初始化信号更新
  void _initSubscription() {
    try {
      // 订阅更新
      _dataUpdateSubscription =
          _projectRepository.dataUpdates.listen((UpdateEvent<String> event) {
        PGLog.d('AIGC项目${event.updated}发生了${event.type}操作');
        loadProjects();
      });
    } catch (e) {
      PGLog.e('AIGC项目页面订阅失败: $e');
    }
  }

  /// 初始化项目状态监听
  void _initProjectStateSubscription() {
    try {
      // 监听项目状态变化
      if (!_isProjectStateListenerAdded) {
        _projectStateProvider.addListener(_handleProjectStateChange);
        _isProjectStateListenerAdded = true;
      }
    } catch (e) {
      PGLog.e('AIGC项目页面项目状态监听失败: $e');
    }
  }

  /// 处理项目状态变化
  void _handleProjectStateChange() {
    final changeType = _projectStateProvider.lastChangeType;
    final previousProjectId = _projectStateProvider.previousProjectId;

    // 当检测到退出编辑状态时，更新工程编辑时间
    if (changeType == ProjectStateChangeType.exitingEdit &&
        previousProjectId != null) {
      _syncProject(previousProjectId);
    }
  }

  /// 更新工程
  Future<void> _syncProject(String projectId) async {
    try {
      // 同步项目信息
      await _projectRepository.syncProject(projectId);
      // 刷新项目列表(syncProject会触发数据变更，此类已经监听自动刷新，不需要手动刷新)
      // loadProjects();
    } catch (e) {
      PGLog.e('更新工程编辑时间失败: $e');
    }
  }

  /// 更新工程编辑时间
  Future<void> _updateProjectEditTime(String projectId) async {
    try {
      final project = await _projectRepository.getProjectById(projectId);
      if (project != null) {
        final updatedProject = project.copyWith(updateDate: DateTime.now());
        await _projectRepository.updateProject(updatedProject);
        PGLog.d('AIGC项目列表检测到编辑场景退出，已更新工程编辑时间: $projectId');
      }
    } catch (e) {
      PGLog.e('更新工程编辑时间失败: $e');
    }
  }

  /// 加载项目列表
  Future<void> loadProjects() async {
    try {
      // 获取所有项目
      _projects = await _projectUseCase.loadAigcProjects.invoke();
      notifyListeners();
      PGLog.d('AIGC项目页面加载项目成功: ${_projects.length} 个项目');
    } catch (e) {
      PGLog.e('AIGC项目页面加载项目失败: $e');
      _projects = [];
      notifyListeners();
    }
  }

  /// 创建项目
  Future<ProcessFilesUiStatus> createProject(List<File> files,
      {String? projectName}) async {
    if (_isProcessing) {
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.processing,
      );
    }

    try {
      _setProcessing(true);
      final projectId = const Uuid().v4();
      // 创建项目处理上下文
      final handlerContext = ProjectCreateSelectHandlerContext(
        files: files,
        projectName: projectName,
        projectId: projectId,
        forBatch: false, // 设置批处理模式标志
      );

      // 使用工厂创建责任链 - 创建项目和存储资源文件路径
      final createProjectChain =
          _projectHandlerChainFactory.createCreateAigcProjectChain();

      // 执行责任链
      final createResult = await createProjectChain.process(handlerContext);
      if (createResult is ErrorStatus) {
        _setProcessing(false);
        return createResult;
      }

      selectProject(projectId);
      loadProjects();
      return const ProcessFilesUiStatus.success();
    } catch (e) {
      PGLog.e('AIGC创建项目失败: $e');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    } finally {
      _setProcessing(false);
    }
  }

  /// 批量创建AIGC项目 - 不跳转编辑页面
  /// [fileGroups] 多个文件列表，每个列表对应一个项目
  /// [projectNames] 可选的项目名称列表，如果为空则使用默认命名
  /// 返回批量创建结果状态
  Future<ProcessFilesUiStatus> batchCreateProjects(
    List<List<File>> fileGroups, {
    List<String>? projectNames,
  }) async {
    PGLog.d('AigcProjectViewModel - batchCreateProjects - 开始批量创建AIGC项目');

    if (_isProcessing || _isBatchCreating) {
      PGLog.w('AigcProjectViewModel - 当前正在处理中，跳过批量创建');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.processing,
      );
    }

    if (fileGroups.isEmpty) {
      PGLog.w('AigcProjectViewModel - 文件组为空，跳过批量创建');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    }

    try {
      _setProcessing(true);
      _setBatchCreating(true);
      _batchCreatedProjectIds.clear();
      _batchCreateErrors.clear();
      _batchCreateTotal = fileGroups.length;
      _batchCreateProgress = 0;
      notifyListeners();

      // 逐个创建项目
      for (int i = 0; i < fileGroups.length; i++) {
        final files = fileGroups[i];
        final projectName = projectNames != null && projectNames.length > i
            ? projectNames[i]
            : null;

        PGLog.d(
            'AigcProjectViewModel - 开始创建第${i + 1}个AIGC项目，文件数量: ${files.length}');

        final result = await _createSingleProjectWithStatus(files, projectName);
        if (result is SuccessStatus) {
          PGLog.d('AigcProjectViewModel - 第${i + 1}个AIGC项目创建成功');
        } else if (result is ErrorStatus) {
          final errorMsg = '第${i + 1}个AIGC项目创建失败: ${result.message}';
          _batchCreateErrors.add(errorMsg);
          PGLog.e('AigcProjectViewModel - $errorMsg');
        }

        _batchCreateProgress = i + 1;
        // notifyListeners();
      }

      PGLog.d(
          'AigcProjectViewModel - 批量创建完成，成功: ${_batchCreatedProjectIds.length}, 失败: ${_batchCreateErrors.length}');

      // 刷新项目列表
      await loadProjects();

      return const ProcessFilesUiStatus.success();
    } on Exception catch (e) {
      PGLog.e('AigcProjectViewModel - 批量创建AIGC项目时发生异常: $e');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    } finally {
      _setProcessing(false);
      _setBatchCreating(false);
    }
  }

  /// 创建单个AIGC项目 - 不跳转编辑页面
  /// 返回创建状态
  Future<ProcessFilesUiStatus> _createSingleProjectWithStatus(
      List<File> files, String? projectName) async {
    if (files.isEmpty) {
      PGLog.w('AigcProjectViewModel - 文件列表为空，跳过创建项目');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    }

    try {
      final projectId = const Uuid().v4();
      // 创建项目处理上下文
      final handlerContext = ProjectCreateSelectHandlerContext(
        files: files,
        projectName: projectName,
        projectId: projectId,
        forBatch: false, // 设置批处理模式标志
      );

      // 使用工厂创建责任链 - 创建项目和存储资源文件路径
      final createProjectChain =
          _projectHandlerChainFactory.createCreateAigcProjectChain();

      // 执行责任链
      final createResult = await createProjectChain.process(handlerContext);
      if (createResult is ErrorStatus) {
        PGLog.e('AigcProjectViewModel - 创建AIGC项目失败: ${createResult.message}');
        return createResult;
      }

      // 记录成功创建的项目ID
      _batchCreatedProjectIds.add(projectId);
      PGLog.d('AigcProjectViewModel - AIGC项目创建成功: $projectId');
      return const ProcessFilesUiStatus.success();
    } on Exception catch (e) {
      PGLog.e('AigcProjectViewModel - 创建单个AIGC项目时发生异常: $e');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    }
  }

  /// 删除项目
  Future<void> deleteProject(String projectId) async {
    try {
      PGLog.d('AIGC删除项目: $projectId');
      // 调用项目用例删除项目
      final success = await _projectUseCase.deleteProject.invoke([projectId]);
      if (success) {
        PGLog.d('AIGC删除项目成功: $projectId');
        // 刷新项目列表
        await loadProjects();
      } else {
        PGLog.e('AIGC删除项目失败: $projectId');
        throw Exception('删除项目失败');
      }
    } catch (e) {
      PGLog.e('AIGC删除项目失败: $e');
      rethrow;
    }
  }

  /// 重命名项目
  Future<void> renameProject(String projectId, String newName) async {
    try {
      PGLog.d('AIGC重命名项目: $projectId -> $newName');
      // 调用项目仓库重命名项目
      await _projectRepository.renameProject(projectId, newName);
      PGLog.d('AIGC重命名项目成功: $projectId -> $newName');
      // 刷新项目列表
      await loadProjects();
    } catch (e) {
      PGLog.e('AIGC重命名项目失败: $e');
      rethrow;
    }
  }

  Future<SelectProjectErrorType?> trySelectedProject(int idx) async {
    if (idx < 0 || idx >= _projects.length) {
      PGLog.e('无效的项目索引: $idx');
      return SelectProjectErrorType.indexError;
    }

    final result = await _workspaceUseCase.openWorkspaceDiskSpaceGuard.invoke();
    if (!result) {
      return SelectProjectErrorType.diskSpace;
    }
    return null;
  }

  void selectProject(String projectId) {
    _projectStateProvider.editProjectId(projectId, isUnityEngine: false);
    _navigator.navigateToAigcEditing(projectId);
    // 更新最后编辑时间
    _updateProjectEditTime(projectId);
  }

  void _setProcessing(bool processing) {
    _isProcessing = processing;
    notifyListeners();
  }

  /// 设置批量创建状态
  void _setBatchCreating(bool value) {
    _isBatchCreating = value;
    if (!value) {
      // 重置进度
      _batchCreateProgress = 0;
      _batchCreateTotal = 0;
    }
  }

  /// 获取批量创建进度百分比
  double get batchCreateProgressPercent {
    if (_batchCreateTotal == 0) {
      return 0.0;
    }
    return _batchCreateProgress / _batchCreateTotal;
  }

  /// 检查是否正在批量创建
  bool get isCreatingProjects => _isBatchCreating;

  /// 获取批量创建状态描述
  String get batchCreateStatusText {
    if (!_isBatchCreating) {
      return '';
    }
    return '正在创建AIGC项目 $_batchCreateProgress/$_batchCreateTotal';
  }

  /// 清除批量创建结果
  void clearBatchCreateResults() {
    _batchCreatedProjectIds.clear();
    _batchCreateErrors.clear();
    notifyListeners();
  }

  /// 获取批量创建成功数量
  int get batchCreateSuccessCount => _batchCreatedProjectIds.length;

  /// 获取批量创建失败数量
  int get batchCreateFailureCount => _batchCreateErrors.length;

  /// 处理选择文件
  Future<DealImageFilesResult?> pickImagesFromFiles() async {
    return _imageSelectionService.pickImagesFromFiles();
  }

  /// 将ProjectInfo转换为ProjectUIModel
  ProjectUIModel _convertToUIModel(ProjectInfo project) {
    final DateFormat dateFormat = DateFormat('yyyy.MM.dd HH:mm');
    final time = project.updateDate != null
        ? dateFormat.format(project.updateDate!)
        : '';

    return ProjectUIModel(
      coverImages: project.coverImages.take(3).toList(),
      totalPhotos: project.fileCount,
      title: project.name,
      time: time,
    );
  }

  @override
  void dispose() {
    _dataUpdateSubscription?.cancel();
    if (_isProjectStateListenerAdded) {
      _projectStateProvider.removeListener(_handleProjectStateChange);
    }
    super.dispose();
  }
}
