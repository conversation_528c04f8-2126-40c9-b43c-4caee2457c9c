import 'package:cross_file/cross_file.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';

/// 拖放接口
abstract class DragDropInterface {
  /// 处理拖放文件为单个项目
  Future<DealImageFilesResult?> processDroppedFilesForSingleProject(
      List<XFile> files);

  /// 处理拖放文件为多个项目
  Future<List<DealImageFilesResult>> processDroppedFilesForMultiProject(
      List<XFile> files);
}
