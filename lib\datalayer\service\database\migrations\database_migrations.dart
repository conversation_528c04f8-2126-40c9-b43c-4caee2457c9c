import 'package:drift/drift.dart';
import 'package:turing_art/datalayer/service/database/schema_versions.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 数据库迁移助手类
class DatabaseMigrations {
  /// 从数据库版本1迁移到版本2
  static Future<void> migrateFrom1To2(Migrator m, Schema2 schema) async {
    try {
      await Future.wait([
        // 1.1.0 调整用户表主键，从uid改为employeeId
        m.drop(schema.userEntity),

        m.createTable(schema.userEntity),
        // 1.1.0 新增creatorInfo表
        m.createTable(schema.creatorInfoEntity),
        // 尝试创建表，如果表已存在会抛出异常
        m.createTable(schema.exportTokenEntity),
      ]);

      PGLog.d('数据库迁移成功');
    } catch (e) {
      PGLog.e('数据库迁移失败: ${e.toString()}');
    }
  }

  /// 从数据库版本2迁移到版本3
  static Future<void> migrateFrom2To3(<PERSON>gra<PERSON> m, Schema3 schema) async {
    try {
      await Future.wait([
        // 为 ProjectEntity 添加新字段
        m.addColumn(
            schema.projectEntity, schema.projectEntity.workspaceVersion),

        // 为 WorkspaceEntity 添加新字段
        m.addColumn(schema.workspaceEntity, schema.workspaceEntity.filterValue),
        m.addColumn(schema.workspaceEntity, schema.workspaceEntity.sortValue),

        // 为 WorkspaceFileEntity 添加新字段
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.fileName),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.iconized),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.midIconized),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.captureTime),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.isOverSize),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.faceCount),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.binFormat),
        m.addColumn(schema.workspaceFileEntity,
            schema.workspaceFileEntity.rawAutoExpose),
        m.addColumn(schema.workspaceFileEntity,
            schema.workspaceFileEntity.rawAutoAdjustType),
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.isDeleted),
      ]);

      PGLog.d('数据库迁移成功');
    } catch (e) {
      PGLog.e('数据库迁移失败: ${e.toString()}');
    }
  }

  /// 从数据库版本3迁移到版本4
  static Future<void> migrateFrom3To4(Migrator m, Schema4 schema) async {
    try {
      await Future.wait([
        // 新增工程类型标识
        m.addColumn(schema.projectEntity, schema.projectEntity.projectType),
      ]);

      PGLog.d('数据库迁移成功');
    } catch (e) {
      PGLog.e('数据库迁移失败: ${e.toString()}');
    }
  }

  /// 从数据库版本4迁移到版本5
  static Future<void> migrateFrom4To5(Migrator m, Schema5 schema) async {
    try {
      await Future.wait([
        // token 表新增 exportType
        // m.addColumn(
        //     schema.exportTokenEntity, schema.exportTokenEntity.exportType),
        m.drop(schema.exportTokenEntity),

        m.createTable(schema.exportTokenEntity),

        // workspace_file新增 n8相关字段
        m.addColumn(
            schema.workspaceFileEntity, schema.workspaceFileEntity.n8Exported),
        m.addColumn(schema.workspaceFileEntity,
            schema.workspaceFileEntity.n8ExportTime),
        m.addColumn(schema.workspaceFileEntity,
            schema.workspaceFileEntity.fileExportedState),

        // 新增文件操作历史表
        m.createTable(schema.fileOperationHistoryEntity),
      ]);

      PGLog.d('数据库迁移成功');
    } catch (e) {
      PGLog.e('数据库迁移失败: ${e.toString()}');
    }
  }

  /// 从数据库版本5迁移到版本6
  static Future<void> migrateFrom5To6(Migrator m, Schema6 schema) async {
    try {
      await Future.wait([
        // 新增工程删除标识
        m.addColumn(schema.projectEntity, schema.projectEntity.isDelete),

        // 新增导出任务表
        m.createTable(schema.exportTaskEntity),
        m.createTable(schema.exportTaskFileEntity),
      ]);
      PGLog.d('数据库迁移成功');
    } catch (e) {
      PGLog.e('数据库迁移失败: ${e.toString()}');
    }
  }
}
