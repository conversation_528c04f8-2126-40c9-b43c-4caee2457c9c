// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_file_certify.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UploadFileCertifyImpl _$$UploadFileCertifyImplFromJson(
        Map<String, dynamic> json) =>
    _$UploadFileCertifyImpl(
      uploadCertify: json['uploadCertify'] as String,
      type: json['type'] as String,
      domain: json['domain'] as String,
      accelerationDomain: json['accelerationDomain'] as String,
      innerDomain: json['innerDomain'] as String,
      expireAt: json['expireAt'] as String,
      scheme: json['scheme'] as String,
    );

Map<String, dynamic> _$$UploadFileCertifyImplToJson(
        _$UploadFileCertifyImpl instance) =>
    <String, dynamic>{
      'uploadCertify': instance.uploadCertify,
      'type': instance.type,
      'domain': instance.domain,
      'accelerationDomain': instance.accelerationDomain,
      'innerDomain': instance.innerDomain,
      'expireAt': instance.expireAt,
      'scheme': instance.scheme,
    };
