import 'package:turing_art/core/service/disk_cache_manager/cache_config/cache_config.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class SaveSettingUseCase {
  final SettingRepository _settingRepository;
  final ProjectRepository _projectRepository;
  final String? Function() getCurrentProjectId;
  final UnityUseCaseProvider _unityUseCaseProvider;
  final UnityController _unityController;
  final DiskCacheManager _diskCacheManager;

  SaveSettingUseCase({
    required this.getCurrentProjectId,
    required SettingRepository settingRepository,
    required ProjectRepository projectRepository,
    required UnityUseCaseProvider unityUseCaseProvider,
    required UnityController unityController,
    required DiskCacheManager diskCacheManager,
  })  : _settingRepository = settingRepository,
        _projectRepository = projectRepository,
        _unityUseCaseProvider = unityUseCaseProvider,
        _unityController = unityController,
        _diskCacheManager = diskCacheManager;

  // 保存设置
  Future<void> invoke(SettingConfig config) async {
    try {
      await _settingRepository.saveSettingConfig(config);
      // 刷新所有项目配置
      _refreshAllProjectsExportSetting();
      // 刷新当前编辑工程的项目配置
      _refreshCurrentProjectExportSetting();
      // 刷新缓存设置
      _refreshCacheSetting();

      // 刷新当前用户鼠标配置
      _refreshCurrentUserMouseSetting();
    } catch (e) {
      PGLog.e('Error saving settings: $e');
      rethrow;
    }
  }

  // 刷新所有工程的导出设置
  Future<void> _refreshAllProjectsExportSetting() async {
    final allProjects = await _projectRepository.getAllProjects();
    allProjects.map((project) async {
      final newExportSettingConfig =
          await _settingRepository.getExportConfig(project.exportConfig);
      _projectRepository.updateProject(
          project.copyWith(exportConfig: newExportSettingConfig));
    });
  }

  //刷新当前工程的导出设置
  Future<void> _refreshCurrentProjectExportSetting() async {
    // 如果当前在编辑页面，则发送导出设置信息到Unity
    final currentProjectId = getCurrentProjectId();
    if (currentProjectId == null) {
      return;
    }
    final message =
        await _unityUseCaseProvider.setupExportConfig.invoke(currentProjectId);
    if (message == null) {
      return;
    }
    await _unityController.sendMessage(message);
  }

  Future<void> _refreshCacheSetting() async {
    final cacheSetting = await _settingRepository.getCacheCleanSetting();
    _diskCacheManager.updateCacheConfig(
      CacheConfig(
        cleanupDays: CacheValidTime.fromDays(cacheSetting.maxDays),
        cleanupThresholdSize: cacheSetting.maxSize,
      ),
      tryCleanupWithConfig: true,
    );
  }

  // 刷新当前用户鼠标配置
  Future<void> _refreshCurrentUserMouseSetting() async {
    final mouseSetting = await _settingRepository.getTouchpadModeEnabled();
    final message = _unityUseCaseProvider
        .createSetupControlConfigHandler()
        .invoke(isMagicControl: mouseSetting);
    await _unityController.sendMessage(message);
  }
}
