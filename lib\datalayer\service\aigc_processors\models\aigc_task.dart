import 'dart:typed_data';

import 'package:turing_art/datalayer/service/task_queue_system/generic_task.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';

// ==================== AIGC任务系统设计说明 ====================
///
/// AIGC任务系统设计：
/// 1. AigcTask：任务调度核心类，存储任务基本信息（taskId, priority, submittedAt等）
/// 2. AigcTaskRequest：任务请求数据抽象基类，存储任务特定的输入参数
/// 3. AigcTaskResult：任务结果数据抽象基类，存储任务特定的输出结果
///
/// 任务类型与对应的Request/Result：
/// - ImageAssets: AigcImageAssetsTaskRequest / AigcImageAssetsTaskResult
/// - Mask: AigcMaskTaskRequest / AigcMaskTaskResult
/// - InteractiveMask: AigcInteractiveMaskTaskRequest / AigcInteractiveMaskTaskResult
/// - Export: AigcExportTaskRequest / AigcExportTaskResult
/// - Proof: AigcProofTaskRequest / AigcProofTaskResult
///
/// 使用示例：
/// ```dart
/// // 创建任务请求
/// final request = AigcImageAssetsTaskRequest(
///   inputPath: '/path/to/input.jpg',
///   fileId: 'file_001',
///   maxWidth: 200,
///   maxHeight: 200,
/// );
///
/// // 创建任务
/// final task = AigcTask(
///   taskId: 'task_001',
///   taskType: AigcTaskType.imageAssets,
///   request: request,
///   submittedAt: DateTime.now(),
/// );
///
/// // 创建任务结果
/// final result = AigcImageAssetsTaskResult(
///   highQualityPath: '/path/to/highquality.jpg',
///   previewPath: '/path/to/preview.jpg',
///   thumbnailPath: '/path/to/thumbnail.jpg',
/// );
/// ```
///
/// 消息构建示例：
/// ```dart
/// // 创建任务消息
/// final taskMessage = AigcTaskMessage(
///   taskId: 'task_001',
///   processorKey: 'image_assets_processor',
///   payload: task,
/// );
///
/// // 从任务消息快速创建进度消息
/// final startMessage = taskMessage.createStartMessage();
/// final progressMessage = taskMessage.createInProgressMessage(
///   currentStep: '正在生成缩略图',
///   progress: 0.5,
/// );
///
/// // 从任务消息快速创建结果消息
/// final successMessage = taskMessage.createTypedSuccessMessage(
///   processingTime: Duration(seconds: 10),
///   previewPath: '/path/to/preview.jpg',
///   thumbnailPath: '/path/to/thumbnail.jpg',
/// );
///
/// final failureMessage = taskMessage.createFailureMessage(
///   errorMessage: '处理失败: 文件格式不支持',
///   processingTime: Duration(seconds: 5),
/// );
/// ```
///
/// 扩展新任务类型的步骤：
/// 1. 在AigcTaskType枚举中添加新类型
/// 2. 在TaskTypeExtensions中添加displayName
/// 3. 创建新的请求类（继承AigcTaskRequest）
/// 4. 创建新的结果类（继承AigcTaskResult）
///
// ==================== 任务类型定义 ====================

enum AigcTaskType {
  imageAssets,
  cover,
  thumbnail,
  mask,
  interactiveMask,
  image,
  rawConversion
}

extension TaskTypeExtensions on AigcTaskType {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case AigcTaskType.imageAssets:
        return '图像资源生成';
      case AigcTaskType.cover:
        return '封面生成';
      case AigcTaskType.thumbnail:
        return '缩略图生成';
      case AigcTaskType.mask:
        return '主体蒙版生成';
      case AigcTaskType.interactiveMask:
        return '交互式蒙版生成';
      case AigcTaskType.image:
        return '图像生成';
      case AigcTaskType.rawConversion:
        return '智能调色';
    }
  }
}

// ==================== 任务请求数据类型 ====================

/// AIGC任务请求数据基类
abstract class AigcTaskRequest {
  final String inputPath;
  final String outputPath;
  final String fileId;

  AigcTaskRequest({
    required this.inputPath,
    required this.outputPath,
    required this.fileId,
  });
}

/// 图像资源任务请求
class AigcImageAssetsTaskRequest extends AigcTaskRequest {
  final int? maxWidth;
  final int? maxHeight;
  final String? format;

  AigcImageAssetsTaskRequest(
      {required super.inputPath,
      required super.outputPath,
      required super.fileId,
      this.maxWidth,
      this.maxHeight,
      this.format});
}

/// 封面任务请求
class AigcCoverTaskRequest extends AigcTaskRequest {
  AigcCoverTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
  });
}

/// 缩略图任务请求
class AigcThumbnailTaskRequest extends AigcTaskRequest {
  AigcThumbnailTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
  });
}

/// 蒙版任务请求
class AigcMaskTaskRequest extends AigcTaskRequest {
  final String? modelName;
  final double? threshold;

  AigcMaskTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
    this.modelName,
    this.threshold,
  });
}

/// 交互式蒙版任务请求
class AigcInteractiveMaskTaskRequest extends AigcTaskRequest {
  /// 蒙版绘制路径图片数据信息对象（包含所有区域抠图相关的数据）
  final AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo;

  /// 模型名称
  final String? modelName;

  /// 阈值
  final double? threshold;

  AigcInteractiveMaskTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
    required this.mattingMaskDataInfo,
    this.modelName,
    this.threshold,
  });

  // 提供便捷的 getter 方法以保持向后兼容性
  /// 上次的蒙版数据
  Uint8List? get previousMaskData => mattingMaskDataInfo.previousMaskBytes;

  /// 上次前景蒙版数据
  Uint8List? get previousForegroundMaskData =>
      mattingMaskDataInfo.foreStrokesBytes;

  /// 上次的背景蒙版数据
  Uint8List? get previousBackgroundMaskData =>
      mattingMaskDataInfo.backStrokesBytes;

  /// 新的涂抹路径数据
  Uint8List? get brushStrokeData => mattingMaskDataInfo.currentStrokesBytes;

  /// 是涂抹还是擦除 (true: 涂抹, false: 擦除)
  bool get isBrushMode => mattingMaskDataInfo.isBrushMode;

  /// 是否为区域抠图模式
  bool get isRegionalMode => mattingMaskDataInfo.isRegionalMode;

  /// 区域框信息
  AigcRegionalFrameDataInfo? get regionalFrameDataInfo =>
      mattingMaskDataInfo.regionalFrameDataInfo;

  /// 显示图数据
  Uint8List? get displayImageBytes => mattingMaskDataInfo.displayImageBytes;

  /// 展示图蒙版数据（完整蒙版）
  Uint8List? get fullMaskBytes => mattingMaskDataInfo.fullMaskBytes;

  /// 区域框内Crop的图片数据
  Uint8List? get regionalImageBytes => mattingMaskDataInfo.regionalImageBytes;
}

/// 图像生成任务请求
class AigcImageTaskRequest extends AigcTaskRequest {
  final int? maxWidth;
  final int? maxHeight;
  final String? format;

  AigcImageTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
    this.maxWidth,
    this.maxHeight,
    this.format,
  });
}

/// 导出任务请求
class AigcExportTaskRequest extends AigcTaskRequest {
  final String? format;
  final int? quality;
  final Map<String, dynamic>? exportOptions;

  AigcExportTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
    this.format,
    this.quality,
    this.exportOptions,
  });
}

/// 智能调色任务请求
class AigcRawConversionTaskRequest extends AigcTaskRequest {
  /// 是否使用自定义调色参数
  final bool adjustCustom;

  /// 调色配置参数（可选，为null时使用默认配置）
  final Map<String, dynamic>? config;

  AigcRawConversionTaskRequest({
    required super.inputPath,
    required super.outputPath,
    required super.fileId,
    this.adjustCustom = true,
    this.config,
  });
}

// ==================== 任务结果数据类型 ====================

/// AIGC任务结果数据基类
abstract class AigcTaskResult {}

/// 图像资源任务结果
class AigcImageAssetsTaskResult extends AigcTaskResult {
  final String? highQualityPath;
  final String? previewPath;
  final String? thumbnailPath;

  AigcImageAssetsTaskResult({
    this.highQualityPath,
    this.previewPath,
    this.thumbnailPath,
  });
}

/// 封面任务结果
class AigcCoverTaskResult extends AigcTaskResult {
  final String? coverPath;

  AigcCoverTaskResult({
    this.coverPath,
  });
}

/// 缩略图任务结果
class AigcThumbnailTaskResult extends AigcTaskResult {
  final String? thumbnailPath;

  AigcThumbnailTaskResult({
    this.thumbnailPath,
  });
}

/// 蒙版任务结果
class AigcMaskTaskResult extends AigcTaskResult {
  final String? outputPath;

  AigcMaskTaskResult({
    this.outputPath,
  });
}

/// 交互式蒙版任务结果
class AigcInteractiveMaskTaskResult extends AigcTaskResult {
  final String? outputPath;

  /// 蒙版绘制路径图片数据信息对象（包含所有区域抠图相关的数据）
  final AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo;

  AigcInteractiveMaskTaskResult({
    this.outputPath,
    required this.mattingMaskDataInfo,
  });
}

/// 图像生成任务结果
class AigcImageTaskResult extends AigcTaskResult {
  final String? outputPath;

  AigcImageTaskResult({
    this.outputPath,
  });
}

/// 导出任务结果
class AigcExportTaskResult extends AigcTaskResult {
  final String? outputPath;

  AigcExportTaskResult({
    this.outputPath,
  });
}

/// 打样任务结果
class AigcProofTaskResult extends AigcTaskResult {
  final String? outputPath;

  AigcProofTaskResult({
    this.outputPath,
  });
}

/// 智能调色任务结果
class AigcRawConversionTaskResult extends AigcTaskResult {
  final String? outputPath;

  AigcRawConversionTaskResult({
    this.outputPath,
  });
}

// ==================== 任务核心类 ====================

class AigcTask extends GenericTask {
  @override
  final String taskId;

  @override
  final DateTime submittedAt;

  @override
  final int priority;

  final AigcTaskType taskType;
  final AigcTaskRequest request;

  AigcTask({
    required this.taskId,
    required this.taskType,
    required this.request,
    required this.submittedAt,
    this.priority = 0, // 默认优先级为0（最高）
  });

  @override
  AigcTask copyWithPriority(int newPriority) {
    return AigcTask(
      taskId: taskId,
      taskType: taskType,
      request: request,
      submittedAt: submittedAt,
      priority: newPriority,
    );
  }

  /// 获取输入路径（从request中）
  String get inputPath => request.inputPath;

  /// 获取输出路径（从request中）
  String get outputPath => request.outputPath;

  /// 获取文件ID（从request中）
  String get fileId => request.fileId;

  @override
  String toString() =>
      'AigcTask(id: $taskId, type: $taskType, priority: $priority, inputPath: $inputPath, outputPath: $outputPath)';
}

// ==================== 具体任务类型的工厂方法 ====================

/// 图像资源任务工厂
class AigcImageAssetsTask {
  static AigcTask create(
      {required String taskId,
      required String inputPath,
      required String outputPath,
      required String fileId,
      required DateTime submittedAt,
      int priority = 0,
      int? maxWidth,
      int? maxHeight,
      String? format}) {
    final request = AigcImageAssetsTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      format: format,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.imageAssets,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcImageAssetsTaskResult createResult({
    String? highQualityPath,
    String? previewPath,
    String? thumbnailPath,
  }) {
    return AigcImageAssetsTaskResult(
      highQualityPath: highQualityPath,
      previewPath: previewPath,
      thumbnailPath: thumbnailPath,
    );
  }
}

/// 封面任务工厂
class AigcCoverTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    int priority = 0,
  }) {
    final request = AigcCoverTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.cover,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcCoverTaskResult createResult({
    String? coverPath,
  }) {
    return AigcCoverTaskResult(
      coverPath: coverPath,
    );
  }
}

/// 缩略图任务工厂
class AigcThumbnailTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    int priority = 0,
  }) {
    final request = AigcThumbnailTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.thumbnail,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcThumbnailTaskResult createResult({
    String? thumbnailPath,
  }) {
    return AigcThumbnailTaskResult(
      thumbnailPath: thumbnailPath,
    );
  }
}

/// 蒙版任务工厂
class AigcMaskTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    int priority = 0,
    String? modelName,
    double? threshold,
  }) {
    final request = AigcMaskTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      modelName: modelName,
      threshold: threshold,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.mask,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcMaskTaskResult createResult({
    String? outputPath,
  }) {
    return AigcMaskTaskResult(
      outputPath: outputPath,
    );
  }
}

/// 交互式蒙版任务工厂
class AigcInteractiveMaskTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
    int priority = 0,
    String? modelName,
    double? threshold,
  }) {
    final request = AigcInteractiveMaskTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      mattingMaskDataInfo: mattingMaskDataInfo,
      modelName: modelName,
      threshold: threshold,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.interactiveMask,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcInteractiveMaskTaskResult createResult({
    String? outputPath,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
  }) {
    return AigcInteractiveMaskTaskResult(
      outputPath: outputPath,
      mattingMaskDataInfo: mattingMaskDataInfo,
    );
  }
}

/// 导出任务工厂
class AigcImageTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    int priority = 0,
    int? maxWidth,
    int? maxHeight,
    String? format,
  }) {
    final request = AigcImageTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      format: format,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.image,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcImageTaskResult createResult({
    String? outputPath,
  }) {
    return AigcImageTaskResult(
      outputPath: outputPath,
    );
  }
}

/// 智能调色任务工厂
class AigcRawConversionTask {
  static AigcTask create({
    required String taskId,
    required String inputPath,
    required String outputPath,
    required String fileId,
    required DateTime submittedAt,
    int priority = 0,
    bool adjustCustom = true,
    Map<String, dynamic>? config,
  }) {
    final request = AigcRawConversionTaskRequest(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      adjustCustom: adjustCustom,
      config: config,
    );

    return AigcTask(
      taskId: taskId,
      taskType: AigcTaskType.rawConversion,
      request: request,
      submittedAt: submittedAt,
      priority: priority,
    );
  }

  static AigcRawConversionTaskResult createResult({
    String? outputPath,
  }) {
    return AigcRawConversionTaskResult(
      outputPath: outputPath,
    );
  }
}
