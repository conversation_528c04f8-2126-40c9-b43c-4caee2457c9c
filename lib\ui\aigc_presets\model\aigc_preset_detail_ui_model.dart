import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_group.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';

/// 用于展示 AIGC 预设效果的 UI 模型
class AIGCPresetEffectUI {
  final String effectCode;
  final String name;
  final String status; // 效果状态：running, failed, completed
  final String? thumbUrl;
  final String? photoUrl;
  final String? errorMsg; // 错误信息，如果有的话

  final bool isSelected; // UI 状态，是否被选中
  final bool isShowDeleteIcon; // UI 状态，是否显示删除图标

  AIGCPresetEffectUI({
    required this.effectCode,
    required this.name,
    required this.status,
    this.thumbUrl,
    this.photoUrl,
    this.errorMsg,
    this.isSelected = false,
    this.isShowDeleteIcon = false,
  });

  factory AIGCPresetEffectUI.fromServerModel(AigcPresetsEffect model) {
    return AIGCPresetEffectUI(
      effectCode: model.effectCode,
      name: model.name,
      status: model.status,
      thumbUrl: model.thumbUrl,
      photoUrl: model.photoUrl,
      errorMsg: model.errorMsg,
      isSelected: model.isInUse,
      isShowDeleteIcon: false, // 默认不显示删除图标
    );
  }

  AIGCPresetEffectUI copyWith({
    String? status,
    String? thumbUrl,
    String? photoUrl,
    bool? isSelected,
    bool? isShowDeleteIcon,
  }) {
    return AIGCPresetEffectUI(
      effectCode: effectCode,
      name: name,
      status: status ?? this.status,
      thumbUrl: thumbUrl ?? this.thumbUrl,
      photoUrl: photoUrl ?? this.photoUrl,
      isSelected: isSelected ?? this.isSelected,
      isShowDeleteIcon: isShowDeleteIcon ?? this.isShowDeleteIcon,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIGCPresetEffectUI &&
          runtimeType == other.runtimeType &&
          effectCode == other.effectCode &&
          name == other.name &&
          status == other.status &&
          thumbUrl == other.thumbUrl &&
          photoUrl == other.photoUrl &&
          errorMsg == other.errorMsg &&
          isSelected == other.isSelected &&
          isShowDeleteIcon == other.isShowDeleteIcon;

  @override
  int get hashCode =>
      effectCode.hashCode ^
      name.hashCode ^
      status.hashCode ^
      thumbUrl.hashCode ^
      photoUrl.hashCode ^
      errorMsg.hashCode ^
      isSelected.hashCode ^
      isShowDeleteIcon.hashCode;
}

/// Jul 21 新增： 用于展示 AIGC 预设详情中分组信息的 UI 模型
class AIGCPresetGroupUI {
  final String groupId;
  final String supplement; // 创意补充说明;
  final List<AIGCPresetEffectUI> effectList;
  final String createAt;
  final String updateAt;

  String get groupDescription {
    final hasRunningTask =
        effectList.any((element) => element.status == AigcRequestConst.running);

    if (hasRunningTask) {
      return '正在生成';
    }
    final supplementText = supplement.isEmpty ? '无' : supplement;
    return '${effectList.length}张创意 创意补充：$supplementText';
  }

  AIGCPresetGroupUI({
    required this.groupId,
    required this.supplement,
    required this.effectList,
    required this.createAt,
    required this.updateAt,
  });

  factory AIGCPresetGroupUI.fromServerModel(AigcPresetsGroup model) {
    return AIGCPresetGroupUI(
      groupId: model.groupId,
      supplement: model.supplement,
      effectList: model.effectList
          .map((effect) => AIGCPresetEffectUI.fromServerModel(effect))
          .toList(),
      createAt: _formatDate(timestamp: model.createAt, format: 'MM.dd HH:mm'),
      updateAt: _formatDate(timestamp: model.updateAt, format: 'MM.dd HH:mm'),
    );
  }

  AIGCPresetGroupUI copyWith({
    String? groupId,
    String? supplement,
    List<AIGCPresetEffectUI>? effectList,
    String? createAt,
    String? updateAt,
  }) {
    return AIGCPresetGroupUI(
      groupId: groupId ?? this.groupId,
      supplement: supplement ?? this.supplement,
      effectList: effectList ?? this.effectList,
      createAt: createAt ?? this.createAt,
      updateAt: updateAt ?? this.updateAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIGCPresetGroupUI &&
          runtimeType == other.runtimeType &&
          groupId == other.groupId &&
          supplement == other.supplement &&
          effectList == other.effectList &&
          createAt == other.createAt &&
          updateAt == other.updateAt;

  @override
  int get hashCode =>
      Object.hash(groupId, supplement, effectList, createAt, updateAt);
}

/// 用于展示 AIGC 预设详情页面的 UI 模型
class AIGCPresetDetailUI {
  final String id;
  final String name;
  final String status;
  final String createTime;
  final String updateTime;
  final List<AIGCPresetGroupUI> effects;

  AIGCPresetDetailUI({
    required this.id,
    required this.name,
    required this.status,
    required this.createTime,
    required this.updateTime,
    required this.effects,
  });

  factory AIGCPresetDetailUI.fromPresetDetailResponse(
      AigcPcPresetsDetailResponse response) {
    return AIGCPresetDetailUI(
      id: response.id,
      name: response.name,
      status: response.status,
      createTime: _formatDate(timestamp: response.createAt),
      updateTime: _formatDate(timestamp: response.updateAt),
      effects: response.effects
          .map((it) => AIGCPresetGroupUI.fromServerModel(it))
          .toList(),
    );
  }

  AIGCPresetDetailUI copyWith({
    String? name,
    String? status,
    List<AIGCPresetGroupUI>? effects,
  }) {
    return AIGCPresetDetailUI(
      id: id,
      name: name ?? this.name,
      status: status ?? this.status,
      createTime: createTime,
      updateTime: updateTime,
      effects: effects ?? this.effects,
    );
  }

  /// 使用新的响应数据更新，同时保留现有UI状态（如选中状态）
  AIGCPresetDetailUI copyWithNewResponse(AigcPcPresetsDetailResponse response) {
    final currentEffectList =
        effects.expand((element) => element.effectList).toList();

    // 1. 保留当前 UI 上的选中状态
    final selectedCodes = currentEffectList
        .where((effect) => effect.isSelected)
        .map((effect) => effect.effectCode)
        .toSet();

    // 2. 基于新数据创建 UI 模型
    final newPresetDetail =
        AIGCPresetDetailUI.fromPresetDetailResponse(response);

    // 3. 恢复选中状态
    final updatedGroups = newPresetDetail.effects.map((groupUI) {
      final updatedEffects = groupUI.effectList.map((effectUI) {
        final shouldBeSelected =
            effectUI.isSelected || selectedCodes.contains(effectUI.effectCode);
        return effectUI.copyWith(isSelected: shouldBeSelected);
      }).toList();

      return groupUI.copyWith(effectList: updatedEffects);
    }).toList();

    // 4. 返回最终合并后的新实例
    return newPresetDetail.copyWith(effects: updatedGroups);
  }

  /// 获取总数量
  int get totalCount => effects.expand((list) => list.effectList).length;

  /// 获取已选中数量
  int get selectedCount => effects
      .expand((list) => list.effectList)
      .where((effectUI) => effectUI.isSelected)
      .length;

  /// 获取已选中的效果代码列表
  List<String> get selectedEffectCodes => effects
      .expand((list) => list.effectList)
      .where((effectUI) => effectUI.isSelected)
      .map((effectUI) => effectUI.effectCode)
      .toList();

  /// 是否有选中的效果
  bool get hasSelectedEffects => selectedCount > 0;

  /// 删除指定索引的效果
  AIGCPresetDetailUI removeEffectAt(String groupId, String effectCode) {
    final newGroups = effects
        .map((group) {
          if (group.groupId != groupId) {
            return group; // 不是目标组，直接返回原组
          }

          // 获取 effect 所在集合的索引
          final effectIndex = group.effectList
              .indexWhere((element) => element.effectCode == effectCode);

          // 是目标组，检查索引有效性
          if (effectIndex < 0 || effectIndex >= group.effectList.length) {
            return group; // 索引无效，返回原组
          }

          final newEffectList = group.effectList
              .asMap()
              .entries
              .where((entry) => entry.key != effectIndex)
              .map((entry) => entry.value)
              .toList();

          return group.copyWith(effectList: newEffectList);
        })
        .where((element) => element.effectList.isNotEmpty)
        .toList();

    return copyWith(effects: newGroups);
  }

  /// 切换指定索引效果的选中状态
  AIGCPresetDetailUI toggleEffectSelectionAt(
      String groupId, String effectCode) {
    final newGroups = effects.map((group) {
      if (group.groupId != groupId) {
        return group; // 不是目标组，直接返回原组
      }

      // 是目标组，更新其中的效果列表
      final newEffectList = group.effectList.map((effect) {
        if (effect.effectCode == effectCode) {
          // 找到目标效果，切换选中状态
          return effect.copyWith(isSelected: !effect.isSelected);
        }
        return effect; // 不是目标效果，直接返回原效果
      }).toList();

      return group.copyWith(effectList: newEffectList);
    }).toList();

    return copyWith(effects: newGroups);
  }

  /// 通过 groupId 获取获取索引
  int _getGroupUIById(String groupId) {
    return effects.indexWhere((element) => element.groupId == groupId);
  }

  /// 通过 groupId 获取组对象
  AIGCPresetGroupUI? getGroupUIById(String groupId) {
    final index = _getGroupUIById(groupId);
    if (index == -1) {
      return null;
    }
    return effects[index];
  }

  /// 通过 effectCode 获取效果对象
  AIGCPresetEffectUI? getEffectByCode(String effectCode) {
    for (final group in effects) {
      final effect = group.effectList.firstWhereOrNull(
        (effect) => effect.effectCode == effectCode,
      );

      if (effect != null && effect.effectCode.isNotEmpty) {
        return effect;
      }
    }
    return null;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIGCPresetDetailUI &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          status == other.status &&
          createTime == other.createTime &&
          updateTime == other.updateTime &&
          effects == other.effects;

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      status.hashCode ^
      createTime.hashCode ^
      updateTime.hashCode ^
      effects.hashCode;
}

String _formatDate(
    {required int timestamp, String format = 'yyyy.MM.dd HH:mm'}) {
  final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  return DateFormat(format).format(date);
}
