import 'dart:math' as math;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_key.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_manager_mixin.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_canvas_painter_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_image_overlay_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_painter_path_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_smart_box_selection_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/painter/aigc_background_painter.dart';
import 'package:turing_art/ui/aigc_editing/widgets/painter/aigc_cursor_painter.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC画布绘制器组件
///
/// 新架构中的核心绘制容器，支持缩放和平移操作
/// 包含图像叠加组件和路径绘制组件
class AigcCanvasPainterWidget extends StatefulWidget {
  /// 画布宽度
  final double width;

  /// 画布高度
  final double height;

  /// 初始背景图片URL
  final String? initialBackgroundImageUrl;

  /// 图像初始化完成回调
  final Function(Size imageSize)? onImageInitialized;

  /// 构造函数
  const AigcCanvasPainterWidget({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.initialBackgroundImageUrl,
    this.onImageInitialized,
  });

  @override
  State<AigcCanvasPainterWidget> createState() =>
      _AigcCanvasPainterWidgetState();
}

class _AigcCanvasPainterWidgetState extends State<AigcCanvasPainterWidget>
    with ShortcutManagerMixin {
  /// 画布绘制器ViewModel
  late AigcCanvasPainterViewModel _viewModel;

  /// 背景绘制器
  AigcBackgroundPainter? _backgroundPainter;

  /// 控制键是否按下
  bool _isCtrlPressed = false;

  /// 鼠标是否在画布内
  bool _isMouseInCanvas = false;

  /// 全局鼠标位置（用于自定义光标定位）
  Offset _globalMousePosition = Offset.zero;

  /// 是否正在进行指针操作（按下状态）
  bool _isPointerDown = false;

  /// 画布容器的全局键
  final GlobalKey _canvasContainerKey = GlobalKey();

  /// 画布容器的RenderBox
  RenderBox? _canvasRenderBox;

  /// 是否已经完成初始化
  bool _isInitialized = false;

  /// Overlay entry for custom cursor
  OverlayEntry? _cursorOverlayEntry;

  /// 上一次的画布尺寸
  Size? _lastCanvasSize;

  /// 上一次的光标尺寸（用于优化光标重建）
  double? _lastCursorSize;

  /// 上一次的光标模式（用于优化光标重建）
  bool? _lastIsSmartBoxMode;

  @override
  void initState() {
    super.initState();

    // 初始化ViewModel
    final painterProvider = context.read<AigcCanvasPainterProvider>();
    final controlProvider = context.read<AigcEditingControlProvider>();

    _viewModel = AigcCanvasPainterViewModel(
      provider: painterProvider,
      controlProvider: controlProvider,
    );

    // 监听键盘事件
    _setupKeyboardListener();
    _registerShortcuts();

    // 等待组件完全构建后设置画布尺寸
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 检查widget是否仍然mounted，避免在已销毁的widget上操作
      if (!mounted) {
        return;
      }

      // 获取画布RenderBox
      _canvasRenderBox =
          _canvasContainerKey.currentContext?.findRenderObject() as RenderBox?;

      final Size actualSize = _getActualCanvasSize();
      _viewModel.setCanvasSize(actualSize);

      // 通知图像叠加提供者画布尺寸已设置，可以进行适配计算
      try {
        final imageOverlayProvider = context.read<AigcImageOverlayProvider>();
        imageOverlayProvider.handleCanvasSizeChanged(actualSize);
      } catch (e) {
        // 忽略错误，可能是因为context已经被销毁
        PGLog.d('AigcCanvasPainterWidget: 设置画布尺寸时发生错误: $e');
      }
    });
  }

  @override
  void dispose() {
    _removeKeyboardListener();
    _removeCursorOverlay();
    super.dispose();
  }

  void _registerShortcuts() {
    registerShortcut(
        id: 'undo',
        shortcut: ShortcutKey.undo,
        handler: () {
          final historyProvider = context.read<AigcEditingHistoryProvider>();
          if (historyProvider.canUndo) {
            historyProvider.undo();
          }
          return KeyEventHandleResult.handled;
        },
        description: '撤销',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });

    registerShortcut(
        id: 'redo',
        shortcut: ShortcutKey.redo,
        handler: () {
          final historyProvider = context.read<AigcEditingHistoryProvider>();
          if (historyProvider.canRedo) {
            historyProvider.redo();
          }
          return KeyEventHandleResult.handled;
        },
        description: '重做',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });
  }

  /// 设置键盘监听
  void _setupKeyboardListener() {
    HardwareKeyboard.instance.addHandler(_handleKeyEvent);
  }

  /// 移除键盘监听
  void _removeKeyboardListener() {
    HardwareKeyboard.instance.removeHandler(_handleKeyEvent);
  }

  /// 处理键盘事件
  bool _handleKeyEvent(KeyEvent event) {
    // 更新Ctrl键状态
    final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    if (_isCtrlPressed != isCtrlPressed) {
      setState(() {
        _isCtrlPressed = isCtrlPressed;
      });
    }

    return false; // Let other handlers also process this event
  }

  /// 获取实际画布尺寸
  Size _getActualCanvasSize() {
    return Size(
      widget.width == double.infinity
          ? MediaQuery.of(context).size.width
          : widget.width,
      widget.height == double.infinity
          ? MediaQuery.of(context).size.height
          : widget.height,
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 当约束变化时，更新画布尺寸到Provider
        final canvasSize = Size(constraints.maxWidth, constraints.maxHeight);
        if (_lastCanvasSize != canvasSize) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              // if (_viewModel.currentRealCanvasSize != null) {
              //   _viewModel.setCanvasSize(_viewModel.currentRealCanvasSize!);
              // }
              // 同时通知图像叠加提供者重新计算适配缩放
              try {
                final imageOverlayProvider =
                    context.read<AigcImageOverlayProvider>();
                imageOverlayProvider.handleCanvasSizeChanged(canvasSize);
              } catch (e) {
                // 忽略错误，可能是因为context已经被销毁
                debugPrint('AigcCanvasPainterWidget: 画布尺寸变化处理时发生错误: $e');
              }
            }
          });
          _lastCanvasSize = canvasSize;
        }

        return Consumer2<AigcCanvasPainterProvider, AigcEditingControlProvider>(
          builder: (context, painterProvider, pathProvider, child) {
            // 监听模式变化
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _handleModeChange();
              }
            });

            // 获取内容尺寸（图片尺寸）
            final contentSize = painterProvider.contentSize;

            // 如果有内容尺寸，使用内容尺寸；否则使用约束尺寸
            final Size baseSize;
            if (contentSize != null) {
              // 图片已加载，使用图片尺寸
              baseSize = contentSize;
            } else {
              // 图片未加载，使用约束尺寸
              baseSize = Size(
                widget.width == double.infinity
                    ? constraints.maxWidth
                    : widget.width,
                widget.height == double.infinity
                    ? constraints.maxHeight
                    : widget.height,
              );
            }

            // 应用缩放到尺寸
            final scaledSize = Size(
              baseSize.width * painterProvider.scale,
              baseSize.height * painterProvider.scale,
            );

            // 计算位置（居中 + 偏移）
            final centerX = constraints.maxWidth / 2 - scaledSize.width / 2;
            final centerY = constraints.maxHeight / 2 - scaledSize.height / 2;
            final position = Offset(
              centerX + painterProvider.offset.dx,
              centerY + painterProvider.offset.dy,
            );

            // 计算画布容器的实际尺寸
            // 如果有图片内容，使用图片的缩放后尺寸；否则使用约束尺寸
            final containerSize = contentSize != null
                ? Size(
                    math.max(scaledSize.width, constraints.maxWidth),
                    math.max(scaledSize.height, constraints.maxHeight),
                  )
                : Size(constraints.maxWidth, constraints.maxHeight);

            // 使用ClipRect包装整个视图，确保超出边界的内容被裁剪
            return Stack(
              clipBehavior: Clip.none, // 允许子组件超出边界显示
              children: [
                // 使用全屏监听器来捕获所有指针事件，确保拖拽时也能跟踪
                Positioned.fill(
                  child: Listener(
                    onPointerMove: _handlePointerMove,
                    onPointerUp: _handlePointerUp,
                    behavior: HitTestBehavior.translucent,
                    child: Container(),
                  ),
                ),
                Listener(
                  key: _canvasContainerKey,
                  onPointerMove: _handlePointerMove,
                  onPointerHover: _handlePointerHover,
                  onPointerDown: _handlePointerDown,
                  onPointerUp: _handlePointerUp,
                  child: MouseRegion(
                    onEnter: (_) {
                      setState(() {
                        _isMouseInCanvas = true;
                      });
                      // 根据当前模式显示对应的自定义光标
                      _showCursorOverlay();
                    },
                    onExit: (_) {
                      // 只有在没有按下指针的情况下才设置为 false
                      if (!_isPointerDown) {
                        setState(() {
                          _isMouseInCanvas = false;
                        });
                        // 移除自定义光标
                        _removeCursorOverlay();
                      }
                    },
                    cursor: SystemMouseCursors.none, // 根据模式设置光标
                    child: ClipRect(
                      child: SizedBox(
                        width: containerSize.width,
                        height: containerSize.height,
                        child: Stack(
                          children: [
                            Positioned(
                              left: position.dx,
                              top: position.dy,
                              child: GestureDetector(
                                // 处理拖拽（仅在按住Ctrl键时）
                                onPanStart: _isCtrlPressed
                                    ? (details) =>
                                        _handlePanStart(details.localPosition)
                                    : null,
                                onPanUpdate: _isCtrlPressed
                                    ? (details) =>
                                        _handlePanUpdate(details.delta)
                                    : null,
                                onPanEnd: _isCtrlPressed
                                    ? (details) => _handlePanEnd()
                                    : null,
                                // 设置行为，允许事件穿透到子组件
                                behavior: _isCtrlPressed
                                    ? HitTestBehavior.opaque
                                    : HitTestBehavior.translucent,

                                child: Listener(
                                  // 处理鼠标滚轮缩放
                                  onPointerSignal: (event) =>
                                      _handlePointerSignal(event),
                                  child: SizedBox(
                                    width: scaledSize.width,
                                    height: scaledSize.height,
                                    child: Consumer2<AigcEditingControlProvider,
                                        AigcCanvasPainterProvider>(
                                      builder: (context, controlProvider,
                                          painterProvider, child) {
                                        return ClipRect(
                                          child: CustomPaint(
                                            painter: _getBackgroundPainter(
                                                controlProvider, scaledSize),
                                            size: scaledSize,
                                            child:
                                                _buildCanvasContent(scaledSize),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 获取背景绘制器
  AigcBackgroundPainter _getBackgroundPainter(
      AigcEditingControlProvider controlProvider, Size actualDrawSize) {
    final currentColor = controlProvider.maskBackgroundColor.color;
    final canvasProvider = context.read<AigcCanvasPainterProvider>();
    final imageOverlayProvider = context.read<AigcImageOverlayProvider>();

    // 在混合模式下，不绘制背景，避免与ImageOverlayPainter重复绘制
    // isShowSingleMattingScope = true 表示普通模式，false 表示混合模式
    final isEyeOpen = controlProvider.isShowSingleMattingScope;
    final hasImageContent = canvasProvider.contentSize != null;

    // 只有在眼睛开启且有图片内容时才绘制背景，其他情况绘制透明背景
    final shouldDrawBackground = isEyeOpen && hasImageContent;

    // 创建或更新背景绘制器
    if (_backgroundPainter == null) {
      _backgroundPainter = AigcBackgroundPainter(
          shouldDrawBackground
              ? currentColor
              : MaskBackgroundColor.transparent.color,
          controlProvider: controlProvider);
    } else {
      // 更新现有绘制器的显示模式
      _backgroundPainter!.updateDisplayMode();
    }

    // 只有在初始化完成且不在图片切换期间才显示背景
    if (_isInitialized && !imageOverlayProvider.isImageSwitching) {
      _backgroundPainter!.setVisible(visible: true);
    } else {
      _backgroundPainter!.setVisible(visible: false);
    }

    return _backgroundPainter!;
  }

  /// 构建画布内容
  Widget _buildCanvasContent(Size scaledSize) {
    return Consumer<AigcCanvasPainterProvider>(
      builder: (context, painterProvider, child) {
        // // 计算缩放后的尺寸
        // final scaledSize = Size(
        //   canvasSize.width * painterProvider.scale,
        //   canvasSize.height * painterProvider.scale,
        // );

        // 使用缩放后的尺寸作为容器尺寸，确保与CustomPaint尺寸一致
        return SizedBox(
          width: scaledSize.width,
          height: scaledSize.height,
          child: Stack(
            children: [
              // 图像叠加组件（底层）- 明确传递缩放后的尺寸
              Positioned.fill(
                child: AigcImageOverlayWidget(
                  onImageInitialized: _handleImageInitialized,
                  explicitCanvasSize: scaledSize,
                ),
              ),

              // 智能框选组件，画布大小与位置变化时不响应
              if (!painterProvider.isDragging &&
                  !painterProvider.isZooming) ...[
                const Positioned.fill(
                  child: AigcSmartBoxSelectionWidget(),
                )
              ],

              // 路径绘制组件
              const Positioned.fill(
                child: AigcPainterPathWidget(),
              ),

              // 如果还没有内容尺寸，在顶层显示加载提示
              Consumer<AigcCanvasPainterProvider>(
                builder: (context, painterProvider, child) {
                  if (painterProvider.contentSize == null) {
                    return Positioned.fill(
                      child: Container(
                        color: Colors.transparent,
                        child: const Center(
                          child: Text(
                            '等待图片加载...',
                            style: TextStyle(color: Colors.white54),
                          ),
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 处理拖拽开始
  void _handlePanStart(Offset localPosition) {
    _viewModel.handleDragStart(localPosition);
  }

  /// 处理拖拽更新
  void _handlePanUpdate(Offset delta) {
    _viewModel.handleDragUpdate(delta);
  }

  /// 处理拖拽结束
  void _handlePanEnd() {
    _viewModel.handleDragEnd();
  }

  /// 处理图像初始化完成
  void _handleImageInitialized(Size imageSize) {
    // 设置内容尺寸
    _viewModel.setContentSize(imageSize);

    // 通知外部回调
    widget.onImageInitialized?.call(imageSize);

    // 所有组件已构建完成，设置背景绘制器为可见
    setState(() {
      _isInitialized = true;
      if (_backgroundPainter != null) {
        _backgroundPainter!.setVisible(visible: true);
      }
    });
  }

  /// 处理指针信号（鼠标滚轮）
  void _handlePointerSignal(PointerSignalEvent event) {
    if (event is PointerScrollEvent) {
      final delta = event.scrollDelta.dy;
      _viewModel.handleScrollZoom(delta, Offset.zero);
    }
  }

  /// 处理指针移动事件
  void _handlePointerMove(PointerMoveEvent event) {
    _updateMousePosition(event.position);
    _checkMouseInCanvas(event.position);
    // 确保光标在移动时正确显示
    _ensureCursorVisible();
  }

  /// 处理指针悬停事件
  void _handlePointerHover(PointerHoverEvent event) {
    _updateMousePosition(event.position);
    _checkMouseInCanvas(event.position);
    // 确保光标在悬停时正确显示
    _ensureCursorVisible();
  }

  /// 处理指针按下事件
  void _handlePointerDown(PointerDownEvent event) {
    // 只处理鼠标左键
    if (event.buttons == kPrimaryMouseButton) {
      setState(() {
        _isPointerDown = true;
      });
      // 显示全局光标
      _showCursorOverlay();
    }
    _updateMousePosition(event.position);
    _checkMouseInCanvas(event.position);
  }

  /// 处理指针抬起事件
  void _handlePointerUp(PointerUpEvent event) {
    setState(() {
      _isPointerDown = false;
    });
    _updateMousePosition(event.position);
    _checkMouseInCanvas(event.position);

    // 只有在鼠标不在画布内时才移除光标
    if (!_isMouseInCanvas) {
      _removeCursorOverlay();
    }
  }

  /// 更新鼠标位置
  void _updateMousePosition(Offset globalPosition) {
    _canvasRenderBox ??=
        _canvasContainerKey.currentContext?.findRenderObject() as RenderBox?;

    setState(() {
      _globalMousePosition = globalPosition; // 保存全局位置
    });

    // 更新overlay光标位置
    _updateCursorOverlay();

    // if (_canvasRenderBox != null) {
    //   // 将全局坐标转换为相对于画布的本地坐标
    //   final localPosition = _canvasRenderBox!.globalToLocal(globalPosition);
    //   setState(() {
    //     _mousePosition = localPosition;
    //   });
    // }
  }

  /// 检查鼠标是否在画布内
  void _checkMouseInCanvas(Offset globalPosition) {
    _canvasRenderBox ??=
        _canvasContainerKey.currentContext?.findRenderObject() as RenderBox?;

    if (_canvasRenderBox != null) {
      // 将全局坐标转换为相对于画布的本地坐标
      final localPosition = _canvasRenderBox!.globalToLocal(globalPosition);

      // 获取画布尺寸
      final canvasSize = _canvasRenderBox!.size;

      // 检查鼠标是否在画布边界内
      final isInCanvas = localPosition.dx >= 0 &&
          localPosition.dy >= 0 &&
          localPosition.dx <= canvasSize.width &&
          localPosition.dy <= canvasSize.height;

      // 只有在不处于指针按下状态时才根据边界判断更新 _isMouseInCanvas
      // 在指针按下状态时，即使移出边界也保持光标显示
      if (!_isPointerDown && _isMouseInCanvas != isInCanvas) {
        setState(() {
          _isMouseInCanvas = isInCanvas;
        });
      }
    }
  }

  /// 获取画笔大小
  double _getBrushSize() {
    try {
      final pathProvider =
          Provider.of<AigcEditingControlProvider>(context, listen: false);
      return pathProvider.brushSize;
    } catch (e) {
      return 10.0; // 默认大小
    }
  }

  /// 检查是否为智能框选模式
  bool _isSmartBoxSelectionMode() {
    try {
      final controlProvider = context.read<AigcEditingControlProvider>();
      return controlProvider.isInSmartBoxSelectionMode;
    } catch (e) {
      return false;
    }
  }

  /// 处理模式切换时的光标更新
  void _handleModeChange() {
    // 应该显示自定义光标，如果鼠标在画布内，显示自定义光标
    if (_isMouseInCanvas || _isPointerDown) {
      _showCursorOverlay();
    }
  }

  /// 构建自定义光标
  Widget _buildCustomCursor() {
    if (_isSmartBoxSelectionMode()) {
      // 智能框选模式：使用十字光标
      const cursorSize = AigcCrosshairCursorPainter.crossCursorSize;
      return CustomPaint(
        size: const Size(cursorSize, cursorSize),
        painter: AigcCrosshairCursorPainter(cursorSize),
      );
    } else {
      // 普通绘制模式：使用圆形光标
      final brushSize = _getBrushSize();
      return CustomPaint(
        size: Size(brushSize, brushSize),
        painter: AigcCursorPainter(brushSize),
      );
    }
  }

  /// 显示全局自定义光标
  void _showCursorOverlay() {
    if (!mounted) {
      return;
    }

    // 根据模式计算光标尺寸和偏移
    final isSmartBoxMode = _isSmartBoxSelectionMode();
    double cursorSize;
    if (isSmartBoxMode) {
      cursorSize = AigcCrosshairCursorPainter.crossCursorSize;
    } else {
      cursorSize = _getBrushSize();
    }

    // 检查是否需要重新创建overlay
    final needRecreate = _cursorOverlayEntry == null ||
        _lastIsSmartBoxMode != isSmartBoxMode ||
        (!isSmartBoxMode && _lastCursorSize != cursorSize);

    if (needRecreate) {
      // 移除旧的overlay
      if (_cursorOverlayEntry != null) {
        _cursorOverlayEntry!.remove();
        _cursorOverlayEntry = null;
      }

      // 创建新的overlay，builder函数会在每次markNeedsBuild时重新执行
      _cursorOverlayEntry = OverlayEntry(
        builder: (context) {
          // 动态获取当前的光标信息
          final currentIsSmartBoxMode = _isSmartBoxSelectionMode();
          final currentCursorSize = currentIsSmartBoxMode
              ? AigcCrosshairCursorPainter.crossCursorSize
              : _getBrushSize();

          return Positioned(
            left: _globalMousePosition.dx - currentCursorSize / 2,
            top: _globalMousePosition.dy - currentCursorSize / 2,
            child: IgnorePointer(
              child: _buildCustomCursor(),
            ),
          );
        },
      );

      // 缓存当前状态
      _lastCursorSize = cursorSize;
      _lastIsSmartBoxMode = isSmartBoxMode;

      Overlay.of(context).insert(_cursorOverlayEntry!);
    } else if (_cursorOverlayEntry != null) {
      // 只更新位置，不重新创建
      _updateCursorOverlay();
    }
  }

  /// 更新全局自定义光标位置
  void _updateCursorOverlay() {
    if (_cursorOverlayEntry == null || !mounted) {
      return;
    }

    // 只触发重建，不重新创建overlay
    _cursorOverlayEntry!.markNeedsBuild();
  }

  /// 移除全局自定义光标
  void _removeCursorOverlay() {
    if (_cursorOverlayEntry != null) {
      _cursorOverlayEntry!.remove();
      _cursorOverlayEntry = null;
      // 清除缓存状态
      _lastCursorSize = null;
      _lastIsSmartBoxMode = null;
    }
  }

  /// 检查并恢复光标状态（用于处理意外丢失的光标）
  void _ensureCursorVisible() {
    // 如果鼠标在画布内或正在按下，但没有自定义光标，则重新显示
    if ((_isMouseInCanvas || _isPointerDown) && _cursorOverlayEntry == null) {
      _showCursorOverlay();
    }
  }
}
