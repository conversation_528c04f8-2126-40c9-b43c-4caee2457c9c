import 'package:cross_file/cross_file.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/app_constants.dart';

import 'drag_drop_handler.dart';
import 'drag_drop_interface.dart';

class DragDropService {
  static final isDesktop = AppConstants.isDesktop;

  final DragDropInterface _handler;

  DragDropService._({required DragDropInterface handler}) : _handler = handler;

  /// 处理拖放文件为单个项目
  Future<DealImageFilesResult?> processDroppedFilesForSingleProject(
      List<XFile> files) {
    return _handler.processDroppedFilesForSingleProject(files);
  }

  /// 处理拖放文件为多个项目
  Future<List<DealImageFilesResult>> processDroppedFilesForMultiProject(
      List<XFile> files) {
    return _handler.processDroppedFilesForMultiProject(files);
  }

  factory DragDropService.forPlatform() {
    final handler =
        isDesktop ? DesktopDragDropHandler() : MobileDragDropHandler();
    return DragDropService._(handler: handler);
  }
}
