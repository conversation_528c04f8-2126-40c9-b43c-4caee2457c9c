import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';

class ProjectHomeMutiChoiceTopBar extends StatefulWidget {
  const ProjectHomeMutiChoiceTopBar({
    super.key,
    this.batchPresetClick,
    this.batchDeleteClick,
    this.allClick,
    this.cancelClick,
    required this.sideMargin,
  });

  final VoidCallback? batchPresetClick;
  final VoidCallback? batchDeleteClick;
  final VoidCallback? allClick;
  final VoidCallback? cancelClick;
  final double sideMargin;

  @override
  State<ProjectHomeMutiChoiceTopBar> createState() =>
      _ProjectHomeMutiChoiceTopBarState();
}

class _ProjectHomeMutiChoiceTopBarState
    extends State<ProjectHomeMutiChoiceTopBar> {
  bool _isConfirmHovered = false;
  bool _isDeleteHovered = false;

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<HomeViewModel>();
    final mutiCount = viewModel.mutiCount;
    final totalCount = viewModel.projects.length;
    return Container(
      height: 56,
      color: const Color(0xFF0D0D0D),
      child: Row(
        children: [
          SizedBox(width: widget.sideMargin),
          GestureDetector(
            onTap: widget.cancelClick,
            child: PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              child: SizedBox(
                width: 76,
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icons/home_pc_top_back.png',
                      color: Colors.white,
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      '返回',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontFamily: Fonts.fontFamilySF,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                ),
              ),
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.only(left: 0, top: 0),
            child: Row(
              children: [
                Text(
                  '已选 $mutiCount',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: Fonts.medium,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '/ 总共$totalCount个项目',
                  style: TextStyle(
                    color: const Color(0xFFEBF2F5).withAlpha(150),
                    fontSize: 12,
                    fontWeight: Fonts.regular,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // 批量删除按钮
          PlatformMouseRegion(
            onEnter: (_) => setState(() => _isDeleteHovered = true),
            onExit: (_) => setState(() => _isDeleteHovered = false),
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: mutiCount > 0 ? widget.batchDeleteClick : null,
              child: Container(
                width: 100,
                height: 32,
                decoration: BoxDecoration(
                  color: _isDeleteHovered
                      ? Colors.white.withOpacity(0.1)
                      : Colors.white.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icons/home_pc_top_delete.png',
                      color: mutiCount > 0
                          ? Colors.white
                          : Colors.white.withOpacity(0.6),
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 1),
                    Text(
                      '批量删除',
                      style: TextStyle(
                        fontSize: 12,
                        color: mutiCount > 0
                            ? Colors.white
                            : Colors.white.withOpacity(0.6),
                        fontFamily: Fonts.fontFamilySF,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 批量修图按钮
          PlatformMouseRegion(
            onEnter: (_) => setState(() => _isConfirmHovered = true),
            onExit: (_) => setState(() => _isConfirmHovered = false),
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: mutiCount > 0 ? widget.batchPresetClick : null,
              child: Container(
                width: 100,
                height: 32,
                decoration: BoxDecoration(
                  color: _isConfirmHovered
                      ? Colors.white.withOpacity(0.1)
                      : Colors.white.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icons/home_pc_top_batch_export.png',
                      color: mutiCount > 0
                          ? Colors.white
                          : Colors.white.withOpacity(0.6),
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 1),
                    Text(
                      '批量修图',
                      style: TextStyle(
                        fontSize: 12,
                        color: mutiCount > 0
                            ? Colors.white
                            : Colors.white.withOpacity(0.6),
                        fontFamily: Fonts.fontFamilySF,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(width: widget.sideMargin),
        ],
      ),
    );
  }
}
