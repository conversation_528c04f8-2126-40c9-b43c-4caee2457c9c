import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_effect_item_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 预设详情页面效果列表外层 item 组件
class PresetDetailGroupItemWidget extends StatelessWidget {
  final AIGCPresetGroupUI groupUI;

  const PresetDetailGroupItemWidget({
    super.key,
    required this.groupUI,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          groupUI.createAt,
          style: TextStyle(
            color: const Color(0x59FFFFFF),
            fontSize: 12,
            fontFamily: Fonts.defaultFontFamily,
          ),
        ),
        const SizedBox(height: 4),
        _HoverableTitle(
          text: groupUI.groupDescription,
          enableSelect: groupUI.supplement.isNotEmpty,
          onTap: () => _copyToClipboard(context, groupUI.supplement),
        ),
        const SizedBox(height: 8),
        _buildGridView(groupUI.effectList),
      ],
    );
  }

  /// 构建显示创意图的网格视图
  Widget _buildGridView(List<AIGCPresetEffectUI> effectList) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 1.0,
      ),
      itemCount: effectList.length,
      itemBuilder: (context, index) {
        return PresetEffectItemWidget(
          key: ValueKey('creative_item_$index'),
          groupId: groupUI.groupId,
          effectUI: effectList[index],
          creativeItemSize: PresetDetailConstants.imageContainerSize,
        );
      },
    );
  }

  /// 复制文本到剪贴板
  Future<void> _copyToClipboard(BuildContext context, String text) async {
    await Clipboard.setData(ClipboardData(text: text));

    // 显示复制成功提示
    if (context.mounted) {
      PGDialog.showToast('已复制到剪贴板');
    }
  }
}

class _HoverableTitle extends StatefulWidget {
  final String text;
  final bool enableSelect;
  final VoidCallback? onTap;

  const _HoverableTitle({
    required this.text,
    required this.enableSelect,
    this.onTap,
  });

  @override
  State<_HoverableTitle> createState() => _HoverableTitleState();
}

class _HoverableTitleState extends State<_HoverableTitle> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final opacity = _isHovered ? 1.0 : 0.7;
    return PlatformMouseRegion(
      onEnter: (_) => _notifyUI(true),
      onExit: (_) => _notifyUI(false),
      onTap: widget.enableSelect ? widget.onTap : null,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            widget.text,
            style: TextStyle(
              color: Colors.white.withOpacity(opacity),
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          if (widget.enableSelect) ...[
            const SizedBox(width: 2),
            Opacity(
              opacity: opacity,
              child: Image.asset('assets/icons/icon_copy_small.png',
                  width: 16, height: 16, color: Colors.white),
            ),
          ]
        ],
      ),
    );
  }

  void _notifyUI(bool hovered) {
    if (!widget.enableSelect) {
      return;
    }
    setState(() => _isHovered = hovered);
  }
}
