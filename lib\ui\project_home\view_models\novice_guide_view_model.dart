import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_chain.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_factory.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 引导数据类，包含显示引导所需的所有信息
class NoviceGuideData {
  final bool shouldShow; // 是否需要显示引导
  final ProjectInfo? projectInfo; // 项目信息
  final int? projectIndex; // 项目索引
  final NoviceGuideInfo? guideInfo; // 引导信息

  NoviceGuideData({
    required this.shouldShow,
    this.projectInfo,
    this.projectIndex,
    this.guideInfo,
  });
}

/// 新手引导Demo项目ViewModel
/// 负责处理新手引导第一步相关的业务逻辑（demo项目创建和显示）
class NoviceGuideDemoProjectViewModel extends ChangeNotifier {
  final NoviceGuideManager _noviceGuideManager;
  final ProjectUseCaseProvider _projectUseCase;
  final CurrentUserRepository _currentUserRepository;
  final UnityController _unityController;
  final ProjectStateProvider _projectStateProvider;
  final UnityUseCaseProvider _unityUseCase;
  final WorkspaceUseCaseProvider _workspaceUseCase;
  final MediaRepository _mediaRepository;
  final ProjectRepository _projectRepository;
  // 处理链工厂和状态变量
  late final ProjectCreateSelectHandlerFactory _projectHandlerChainFactory;

  // 项目列表
  List<ProjectInfo> _projects = <ProjectInfo>[];
  List<ProjectInfo> get projects => _projects;

  NoviceGuideDemoProjectViewModel(
    this._noviceGuideManager,
    this._projectUseCase,
    this._currentUserRepository,
    this._unityController,
    this._projectStateProvider,
    this._unityUseCase,
    this._workspaceUseCase,
    this._mediaRepository,
    this._projectRepository,
  ) {
    // 初始化责任链工厂
    _projectHandlerChainFactory = ProjectCreateSelectHandlerFactory(
      projectUseCase: _projectUseCase,
      unityUseCase: _unityUseCase,
      workspaceUseCase: _workspaceUseCase,
      projectStateProvider: _projectStateProvider,
      unityController: _unityController,
      currentUserRepository: _currentUserRepository,
      mediaRepository: _mediaRepository,
      projectRepository: _projectRepository,
    );
  }

  /// public唯一对外接口：检查并处理新手引导逻辑
  Future<NoviceGuideData> handleFirstStepDemoProjectNoviceGuide() async {
    // 检查是否需要处理demo项目步骤
    final shouldHandle =
        await _noviceGuideManager.shouldHandleDemoProjectStep();

    if (!shouldHandle) {
      PGLog.d('不需要处理demo项目步骤');
      return NoviceGuideData(shouldShow: false);
    }

    // 无论是否有项目，先尝试创建演示项目
    await _createDemoProjectIfNeeded();

    // 查找演示项目并准备引导数据
    return await _findDemoProjectAndShowNoviceGuide();
  }

  // 如果需要，创建演示项目
  Future<void> _createDemoProjectIfNeeded() async {
    final created = await _checkAndCreateDemoProject();
    if (created) {
      // 如果创建了演示项目，重新加载项目列表
      await _loadProjects();
    }
  }

  // 加载项目列表
  Future<void> _loadProjects() async {
    _projects = await _projectUseCase.loadEditProjects.invoke();
  }

  Future<NoviceGuideData> _findDemoProjectAndShowNoviceGuide() async {
    await _loadProjects();

    if (_projects.isEmpty) {
      PGLog.d('_findDemoProjectAndShowNoviceGuide-项目列表为空，无法显示新手引导');
      return NoviceGuideData(shouldShow: false);
    }

    // 获取演示项目ID
    final String demoProjectId = _noviceGuideManager.getDemoProjectId();
    PGLog.d('_findDemoProjectAndShowNoviceGuide-演示项目ID: $demoProjectId');

    // 查找演示项目，如果不存在则使用第一个项目
    ProjectInfo? demoProject;
    int demoProjectIndex = -1;

    for (int i = 0; i < projects.length; i++) {
      if (projects[i].uuid == demoProjectId) {
        demoProject = projects[i];
        demoProjectIndex = i;
        break;
      }
    }

    // 如果找不到演示项目，则使用第一个项目
    if (demoProject == null) {
      demoProject = projects.first;
      demoProjectIndex = 0;
      PGLog.d('_findDemoProjectAndShowNoviceGuide-未找到演示项目，使用第一个项目作为引导目标');
    } else {
      PGLog.d(
          '_findDemoProjectAndShowNoviceGuide-找到演示项目，位于索引: $demoProjectIndex');
    }

    // 获取新手引导信息
    final guideInfo = await _noviceGuideManager.getGuideInfo(step: 0);
    if (guideInfo == null) {
      PGLog.d('_findDemoProjectAndShowNoviceGuide-无法获取引导信息');
      return NoviceGuideData(shouldShow: false);
    }

    // 由于GridView可能还没有完成布局，我们需要等待下一帧
    // 这样可以确保ProjectHomePCGridView._itemKeys已经被填充
    // 返回一个可以显示引导的数据，包含项目索引
    // 视图层需要使用索引获取实际位置
    PGLog.d(
        '_findDemoProjectAndShowNoviceGuide-返回新手引导数据，项目索引: $demoProjectIndex');
    return NoviceGuideData(
      shouldShow: true,
      projectInfo: demoProject,
      projectIndex: demoProjectIndex,
      guideInfo: guideInfo,
    );
  }

  /// 检查并创建演示项目
  Future<bool> _checkAndCreateDemoProject() async {
    try {
      // 先确保项目列表已加载
      await _loadProjects();

      // 获取当前项目ID列表
      final List<String> projectIds =
          _projects.map((project) => project.uuid).toList();

      // 检查是否需要创建演示项目
      final shouldCreate =
          await _noviceGuideManager.shouldCreateDemoProject(projectIds);
      if (!shouldCreate) {
        PGLog.d('不需要创建演示项目');
        return false;
      }

      PGLog.d('需要创建演示项目');

      // 获取演示项目资源路径
      final String resourcePath = _noviceGuideManager.getResourcePath(0);
      if (resourcePath.isEmpty) {
        PGLog.e('演示项目资源路径为空');
        return false;
      }

      // 创建演示项目
      return await _createDemoProject(resourcePath);
    } catch (e) {
      PGLog.e('检查并创建演示项目失败: $e');
      return false;
    }
  }

  /// 准备演示项目资源目标路径
  Future<File?> _prepareDemoResourceTargetPath(String resourcePath) async {
    try {
      // 使用用户目录而非应用文档目录
      final Directory? userDir = FileManager().getUserRootDir();
      if (userDir == null) {
        PGLog.e('_prepareDemoResourceTargetPath - 用户根目录不存在');
        return null;
      }

      final String fileName = resourcePath.split('/').last;
      final demoImageResourcesDir = path.join(userDir.path, 'demo_resources');

      // 确保目录存在
      try {
        final demoDir = Directory(demoImageResourcesDir);
        if (!demoDir.existsSync()) {
          demoDir.createSync(recursive: true);
          PGLog.d(
              '_prepareDemoResourceTargetPath - 创建演示资源目录: $demoImageResourcesDir');
        }
      } catch (e) {
        PGLog.e('_prepareDemoResourceTargetPath - 创建演示资源目录失败: $e');
        return null;
      }

      // 创建目标文件路径
      final String targetPath = '$demoImageResourcesDir/$fileName';

      // 不管文件是否存在，都重新复制，确保资源是最新的
      final File targetFile = File(targetPath);
      try {
        if (targetFile.existsSync()) {
          // 如果文件存在，先删除
          await targetFile.delete();
          PGLog.d('_prepareDemoResourceTargetPath - 删除旧演示资源文件: $targetPath');
        }
      } catch (e) {
        PGLog.e('_prepareDemoResourceTargetPath - 删除旧演示资源文件失败: $e');
      }

      try {
        // 复制资源文件
        final ByteData data = await rootBundle.load(resourcePath);
        final List<int> bytes = data.buffer.asUint8List();
        await targetFile.writeAsBytes(bytes);
        PGLog.d('_prepareDemoResourceTargetPath - 复制演示资源文件成功: $targetPath');
        return targetFile;
      } catch (e) {
        PGLog.e('_prepareDemoResourceTargetPath - 复制演示资源文件失败: $e');
        return null;
      }
    } catch (e) {
      PGLog.e('_prepareDemoResourceTargetPath - 准备演示资源目标路径失败: $e');
      return null;
    }
  }

  /// 创建演示项目
  Future<bool> _createDemoProject(String resourcePath) async {
    try {
      final targetFile = await _prepareDemoResourceTargetPath(resourcePath);
      PGLog.d('_createDemoProject -演示项目资源路径: $targetFile');

      // 检查文件是否存在
      if (targetFile == null || !targetFile.existsSync()) {
        PGLog.e('_createDemoProject -文件不存在: $targetFile');
        return false;
      }

      // 获取演示项目ID
      final String demoProjectId = _noviceGuideManager.getDemoProjectId();
      if (demoProjectId.isEmpty) {
        PGLog.e('_createDemoProject -生成演示项目ID失败');
        return false;
      }

      final result = await _createDemoProjectProcess(
        [targetFile],
        projectId: demoProjectId,
        projectName: '演示项目',
      );

      if (result != null) {
        PGLog.d('_createDemoProject - 创建演示项目成功: $demoProjectId');
        return true;
      } else {
        PGLog.e('_createDemoProject - 创建演示项目失败');
        return false;
      }
    } catch (e) {
      PGLog.e('_createDemoProject - 创建演示项目失败: $e');
      return false;
    }
  }

  /// 创建项目但不自动进入编辑页面，使用责任链模式（新入口，保留原有方法）
  Future<ProcessFilesUiStatus?> _createDemoProjectProcess(
    List<File> files, {
    String? projectId,
    String? projectName,
  }) async {
    PGLog.d('_createDemoProjectProcess - start');
    try {
      // 创建项目处理上下文
      final handlerContext = ProjectCreateSelectHandlerContext(
        files: files,
        projectName: projectName,
        projectId: projectId,
        forBatch: false, // 非批处理模式
      );

      // 1. 使用工厂创建只创建项目的责任链
      final createProjectChain =
          _projectHandlerChainFactory.createOnlyCreateProjectChain();
      final createResult = await createProjectChain.process(handlerContext);
      if (createResult is ErrorStatus) {
        PGLog.e('_createDemoProjectProcess - 创建项目失败: $createResult');
        return createResult;
      }

      // 2. 异步去等待Unity初始化并设置工作区
      _waitUnityAndSetupWorkspace(handlerContext);
      return const ProcessFilesUiStatus.success();
    } on Exception catch (e) {
      PGLog.e('_createDemoProjectProcess - 创建项目时出错: $e');
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    }
  }

  /// 异步等待Unity初始化并设置工作区和导入图片
  Future<void> _waitUnityAndSetupWorkspace(
    ProjectCreateSelectHandlerContext handlerContext,
  ) async {
    try {
      // 检查Unity是否初始化完成
      if (!_unityController.isInitialized) {
        PGLog.d('_waitUnityAndSetupWorkspace - 等待Unity初始化');
        // 等待Unity初始化完成
        await _unityController.isReady;
        PGLog.d('_waitUnityAndSetupWorkspace - Unity已初始化完成');
      }

      // 设置工作区和导入图片
      final setupWorkspaceChain =
          _projectHandlerChainFactory.createSetWorkspaceAndImportImagesChain();
      await setupWorkspaceChain.process(handlerContext);

      PGLog.d('_waitUnityAndSetupWorkspace - setupWorkspaceChain 完成');
    } catch (e) {
      PGLog.e('_waitUnityAndSetupWorkspace - setupWorkspaceChain出错: $e');
    }
  }
}
