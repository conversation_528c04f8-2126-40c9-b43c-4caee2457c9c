import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/coupon_repository.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/coupon/view_model/coupon_dialog_view_model.dart';
import 'package:turing_art/ui/coupon/widget/coupon_success_dialog.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/widgets/unity/helper/windows_unity_helper.dart';

/// 兑换码弹窗
class CouponDialog extends StatefulWidget {
  const CouponDialog({super.key});

  @override
  State<CouponDialog> createState() => _CouponDialogState();

  /// public方法：显示对话框(在unity里页调用此方法，因为商品页面不用收起，就不用另外挖洞)
  static Future<void> show() async {
    if (PGDialog.isDialogVisible(DialogTags.coupon)) {
      return;
    }
    PGLog.d('显示兑换码弹窗');
    // 设置焦点到Flutter窗口
    // Update: 目前已不需要在展示前设置焦点，因为当前弹窗会与购买页共存，此时无需进行焦点
    // 切换，切换了反而会导致更多问题
    // if (Platform.isWindows && GlobalVars.isWinEditScreenActive) {
    //   await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible();
    // }
    PGDialog.showCustomDialog(
      width: 400,
      height: 306,
      needBlur: false,
      tag: DialogTags.coupon,
      child: Builder(
        builder: (context) {
          return const CouponDialog();
        },
      ),
    );
  }
}

class _CouponDialogState extends State<CouponDialog> {
  Future<void> _handleSuccessDialog(String couponAmount, String expireDate,
      String lastDes, String? couponType, bool isForever) async {
    // dissmiss当前弹窗（兼容在unity窗口上必须等待，否则层级不正确）
    await PGDialog.dismiss(tag: DialogTags.coupon);

    // 重置焦点控制，允许焦点自动转移到Unity窗口
    // UPDATE: 目前无需在兑换成功后重置焦点
    // _resetFocusToUnity();

    // 购买商品页面不关闭，不用showOnUnity
    CouponSuccessDialog.show(couponAmount, expireDate, lastDes,
        isForever: isForever, couponType: couponType);
  }

  /// 重置焦点控制，允许焦点自动转移到Unity窗口
  void _resetFocusToUnity() async {
    if (Platform.isWindows && GlobalVars.isWinEditScreenActive) {
      await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
          flutterWindowFocus: false);
      // 然后明确设置焦点到Unity窗口
      await WindowsUnityHelper.setUnityVisibility(visible: true);
      PGLog.d('兑换码弹窗关闭后，重置焦点控制并设置焦点回Unity窗口');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CouponDialogViewModel>(
      create: (context) {
        final viewModel = CouponDialogViewModel(
          context.read<CouponRepository>(),
          context.read<AccountRepository>(),
        );

        // 设置兑换成功回调
        viewModel.onCouponRedeemSuccess = (coupon) {
          String expiredAt = '';
          String lastDes = '过期';
          bool isForever = false;
          // 如果过期时间大于9位，则认为是时间戳，否则认为是格式化后的时间
          if (coupon.expiredAt != null) {
            if (coupon.expiredAt!.length > 9) {
              expiredAt = DateTimeUtil.formatDateTime(
                coupon.expiredAt,
                format: DateFormat.chinese,
              );
            } else if (coupon.expiredAt! == '0') {
              expiredAt = '永久';
              lastDes = '有效';
              isForever = true;
            } else {
              PGLog.e('兑换码过期时间格式错误: ${coupon.expiredAt}');
            }
          }
          _handleSuccessDialog(
            coupon.amount.toString(),
            expiredAt,
            lastDes,
            coupon.type,
            isForever,
          );
        };

        return viewModel;
      },
      child: Builder(
        builder: (context) {
          return Container(
            width: 400,
            height: 306,
            decoration: BoxDecoration(
              color: const Color(0xFF121315),
              borderRadius: BorderRadius.circular(12),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x33000000),
                  blurRadius: 40,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Stack(
              clipBehavior: Clip.hardEdge,
              children: [
                // 背景logo
                Positioned(
                  top: 20,
                  left: 260,
                  child: Image.asset(
                    "assets/icons/redeem_pop_back_loge.png",
                    width: 192.5,
                    height: 385,
                  ),
                ),

                // 关闭按钮
                Positioned(
                  top: 16,
                  right: 16,
                  child: GestureDetector(
                    onTap: () {
                      PGDialog.dismiss(tag: DialogTags.coupon);
                      // 重置焦点控制，允许焦点自动转移到Unity窗口
                      // UPDATE: 目前无需在关闭时重置焦点
                      // _resetFocusToUnity();
                    },
                    child: Image.asset(
                      "assets/icons/home_window_close.png",
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),

                // "兑换码"标题
                const Positioned(
                  top: 56,
                  left: 176,
                  child: Text(
                    "兑换码",
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      height: 1.0,
                      color: Colors.white,
                    ),
                  ),
                ),

                // 输入框
                Positioned(
                  top: 118,
                  left: 24,
                  child: Consumer<CouponDialogViewModel>(
                    builder: (context, viewModel, _) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 352,
                            height: 44,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(0x0FFFFFFF),
                                width: 1,
                              ),
                            ),
                            child: TextField(
                              controller: viewModel.codeController,
                              textAlignVertical: TextAlignVertical.center,
                              expands: true,
                              maxLines: null,
                              decoration: InputDecoration(
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 10),
                                isCollapsed: true,
                                hintText: '请输入兑换代码',
                                hintStyle: TextStyle(
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontWeight: Fonts.regular,
                                  fontSize: 14,
                                  height: 1.0,
                                  color: const Color(0xA6EBEDF5),
                                ),
                                border: InputBorder.none,
                              ),
                              style: TextStyle(
                                fontFamily: Fonts.defaultFontFamily,
                                fontWeight: Fonts.regular,
                                fontSize: 14,
                                height: 1.0,
                                color: const Color(0xFFE1E2E5),
                              ),
                              onChanged: (_) => setState(() {}),
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(20),
                              ],
                            ),
                          ),
                          if (viewModel.errorMessage != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                viewModel.errorMessage!,
                                style: TextStyle(
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontWeight: Fonts.regular,
                                  fontSize: 10,
                                  height: 1.0,
                                  color: const Color(0xFFF55C44),
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ),

                // 确认按钮
                Positioned(
                  bottom: 56,
                  left: 24,
                  child: Consumer<CouponDialogViewModel>(
                    builder: (context, viewModel, _) {
                      final hasInput = viewModel.hasInput;
                      final isLoading = viewModel.isLoading;

                      return GestureDetector(
                        onTap: isLoading || !hasInput
                            ? null
                            : viewModel.applyCoupon,
                        child: Container(
                          width: 352,
                          height: 48,
                          decoration: BoxDecoration(
                            color: hasInput
                                ? const Color(0xFFF72561)
                                : const Color(0xFF1B1C1F),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignment: Alignment.center,
                          child: isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFFF71561),
                                  ),
                                )
                              : Text(
                                  '确认',
                                  style: TextStyle(
                                    fontFamily: Fonts.defaultFontFamily,
                                    fontWeight: Fonts.semiBold,
                                    fontSize: 14,
                                    height: 1.0,
                                    color: Colors.white,
                                  ),
                                ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
