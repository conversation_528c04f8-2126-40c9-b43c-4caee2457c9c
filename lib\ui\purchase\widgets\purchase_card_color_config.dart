import 'package:flutter/material.dart';

/// 精修套餐配置类
class RetouchCardConfig {
  final List<Color> backgroundColors;
  final List<double>? backgroundColorStops;
  final List<Color> buttonColors;
  final List<double>? buttonColorStops;

  const RetouchCardConfig({
    required this.backgroundColors,
    this.backgroundColorStops,
    required this.buttonColors,
    this.buttonColorStops,
  });
}

/// AI积分卡片配置类
class AigcCardConfig {
  final String backgroundImagePath;
  final List<Color> titleColor;
  final List<double>? titleColorStops;
  final List<Color> totalCountColor;
  final List<double>? totalCountColorStops;
  final List<Color> totalCountUnitColor;
  final List<double>? totalCountUnitColorStops;
  final List<Color> priceColor;
  final List<double>? priceColorStops;
  final double purchaseButtonBorderWidth;
  final List<Color> purchaseButtonBgColor;
  final List<double>? purchaseButtonBgColorStops;
  final List<Color> purchaseButtonTitleColor;
  final List<double>? purchaseButtonTitleColorStops;
  final List<Color>? purchaseButtonBorderColor;
  final List<double>? purchaseButtonBorderColorStops;

  const AigcCardConfig({
    required this.backgroundImagePath,
    required this.titleColor,
    this.titleColorStops,
    required this.totalCountColor,
    this.totalCountColorStops,
    required this.totalCountUnitColor,
    this.totalCountUnitColorStops,
    required this.priceColor,
    this.priceColorStops,
    required this.purchaseButtonBorderWidth,
    required this.purchaseButtonBgColor,
    this.purchaseButtonBgColorStops,
    required this.purchaseButtonTitleColor,
    this.purchaseButtonTitleColorStops,
    this.purchaseButtonBorderColor,
    this.purchaseButtonBorderColorStops,
  });
}

class PurchaseCardColorConfig {
  // 精修套餐四个档位的配置
  static const List<RetouchCardConfig> retouchConfigs = [
    // 第一种配色
    RetouchCardConfig(
      backgroundColors: [Color(0xFF341F25), Color(0xFF241519)],
      backgroundColorStops: [0.1956, 1.0],
      buttonColors: [Color(0xFFFBF1F4), Color(0xFFFBC9D7)],
    ),
    // 第二种配色
    RetouchCardConfig(
      backgroundColors: [Color(0xFF403934), Color(0xFF241E1B)],
      buttonColors: [Color(0xFFFBF5F1), Color(0xFFFBDEC9)],
    ),
    // 第三种配色
    RetouchCardConfig(
      backgroundColors: [Color(0xFF423B2B), Color(0xFF242119)],
      buttonColors: [Color(0xFFFBF8F1), Color(0xFFFBEAC9)],
    ),
    // 第四种配色
    RetouchCardConfig(
      backgroundColors: [Color(0xFF1F2634), Color(0xFF151A24)],
      backgroundColorStops: [0.1956, 1.0],
      buttonColors: [Color(0xFFF1F4FB), Color(0xFFC9DAFB)],
    ),
  ];

  // AI积分三个档位的配置
  static const List<AigcCardConfig> aigcConfigs = [
    // 低档 灰色lv1
    AigcCardConfig(
      backgroundImagePath: 'assets/icons/purchase_ai_low_bg.png',
      titleColor: [Color(0xFFD9D9D9)],
      totalCountColor: [Color(0xFFD9D9D9)],
      totalCountUnitColor: [Color(0xFFD9D9D9)],
      priceColor: [Color(0xFFD9D9D9)],
      purchaseButtonBorderWidth: 0,
      purchaseButtonBgColor: [Color(0xFF383838)],
      purchaseButtonTitleColor: [Color(0xFFD9D9D9)],
    ),
    // 中档 蓝色lv2
    AigcCardConfig(
      backgroundImagePath: 'assets/icons/purchase_ai_mid_bg.png',
      titleColor: [Color(0xFFC3E1FF)],
      totalCountColor: [Color(0xFFA9F8F3), Color(0xFF3D9EFF)],
      totalCountColorStops: [0.0, 1.0],
      totalCountUnitColor: [Color(0xFF3D9DFE)],
      priceColor: [Color(0xFFA9F8F3), Color(0xFF3D9EFF)],
      priceColorStops: [0.0, 1.0],
      purchaseButtonBorderWidth: 0,
      purchaseButtonBgColor: [Color(0xFF2A2B33), Color(0xFF294854)],
      purchaseButtonBgColorStops: [0.0, 1.0],
      purchaseButtonTitleColor: [Color(0xFFC3E1FF)],
    ),
    // 高档 渐变lv3
    AigcCardConfig(
      backgroundImagePath: 'assets/icons/purchase_ai_high_bg.png',
      titleColor: [Color(0xFFFFFFFF)],
      totalCountColor: [
        Color(0xFFFFBB33),
        Color(0xFFF7FFE6),
        Color(0xFF389BFF)
      ],
      totalCountColorStops: [0.0, 0.5, 1.0],
      totalCountUnitColor: [Color(0xFFFFFFFF)],
      priceColor: [Color(0xFFFFFFFF)],
      purchaseButtonBorderWidth: 1.5,
      purchaseButtonBgColor: [Color(0xFF2A2B33)],
      purchaseButtonTitleColor: [
        Color(0xFFFFBB33),
        Color(0xFFF7FFE6),
        Color(0xFF389BFF)
      ],
      purchaseButtonTitleColorStops: [0.0, 0.5, 1.0],
      purchaseButtonBorderColor: [
        Color(0xFFFFBB33),
        Color(0xFFF7FFE6),
        Color(0xFF389BFF)
      ],
      purchaseButtonBorderColorStops: [0.0, 0.5, 1.0],
    ),
  ];
}
