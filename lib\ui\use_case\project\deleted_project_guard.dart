import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 已删除项目清理守卫
/// 负责清理数据库中已标记为删除但文件仍存在的项目
class DeletedProjectGuard {
  final ProjectRepository _projectRepository;

  DeletedProjectGuard(this._projectRepository);

  /// 执行已删除项目清理
  /// 查找所有已标记删除的项目，并清理其文件
  Future<void> invoke() async {
    try {
      PGLog.d('开始执行已删除项目清理...');

      // 获取所有已标记删除的项目
      final deletedProjects = await _getAllDeletedProjects();

      if (deletedProjects.isEmpty) {
        PGLog.d('没有找到已删除的项目，清理完成');
        return;
      }

      PGLog.d('找到 ${deletedProjects.length} 个已删除的项目，开始清理文件...');

      // 提取项目ID列表
      final projectIds =
          deletedProjects.map((project) => project.uuid).toList();

      // 在后台清理项目文件
      _deleteProjectFilesInBackground(projectIds);

      PGLog.d('已删除项目清理任务已启动，共 ${projectIds.length} 个项目');
    } catch (e) {
      PGLog.e('执行已删除项目清理时发生异常: $e');
    }
  }

  /// 获取所有已删除的项目
  /// 从所有用户中收集已标记删除的项目
  Future<List<ProjectInfo>> _getAllDeletedProjects() async {
    try {
      // 这里需要根据实际情况调整，如果 ProjectRepository 没有提供获取所有已删除项目的方法
      // 可能需要遍历所有用户或者修改 repository 接口

      // 临时实现：假设可以获取所有已删除项目
      // 如果实际接口不支持，需要修改 ProjectRepository
      final deletedProjects = await _projectRepository.getDeletedProjects();

      return deletedProjects;
    } catch (e) {
      PGLog.e('获取已删除项目列表时发生异常: $e');
      return [];
    }
  }

  /// 在后台线程中删除项目文件
  void _deleteProjectFilesInBackground(List<String> projectIds) {
    Future.microtask(() async {
      try {
        // 等待所有文件删除完成
        await _deleteAllProjectFiles(projectIds);

        // 所有文件删除完成后，再进行数据库删除
        await _projectRepository.deleteProjects(projectIds);

        PGLog.d('已删除项目清理完成，共清理 ${projectIds.length} 个项目');
      } catch (e) {
        PGLog.e('后台文件删除过程中发生异常: $e');
      }
    });
  }

  /// 删除所有项目文件，等待所有删除操作完成
  Future<void> _deleteAllProjectFiles(List<String> projectIds) async {
    final futures = <Future<void>>[];

    for (final projectId in projectIds) {
      final future = _deleteSingleProjectFile(projectId);
      futures.add(future);
    }

    // 等待所有文件删除操作完成
    await Future.wait(futures);
  }

  /// 删除单个项目文件
  Future<void> _deleteSingleProjectFile(String projectId) async {
    try {
      await FileManager().deleteProjectDirectory(projectId);
      PGLog.d('项目目录删除成功: $projectId');
    } catch (e) {
      PGLog.e('项目目录删除失败: $projectId, 错误: $e');
    }
  }
}
