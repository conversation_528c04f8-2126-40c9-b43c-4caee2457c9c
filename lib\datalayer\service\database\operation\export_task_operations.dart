import 'package:turing_art/datalayer/service/database/dao/export_task_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/export_task_file_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'database_operation.dart';

/// 获取用户所有导出任务
class GetUserAllExportTasksOperation
    extends DatabaseOperation<List<ExportTaskEntityData>> {
  @override
  String get operation => 'getUserAllExportTasks';

  @override
  Future<List<ExportTaskEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getExportTasksByUserId(
      params['userId'],
    );
  }
}

class UpdateExportTaskOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateExportTask';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateExportTask(params['task']);
  }
}

class GetExportTaskOperation extends DatabaseOperation<ExportTaskEntityData?> {
  @override
  String get operation => 'getExportTask';

  @override
  Future<ExportTaskEntityData?> execute(
      DataBase db, Map<String, dynamic> params) {
    final guid = params['guid'] as String?;
    if (guid == null) {
      return Future.value(null);
    }
    return db.getExportTaskById(guid);
  }
}

class GetExportTasksOperation
    extends DatabaseOperation<List<ExportTaskEntityData>> {
  @override
  String get operation => 'getExportTasks';

  @override
  Future<List<ExportTaskEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    final guids = params['guids'] as List<String>?;
    if (guids == null) {
      return Future.value([]);
    }
    return db.getExportTasksByIds(guids);
  }
}

class DeleteExportTaskOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteExportTask';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteExportTask(params['guid']);
  }
}

class GetExportFilesOperation
    extends DatabaseOperation<List<ExportTaskFileEntityData>> {
  @override
  String get operation => 'getExportFiles';

  @override
  Future<List<ExportTaskFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getAllFilesWithExportTaskId(params['exportTaskId']);
  }
}
