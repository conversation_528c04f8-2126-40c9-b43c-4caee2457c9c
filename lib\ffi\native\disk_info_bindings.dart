import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

// FFI函数签名定义
typedef GetDiskSpaceNative = Bool Function(
  Pointer<Utf8> path,
  Pointer<Uint64> totalSpace,
  Pointer<Uint64> freeSpace,
);

typedef GetDiskSpaceDart = bool Function(
  Pointer<Utf8> path,
  Pointer<Uint64> totalSpace,
  Pointer<Uint64> freeSpace,
);

// get_disk_type_from_path
typedef GetDiskTypeFromPathNative = Bool Function(
  Pointer<Utf8> path,
  Pointer<Int32> type,
);

typedef GetDiskTypeFromPathDart = bool Function(
  Pointer<Utf8> path,
  Pointer<Int32> type,
);

// C struct: DiskInfo
final class C_DiskInfo extends Struct {
  external Pointer<Utf8> path;

  @Int32()
  external int type;
}

// C struct: DiskInfoList
final class C_DiskInfoList extends Struct {
  @Int32()
  external int count;

  external Pointer<C_DiskInfo> disks;
}

// get_all_disks_info
typedef GetAllDisksInfoNative = Pointer<C_DiskInfoList> Function();
typedef GetAllDisksInfoDart = Pointer<C_DiskInfoList> Function();

// free_disk_info
typedef FreeDiskInfoNative = Void Function(Pointer<C_DiskInfoList> infoList);
typedef FreeDiskInfoDart = void Function(Pointer<C_DiskInfoList> infoList);

/// DiskInfo FFI绑定类
/// 提供获取磁盘空间信息的native接口
class DiskInfoBindings {
  static DynamicLibrary? _library;
  static GetDiskSpaceDart? _getDiskSpace;
  static GetDiskTypeFromPathDart? _getDiskTypeFromPath;
  static GetAllDisksInfoDart? _getAllDisksInfo;
  static FreeDiskInfoDart? _freeDiskInfo;
  static bool _initialized = false;

  /// 初始化FFI绑定
  static bool initialize() {
    if (_initialized) return true;

    try {
      // 使用通用平台加载器加载库
      _library = UniversalPlatformLoader.loadLibrary(
        'PGDiskInfo',
        subDirectory: 'disk_info',
      );

      // 查找函数
      _getDiskSpace =
          _library!.lookupFunction<GetDiskSpaceNative, GetDiskSpaceDart>(
        'get_disk_space',
      );

      _getDiskTypeFromPath = _library!
          .lookupFunction<GetDiskTypeFromPathNative, GetDiskTypeFromPathDart>(
        'get_disk_type_from_path',
      );

      _getAllDisksInfo =
          _library!.lookupFunction<GetAllDisksInfoNative, GetAllDisksInfoDart>(
        'get_all_disks_info',
      );

      _freeDiskInfo =
          _library!.lookupFunction<FreeDiskInfoNative, FreeDiskInfoDart>(
        'free_disk_info',
      );

      _initialized = true;
      PGLog.i('DiskInfoBindings initialized successfully');
      return true;
    } catch (e) {
      PGLog.e('Failed to initialize DiskInfoBindings: $e');
      return false;
    }
  }

  /// 检查是否已初始化且可用
  static bool get isAvailable => _initialized && _getDiskSpace != null;

  /// 获取磁盘空间信息
  ///
  /// [path] 磁盘路径，例如 "C:\\" (Windows) 或 "/" (Linux/macOS)
  /// 返回 DiskSpaceInfo 对象，包含总空间和可用空间
  /// 失败时返回 null
  static DiskSpaceInfo? getDiskSpace(String path) {
    if (!isAvailable) {
      PGLog.w('DiskInfoBindings not initialized');
      return null;
    }

    final pathPtr = path.toNativeUtf8();
    final totalSpacePtr = calloc<Uint64>();
    final freeSpacePtr = calloc<Uint64>();

    try {
      final success = _getDiskSpace!(pathPtr, totalSpacePtr, freeSpacePtr);

      if (success) {
        final totalSpace = totalSpacePtr.value;
        final freeSpace = freeSpacePtr.value;

        return DiskSpaceInfo(
          totalSpace: totalSpace,
          freeSpace: freeSpace,
          usedSpace: totalSpace - freeSpace,
        );
      } else {
        PGLog.e('get_disk_space failed for path: $path');
        return null;
      }
    } catch (e) {
      PGLog.e('Error calling get_disk_space: $e');
      return null;
    } finally {
      // 释放内存
      calloc.free(pathPtr);
      calloc.free(totalSpacePtr);
      calloc.free(freeSpacePtr);
    }
  }

  /// 获取指定路径所在磁盘的类型（HDD/SSD/UNKNOWN）
  /// 成功返回对应的 [DiskType]，失败返回 null
  static DiskType? getDiskTypeFromPath(String path) {
    if (!_initialized || _getDiskTypeFromPath == null) {
      PGLog.w('get_disk_type_from_path not available');
      return null;
    }

    final pathPtr = path.toNativeUtf8();
    final typePtr = calloc<Int32>();
    try {
      final success = _getDiskTypeFromPath!(pathPtr, typePtr);
      if (!success) {
        PGLog.e('get_disk_type_from_path failed for path: $path');
        return null;
      }
      return _mapDiskType(typePtr.value);
    } catch (e) {
      PGLog.e('Error calling get_disk_type_from_path: $e');
      return null;
    } finally {
      calloc.free(pathPtr);
      calloc.free(typePtr);
    }
  }

  /// 获取系统中所有物理磁盘信息
  /// 成功返回 [List<DiskInfoEntry>]，失败或不支持返回 null
  static List<DiskInfoEntry>? getAllDisksInfo() {
    if (!_initialized || _getAllDisksInfo == null || _freeDiskInfo == null) {
      PGLog.w('get_all_disks_info not available');
      return null;
    }

    Pointer<C_DiskInfoList> listPtr;
    try {
      listPtr = _getAllDisksInfo!();
    } catch (e) {
      PGLog.e('Error calling get_all_disks_info: $e');
      return null;
    }

    if (listPtr == nullptr) {
      return null;
    }

    final count = listPtr.ref.count;
    final result = <DiskInfoEntry>[];
    try {
      final disksPtr = listPtr.ref.disks;
      for (var i = 0; i < count; i++) {
        final cDisk = (disksPtr + i).ref;
        final path = cDisk.path.toDartString();
        final type = _mapDiskType(cDisk.type);
        result.add(DiskInfoEntry(path: path, type: type));
      }
      return result;
    } catch (e) {
      PGLog.e('Error reading disks info: $e');
      return null;
    } finally {
      try {
        _freeDiskInfo!(listPtr);
      } catch (e) {
        PGLog.e('Error calling free_disk_info: $e');
      }
    }
  }

  static DiskType _mapDiskType(int value) {
    switch (value) {
      case 1:
        return DiskType.hdd;
      case 2:
        return DiskType.ssd;
      default:
        return DiskType.unknown;
    }
  }

  /// 清理资源
  static void dispose() {
    _library = null;
    _getDiskSpace = null;
    _getDiskTypeFromPath = null;
    _getAllDisksInfo = null;
    _freeDiskInfo = null;
    _initialized = false;
  }
}

/// 磁盘空间信息数据类
class DiskSpaceInfo {
  /// 总空间（字节）
  final int totalSpace;

  /// 可用空间（字节）
  final int freeSpace;

  /// 已使用空间（字节）
  final int usedSpace;

  const DiskSpaceInfo({
    required this.totalSpace,
    required this.freeSpace,
    required this.usedSpace,
  });

  /// 可用空间百分比 (0.0 - 1.0)
  double get freeSpacePercentage =>
      totalSpace > 0 ? freeSpace / totalSpace : 0.0;

  /// 已使用空间百分比 (0.0 - 1.0)
  double get usedSpacePercentage =>
      totalSpace > 0 ? usedSpace / totalSpace : 0.0;

  /// 格式化显示总空间
  String get formattedTotalSpace => _formatBytes(totalSpace);

  /// 格式化显示可用空间
  String get formattedFreeSpace => _formatBytes(freeSpace);

  /// 格式化显示已使用空间
  String get formattedUsedSpace => _formatBytes(usedSpace);

  /// 将字节数格式化为人类可读的字符串
  static String _formatBytes(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var value = bytes.toDouble();
    var suffixIndex = 0;

    while (value >= 1024 && suffixIndex < suffixes.length - 1) {
      value /= 1024;
      suffixIndex++;
    }

    return '${value.toStringAsFixed(suffixIndex == 0 ? 0 : 2)} ${suffixes[suffixIndex]}';
  }

  @override
  String toString() {
    return 'DiskSpaceInfo(total: $formattedTotalSpace, free: $formattedFreeSpace, used: $formattedUsedSpace)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DiskSpaceInfo &&
        other.totalSpace == totalSpace &&
        other.freeSpace == freeSpace &&
        other.usedSpace == usedSpace;
  }

  @override
  int get hashCode => Object.hash(totalSpace, freeSpace, usedSpace);
}

/// 磁盘类型
enum DiskType {
  unknown(0),
  hdd(1),
  ssd(2);

  final int value;

  const DiskType(this.value);
}

/// 磁盘信息条目
class DiskInfoEntry {
  final String path;
  final DiskType type;

  const DiskInfoEntry({
    required this.path,
    required this.type,
  });

  @override
  String toString() => 'DiskInfoEntry(path: $path, type: $type)';
}
