import 'package:flutter/material.dart';
import 'package:turing_art/utils/file_manager.dart';

class ExportPathSettingProvider extends ChangeNotifier {
  bool _isValidExportPath = true;
  String _exportPath = "";

  bool get isValidExportPath => _isValidExportPath;
  String get exportPath => _exportPath;

  void setExportPath({required String path}) {
    _exportPath = path;
    // 当路径改变时，重置验证状态为正常
    if (_isValidExportPath != true) {
      _isValidExportPath = true;
      notifyListeners();
    }
  }

  bool _isValidSuffix(String value) {
    // 检查是否包含特殊符号：/ \ : * " < > |
    final invalidChars = RegExp(r'[/\\:*?"<>|]');
    return !invalidChars.hasMatch(value);
  }

  Future<bool> validateExportPath() async {
    try {
      if (!_isValidSuffix(_exportPath)) {
        _isValidExportPath = false;
        return false;
      }

      final result = await FileManager().testCreateTempFolder(exportPath);

      _isValidExportPath = _exportPath.trim().isEmpty || result;
      notifyListeners();

      return _isValidExportPath;
    } catch (e) {
      _isValidExportPath = false;
      notifyListeners();
      return false;
    }
  }
}
