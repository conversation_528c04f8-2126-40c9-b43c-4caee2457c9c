import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/executor/database_executor.dart';
import 'package:turing_art/datalayer/service/database/operation/export_task_operations.dart';

import 'db_operater.dart';

extension ExportTaskDbOperater on DbOperater {
  Future<List<ExportTaskEntityData>> getUserAllExportTasks(
    String userId,
  ) async {
    return DatabaseExecutor.execute<List<ExportTaskEntityData>>(
      GetUserAllExportTasksOperation().operation,
      {'userId': userId, 'isDeleted': false},
    );
  }

  Future<void> updateExportTask(ExportTaskEntityData exportTask) async {
    return DatabaseExecutor.execute<void>(
      UpdateExportTaskOperation().operation,
      {'task': exportTask},
    );
  }

  Future<ExportTaskEntityData?> fetchExportTask(String guid) async {
    return DatabaseExecutor.execute<ExportTaskEntityData?>(
      GetExportTaskOperation().operation,
      {'guid': guid},
    );
  }

  Future<List<ExportTaskEntityData>> fetchExportTasks(
      List<String> guids) async {
    return DatabaseExecutor.execute<List<ExportTaskEntityData>>(
      GetExportTasksOperation().operation,
      {'guids': guids},
    );
  }

  Future<void> deleteExportTask(String guid) async {
    return DatabaseExecutor.execute<void>(
      DeleteExportTaskOperation().operation,
      {'guid': guid},
    );
  }

  Future<List<ExportTaskFileEntityData>> fetchExportTaskFiles(
    String exportTaskId,
  ) async {
    return DatabaseExecutor.execute<List<ExportTaskFileEntityData>>(
      GetExportFilesOperation().operation,
      {'exportTaskId': exportTaskId},
    );
  }
}
