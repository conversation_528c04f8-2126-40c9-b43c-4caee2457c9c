import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_key.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_manager_mixin.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_thumbnail_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_icon_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_action_popup.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_title.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_controller.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_overlay.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缩略图列表组件V2 - 遵循MVVM架构，职责完全分离
/// UI层只关心ViewModel，不知道SharedDataProvider的存在
class AigcThumbnailListWidget extends StatefulWidget {
  final double? width;

  /// 背景颜色
  final double? height;

  /// 滚动物理效果
  final ScrollPhysics? scrollPhysics;

  /// 缩略图大小
  final double thumbnailSize;

  /// 缩略图间距
  final double? thumbnailSpacing;

  /// 内边距
  final EdgeInsets? padding;

  /// 是否启用框选
  final bool enableBoxSelection;

  const AigcThumbnailListWidget({
    super.key,
    this.width,
    this.height,
    this.scrollPhysics = const BouncingScrollPhysics(),
    this.thumbnailSize = 64,
    this.thumbnailSpacing,
    this.padding,
    this.enableBoxSelection = true,
  });

  @override
  State<AigcThumbnailListWidget> createState() =>
      _AigcThumbnailListWidgetState();
}

class _AigcThumbnailListWidgetState extends State<AigcThumbnailListWidget>
    with ShortcutManagerMixin {
  late ScrollController _scrollController;
  Timer? _scrollDebounceTimer;
  late BoxSelectionController _boxSelectionController;
  final GlobalKey _listKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _registerShortcuts();

    // 初始化框选控制器
    _boxSelectionController = BoxSelectionController(
      onEnterMultiSelectMode: () {
        final viewModel = context.read<AigcThumbnailListViewModel>();
        viewModel.enterMultiSelectMode();
      },
      onSelectionChanged: (selectedIndices, selectedIndex) {
        final viewModel = context.read<AigcThumbnailListViewModel>();
        viewModel.batchSelectImages(selectedIndices.toList());
        if (selectedIndex != -1) {
          viewModel.selectImage(selectedIndex);
        }
      },
      onSelectionEnded: () {
        final viewModel = context.read<AigcThumbnailListViewModel>();
        // 如果框选结束时没有选中任何项目，则清空选择
        if (_boxSelectionController.state.selectedIndices.isEmpty) {
          viewModel.clearBoxSelection();
        }
      },
    );

    _scrollController.addListener(() {
      _boxSelectionController.updateScrollOffset(_scrollController.offset);
    });
  }

  void _registerShortcuts() {
    _registerDeleteShortcut('delete', ShortcutKey.delete);
    _registerDeleteShortcut('backspace', ShortcutKey.backspace);

    registerShortcut(
        id: 'left_arrow',
        shortcut: ShortcutKey.leftArrow,
        handler: () => _handleAdjacentImageSelection(isNextImage: false),
        description: '左箭头',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });

    registerShortcut(
        id: 'right_arrow',
        shortcut: ShortcutKey.rightArrow,
        handler: () => _handleAdjacentImageSelection(isNextImage: true),
        description: '右箭头',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });

    registerShortcut(
        id: 'select_all',
        shortcut: ShortcutKey.selectAll,
        handler: () {
          context.read<AigcThumbnailListViewModel>().selectAllImages();
          return KeyEventHandleResult.handled;
        },
        description: '全选',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });
  }

  void _registerDeleteShortcut(String id, ShortcutKey key) {
    registerShortcut(
        id: id,
        shortcut: key,
        handler: () {
          PGLog.d('删除键按下，准备显示删除确认弹窗');
          context
              .read<AigcThumbnailListViewModel>()
              .requestDeleteConfirmation();
          return KeyEventHandleResult.handled;
        },
        description: '删除',
        additionalCheck: () {
          return !PGDialog.isDialogVisible(null);
        });
  }

  KeyEventHandleResult _handleAdjacentImageSelection(
      {required bool isNextImage}) {
    final viewModel = context.read<AigcThumbnailListViewModel>();
    viewModel.selectAdjacentImage(isNextImage: isNextImage);

    // 滚动到选中的图片位置
    if (viewModel.selectedIndex >= 0) {
      _scrollToVisible(viewModel.selectedIndex);
    }
    return KeyEventHandleResult.handled;
  }

  @override
  void dispose() {
    _scrollDebounceTimer?.cancel();
    _scrollController.dispose();
    _boxSelectionController.dispose();
    super.dispose();
  }

  /// 滚动到指定索引位置使其可见，并居中显示
  void _scrollToVisible(int index) {
    if (!_scrollController.hasClients ||
        !_scrollController.position.hasContentDimensions) {
      return;
    }

    // 计算单个缩略图的宽度（包括间距）
    final itemWidth = widget.thumbnailSize + (widget.thumbnailSpacing ?? 2.0);
    final screenWidth = MediaQuery.of(context).size.width;
    final listPadding = widget.padding?.left ?? 8.0;

    // 计算目标位置，使选中的图片居中显示
    final targetOffset =
        (index * itemWidth) + listPadding - (screenWidth / 2 - itemWidth / 2);

    // 确保滚动位置在有效范围内
    final clampedOffset =
        targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent);

    _scrollController.animateTo(
      clampedOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildScene();
  }

  Widget _buildScene() {
    return Consumer<AigcThumbnailListViewModel>(
      builder: (context, viewModel, child) {
        // 监听删除确认弹窗状态
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (viewModel.showDeleteConfirmDialog) {
            _showDeleteConfirmDialog(viewModel);
          }
        });

        return Column(
          children: [
            Container(
                color: const Color(0xFFFFFFFF).withOpacity(0.1), height: 1),
            // 标题
            AigcThumbnailTitle(onImageSelection: (type) {
              viewModel.handleFileSelection(type).then((result) {
                if (result.isNotEmpty) {
                  PGDialog.showToast('本次共导入${result.length}张照片');
                }
              });
            }),
            Expanded(
              child: _buildScrollableList(viewModel),
            )
          ],
        );
      },
    );
  }

  /// 构建可滚动列表
  Widget _buildScrollableList(AigcThumbnailListViewModel viewModel) {
    return ScrollbarTheme(
      data: ScrollbarThemeData(
        // 滚动条拖拽区域的颜色 - 20%透明度的白色
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.hovered)) {
            return const Color(0x40FFFFFF); // 悬停时稍微更明显一些 (25%透明度)
          }
          if (states.contains(MaterialState.dragged)) {
            return const Color(0x4DFFFFFF); // 拖拽时更明显 (30%透明度)
          }
          return const Color(0x33FFFFFF); // 默认20%透明度的白色
        }),
        trackColor: MaterialStateProperty.all(const Color(0x0DFFFFFF)),
        trackBorderColor: MaterialStateProperty.all(Colors.transparent),
        thickness: MaterialStateProperty.all(4.0),
        radius: const Radius.circular(2),
        // 滚动条与内容的间距
        crossAxisMargin: 8.0,
        // 滚动条的主轴边距，使滚动条与内容区域对齐
        mainAxisMargin: 8.0,
      ),
      child: Scrollbar(
        controller: _scrollController,
        thumbVisibility: false, // 不始终显示，只在需要时显示
        trackVisibility: false,
        child: Listener(
          onPointerSignal: (pointerSignal) {
            if (pointerSignal is PointerScrollEvent &&
                _scrollController.hasClients) {
              // 处理鼠标滚轮事件，将垂直滚动转换为水平滚动
              final offset = _scrollController.offset;
              final delta = pointerSignal.scrollDelta.dy;

              // 滚动灵敏度调整 - 根据缩略图大小调整
              final scrollSensitivity = widget.thumbnailSize * 0.5;
              final newOffset = offset + (delta * scrollSensitivity / 120);

              // 确保滚动控制器有客户端连接后再滚动
              if (_scrollController.position.maxScrollExtent > 0) {
                _scrollController.jumpTo(
                  newOffset.clamp(
                      0.0, _scrollController.position.maxScrollExtent),
                );
              }
            }
          },
          child: ScrollConfiguration(
            behavior: ScrollConfiguration.of(context).copyWith(
              scrollbars: false, // 禁用默认滚动条，使用自定义滚动条
            ),
            child: widget.enableBoxSelection
                ? GestureDetector(
                    onPanStart: (details) {
                      _boxSelectionController.handlePanStart(details);
                    },
                    onPanUpdate: (details) {
                      _boxSelectionController.handlePanUpdate(details);
                      // 实时更新框选状态
                      _updateBoxSelection(viewModel);
                    },
                    onPanEnd: (details) {
                      _boxSelectionController.handlePanEnd(details);
                    },
                    child: BoxSelectionContainer(
                        state: _boxSelectionController.state,
                        child: _generateThumbnailListBuilder(viewModel)),
                  )
                : _generateThumbnailListBuilder(viewModel),
          ),
        ),
      ),
    );
  }

  Widget _generateThumbnailListBuilder(AigcThumbnailListViewModel viewModel) {
    return ListView.builder(
      key: _listKey,
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      physics: widget.scrollPhysics,
      padding: const EdgeInsets.only(left: 8, right: 8),
      itemCount: viewModel.images.length,
      itemBuilder: (context, index) => _buildThumbnailItem(viewModel, index),
    );
  }

  /// 更新框选状态
  void _updateBoxSelection(AigcThumbnailListViewModel viewModel) {
    if (!widget.enableBoxSelection) {
      return;
    }

    final listRenderBox =
        _listKey.currentContext?.findRenderObject() as RenderBox?;

    _boxSelectionController.updateSelection(
      listRenderBox: listRenderBox,
      itemCount: viewModel.images.length,
      itemWidth: widget.thumbnailSize,
      itemHeight: widget.thumbnailSize,
      itemSpacing: widget.thumbnailSpacing ?? 2.0,
      listPadding: widget.padding?.left ?? 8.0,
    );
  }

  /// 构建单个缩略图项
  Widget _buildThumbnailItem(AigcThumbnailListViewModel viewModel, int index) {
    if (index >= viewModel.images.length) {
      return const SizedBox();
    }

    final imageData = viewModel.images[index];
    final isSelected = index == viewModel.selectedIndex;
    final isInMultiSelection = viewModel.selectedImages
        .where((element) => element.fileId == imageData.fileId)
        .isNotEmpty;

    return Container(
        margin: EdgeInsets.only(left: index == 0 ? 0 : 2),
        alignment: Alignment.topCenter,
        child: Consumer<AigcMaskAcquisitionProvider>(
          builder: (context, maskProvider, child) {
            final maskAcquisitionStatus =
                maskProvider.getMaskAcquisitionStatus(imageData.fileId);

            return GestureDetector(
              onSecondaryTapDown: (details) {
                // 右键点击时，先选中当前图片（如果未选中）
                if (!isSelected && !isInMultiSelection) {
                  viewModel.selectImage(index, isControlPressed: false);
                }

                // 显示删除弹窗
                _showDeletePopup(context, details.globalPosition, viewModel);
              },
              child: AigcThumbnailWidget(
                index: index,
                imageData: imageData,
                isSelected: isSelected,
                isInMultiSelection: isInMultiSelection,
                isMultiSelectMode: viewModel.isMultiSelectMode,
                maskAcquisitionStatus: maskAcquisitionStatus,
                onTap: ({required bool isControlPressed}) {
                  viewModel.selectImage(index,
                      isControlPressed: isControlPressed);
                  AigcEditIconToast.dismissIconToast();
                },
                onThumbnailRequest: (index) =>
                    viewModel.requestImageTasks(index),
                onThumbnailRelease: (index) =>
                    viewModel.releaseImageTasks(index),
              ),
            );
          },
        ));
  }

  /// 显示删除确认弹窗
  void _showDeleteConfirmDialog(AigcThumbnailListViewModel viewModel) {
    if (PGDialog.isDialogVisible(DialogTags.aigcPcDeleteConfirm)) {
      return;
    }

    // 根据多选模式显示不同的提示文本
    String title;
    String content;

    if (viewModel.isMultiSelectMode && viewModel.selectedImages.isNotEmpty) {
      final selectedCount = viewModel.selectedImages.length;
      title = '确认删除选中的$selectedCount张图片吗？';
    } else {
      title = '确认删除此文件吗？';
    }
    content = '文件将仅从图灵精修中删除，磁盘中的原始文件会保留。';

    AigcPcDeleteConfirmDialog.show(
      context,
      title: title,
      content: content,
      onCancel: () {
        viewModel.hideDeleteConfirmation();
      },
      onConfirm: () async {
        await viewModel.confirmDelete();
        PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
      },
    );
  }

  /// 显示删除弹窗
  void _showDeletePopup(BuildContext context, Offset globalPosition,
      AigcThumbnailListViewModel viewModel) {
    AigcThumbnailActionPopup.show(
      context: context,
      position: globalPosition,
      onDelete: () {
        // 点击删除选项后，执行与删除快捷键相同的操作
        viewModel.requestDeleteConfirmation();
      },
    );
  }
}
