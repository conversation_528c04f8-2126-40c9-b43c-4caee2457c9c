import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/employee/view_model/employee_dialog_view_model.dart';
import 'package:turing_art/ui/employee/widget/employee_account_list.dart';
import 'package:turing_art/ui/employee/widget/employee_category_labels.dart';
import 'package:turing_art/ui/employee/widget/employee_empty_state.dart';
import 'package:turing_art/ui/employee/widget/employee_header.dart';
import 'package:turing_art/ui/employee/widget/employee_info_cards.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 账号管理弹窗
class EmployeeDialog extends StatelessWidget {
  const EmployeeDialog({super.key});

  /// 显示账号管理弹窗
  static void show() {
    if (PGDialog.isDialogVisible(DialogTags.employee)) {
      PGLog.d('账号管理弹窗已经存在，不重复显示');
      return;
    }

    PGLog.d('显示账号管理弹窗');
    PGDialog.showCustomDialog(
      width: 1142,
      height: 700,
      needBlur: false,
      tag: DialogTags.employee,
      child: Builder(
        builder: (context) {
          return const EmployeeDialog();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<EmployeeDialogViewModel>(
      create: (context) => EmployeeDialogViewModel(
        context.read<EmployeeRepository>(),
        context.read<AccountRepository>(),
        context.read<CurrentUserRepository>(),
      ),
      child: Container(
        width: 1142,
        height: 700,
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1F),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                // 标题栏 - 不需要重建
                _buildTitleBar(),

                // 卡片区域 - 需要随ViewModel变化而重建
                Consumer<EmployeeDialogViewModel>(
                  builder: (context, viewModel, _) {
                    return EmployeeInfoCards(
                      summary: viewModel.employeeSummary,
                      employeeList: viewModel.employeeList,
                    );
                  },
                ),

                // 关联账户和添加子账号按钮 - 不需要重建
                const EmployeeHeader(),

                // 分类标签行 - 不需要重建
                const EmployeeCategoryLabels(),

                // 账号列表 - 已在内部处理ViewModel变化
                Expanded(
                  child: Builder(
                    builder: (context) {
                      final viewModel =
                          Provider.of<EmployeeDialogViewModel>(context);
                      return viewModel.isEmpty
                          ? const EmployeeEmptyState()
                          : const EmployeeAccountList();
                    },
                  ),
                ),
              ],
            ),

            // 关闭按钮 - 不需要重建
            Positioned(
              top: 12,
              right: 12,
              child: GestureDetector(
                onTap: () => PGDialog.dismiss(tag: DialogTags.employee),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建标题栏
  Widget _buildTitleBar() {
    return SizedBox(
      height: 46,
      child: Center(
        child: Text(
          "账号管理",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.medium,
            fontSize: 16,
            color: const Color(0xFFE1E2E5),
          ),
        ),
      ),
    );
  }
}
