import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/recharge_record_repository.dart';
import 'package:turing_art/ui/common/data_list/data_list_widget.dart';
import 'package:turing_art/ui/common/date_range/input_date/widget/input_date_range_widget.dart';
import 'package:turing_art/ui/common/pagination/widgets/pagination_widget.dart';
import 'package:turing_art/ui/common/pull_down_list/pull_down_list_widget.dart';
import 'package:turing_art/ui/common/title_bar_with_help_tip/title_bar_with_help_bubble_widget.dart';
import 'package:turing_art/ui/recharge_record/viewmodel/recharge_record_view_model.dart';
import 'package:turing_art/ui/recharge_record/widgets/recharge_record_header_widget.dart';
import 'package:turing_art/ui/recharge_record/widgets/recharge_record_item_widget.dart';
import 'package:turing_art/ui/recharge_record/widgets/remaining_amount_widget.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class RechargeRecordDialog extends StatefulWidget {
  const RechargeRecordDialog({super.key});

  /// 显示充值记录弹窗
  static void show() {
    if (PGDialog.isDialogVisible(DialogTags.rechargeRecord)) {
      PGLog.d('RechargeRecordDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 1060,
      height: 680,
      needBlur: false,
      tag: DialogTags.rechargeRecord,
      child: const RechargeRecordDialog(),
    );
  }

  @override
  State<RechargeRecordDialog> createState() => _RechargeRecordDialogState();
}

class _RechargeRecordDialogState extends State<RechargeRecordDialog> {
  // 分页组件Key
  final PaginationWidgetKey _paginationKey =
      const PaginationWidgetKey('rechargeRecordPagination');
  // 用于强制重建日期控件的key
  Key _dateRangeWidgetKey = UniqueKey();
  // 保存ViewModel的引用
  late RechargeRecordViewModel _viewModel;

  // 动态生成的下拉列表选项
  List<(String, RechargeRecordType)> _detailTypeNames = [
    ('精修导出', RechargeRecordType.exportCount),
  ];
  String _showAIGCDetailListName = '精修导出'; // 当前选择的详情类型名称

  @override
  void initState() {
    super.initState();
    _initializeDetailTypes();
  }

  // 初始化下拉列表选项
  Future<void> _initializeDetailTypes() async {
    final aigcEntranceManager = context.read<AigcEntranceManager>();
    final isAigcUser = await aigcEntranceManager.isAigcUser();

    setState(() {
      _detailTypeNames = [
        ('精修导出', RechargeRecordType.exportCount),
      ];

      // 只有白名单用户才显示"AI场景增强"选项
      if (isAigcUser) {
        _detailTypeNames.add(('AI场景增强', RechargeRecordType.aigcCount));
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        // 创建ViewModel并保存引用
        _viewModel = RechargeRecordViewModel(
          context.read<RechargeRecordRepository>(),
          context.read<AccountRepository>(),
        );
        return _viewModel;
      },
      child: Container(
        width: 1060,
        height: 680,
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1F),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 0.5,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Column(
            children: [
              // 标题栏 - 使用通用组件
              const TitleBarWithHelpBubbleWidget(
                title: '充值记录',
                tooltipTitle: '充值记录：',
                tooltipContentItems: [
                  '1.可查询套餐充值&兑换信息',
                  '2.兑换码兑换的张数可能会存在过期注意识别',
                  '3.导出时优先扣除兑换码张数',
                ],
                dialogTag: DialogTags.rechargeRecord,
              ),

              // 日期选择和剩余张数
              _buildDateSelectAndAccountArea(),

              // 列表头
              RechargeRecordHeaderWidget(
                isAIGCType: _showAIGCDetailListName == 'AI场景增强',
              ),

              // 列表内容
              Expanded(
                child: _buildListContent(),
              ),

              // 分页
              _buildPaginationArea(),
            ],
          ),
        ),
      ),
    );
  }

  // 构建日期选择和剩余张数区域
  Widget _buildDateSelectAndAccountArea() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 32,
        right: 32,
        top: 16,
        bottom: 16,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 下拉列表控件
          PullDownListWidget(
            width: 160,
            height: 40,
            items: _detailTypeNames.map((e) => e.$1).toList(),
            selectedItem: _showAIGCDetailListName,
            collapsedTextColor: const Color(0xFFEBEDF5).withOpacity(0.65),
            collapsedTextSize: 12,
            listTextColor: const Color(0xFFEBEDF5).withOpacity(0.65),
            listTextSize: 14,
            listTextHoverColor: const Color(0xFFEBEDF5),
            onItemSelected: (item, index) {
              _tapBankDetailType(item, _detailTypeNames[index].$2);
            },
          ),

          const SizedBox(width: 12),
          // 日期输入+选择 - 不需要监听RechargeRecordViewModel变化
          InputDateRangeWidget(
            key: _dateRangeWidgetKey,
            onDateRangeChanged: (startDate, endDate) async {
              // 是否和上次筛选时间相同，不同的情况totalPage可能没有变化，但currentPage需要重置
              final isTimeChanged =
                  await _viewModel.setDataTime(startDate, endDate);
              // 同步当前页码
              if (isTimeChanged) {
                _syncCurrentPage(_viewModel.currentPage);
              }
            },
          ),

          const Spacer(),

          // 剩余张数/总张数 - 只有Account信息变化才刷新此控件
          Selector<RechargeRecordViewModel, (String, String)>(
            selector: (_, vm) => (
              vm.getTotalRemaining(vm.bankType),
              vm.getTotalAmount(vm.bankType)
            ),
            builder: (context, data, _) {
              return RemainingAmountWidget(
                totalRemaining: data.$1,
                totalAmount: data.$2,
                isAIGCType: _showAIGCDetailListName == 'AI场景增强',
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建列表内容
  Widget _buildListContent() {
    return Consumer<RechargeRecordViewModel>(
      builder: (context, viewModel, _) {
        // 显示错误信息
        if (viewModel.hasError) {
          // 使用后置方法在构建完成后处理副作用
          WidgetsBinding.instance.addPostFrameCallback((_) {
            viewModel.showErrorToast();
          });
        }

        return DataListWidget(
          isLoading: viewModel.isLoading,
          items: viewModel.rechargeRecordModel?.items ?? [],
          height: 464,
          padding: const EdgeInsets.only(left: 16, right: 16),
          itemBuilder: (context, item, index) {
            return RechargeRecordItemWidget(item: item);
          },
        );
      },
    );
  }

  // 构建分页区域
  Widget _buildPaginationArea() {
    return Selector<RechargeRecordViewModel, int>(
      // 只监听totalPage的变化，选中页面，是否跳转折中页面都是PaginationViewModel控制PaginationWidget刷新
      selector: (_, viewModel) =>
          viewModel.rechargeRecordModel?.pagination.totalPage ?? 1,
      builder: (context, totalPage, _) {
        return PaginationWidget(
          key: _paginationKey,
          initialPage: 1,
          initialTotalPage: totalPage,
          onPageChanged: _viewModel.fetchRechargeRecords,
        );
      },
    );
  }

  // 同步当前页码，用于时间筛选后重置page(是否改变页面是否同步是RechargeRecordViewModel中逻辑决定，单一职责原则；也避免PaginationWidget中重复构建；分离主动和被动页码变更)
  void _syncCurrentPage(int currentPage) {
    _paginationKey.currentState?.updateCurrentPage(currentPage);
  }

  Future<void> _tapBankDetailType(
      String name, RechargeRecordType bankType) async {
    // 检查分类是否真正改变
    if (_showAIGCDetailListName == name) {
      // 分类没有改变，不执行任何操作
      return;
    }

    setState(() {
      _showAIGCDetailListName = name;
      // 重新生成key以强制重建日期控件，从而清空日期数据
      _dateRangeWidgetKey = UniqueKey();
    });
    await _viewModel.setBankType(bankType);
  }
}
