import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_background_strategy_view_model.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 背景绘制器 - 用于绘制纯色背景或透明背景图案
class AigcBackgroundPainter extends CustomPainter {
  /// 视图模型
  final AigcBackgroundStrategyViewModel _viewModel;

  /// 是否可见
  bool _isVisible = false;

  AigcBackgroundPainter(Color color,
      {AigcEditingControlProvider? controlProvider})
      : _viewModel = AigcBackgroundStrategyViewModel(color,
            controlProvider: controlProvider);

  @override
  void paint(Canvas canvas, Size size) {
    // 如果不可见，则不绘制任何内容
    if (!_isVisible) {
      return;
    }

    // 使用背景策略绘制背景，不再传递缩放比例
    _viewModel.backgroundStrategy.drawBackground(canvas, size);
  }

  /// 设置背景颜色
  void setColor(Color color) {
    _viewModel.setBackgroundColor(color);
  }

  /// 设置可见性
  void setVisible({required bool visible}) {
    _isVisible = visible;
  }

  /// 获取当前可见性状态
  bool get isVisible => _isVisible;

  /// 更新显示模式
  void updateDisplayMode() {
    _viewModel.updateDisplayMode();
  }

  @override
  bool shouldRepaint(covariant AigcBackgroundPainter oldDelegate) {
    // 检查可见性或背景策略是否发生变化
    final visibilityChanged = _isVisible != oldDelegate._isVisible;
    final strategyChanged = _viewModel.hasBackgroundStrategyChanged();

    if (visibilityChanged) {
      PGLog.d('BackgroundPainter.shouldRepaint: 可见性变化，需要重绘');
    } else if (strategyChanged) {
      PGLog.d('BackgroundPainter.shouldRepaint: 背景策略变化，需要重绘');
    }

    return visibilityChanged || strategyChanged;
  }
}
