import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/dialog/core/alert_dialog.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 重新链接文件弹窗
/// 由AlertDialog组件实现UI
class AigcReconnectFileDialog {
  static const _tag = "AIGCReconnectFileDialog";

  /// 展示弹窗
  /// [onConfirm] 确认回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  /// [onCancel] 取消回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  static void show({
    required String fileName,
    Function? onConfirm,
    Function? onCancel,
  }) {
    SmartDialog.show(
      maskColor: const Color(0x99000000),
      animationType: SmartAnimationType.fade,
      clickMaskDismiss: false, // 禁止点击外部区域关闭弹窗，防止出现看起来卡死的问题
      builder: (context) => AlertDialog(
        title: "资源异常",
        content: "$fileName 不存在\n，您是否移动了文件或者修改了文件名称？",
        confirmText: "重新链接",
        cancelText: "忽略",
        onConfirm: onConfirm ?? hide,
        onCancel: onCancel ?? hide,
      ),
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (controller, child, param) =>
          AnimatedDialog(controller: controller, child: child),
      tag: _tag,
    );
  }

  /// 隐藏弹窗
  static Future<void> hide() async {
    await PGDialog.dismiss(tag: _tag);
  }
}
