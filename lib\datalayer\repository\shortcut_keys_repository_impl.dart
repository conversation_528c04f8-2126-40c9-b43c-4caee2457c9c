import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:turing_art/datalayer/domain/models/shortcut_keys/shortcut_keys.dart';

import 'shortcut_keys_repository.dart';

class ShortcutKeysRepositoryImpl implements ShortcutKeysRepository {
  List<ShortcutCategory>? _cachedCategories;

  @override
  Future<List<ShortcutCategory>> getShortcutCategories() async {
    // 如果有缓存，直接返回缓存数据
    if (_cachedCategories != null) {
      return _cachedCategories!;
    }

    try {
      final jsonFileName =
          Platform.isMacOS ? 'mac_shortcut_keys.json' : 'shortcut_keys.json';
      final jsonPath = 'assets/static/$jsonFileName';
      final String jsonString = await rootBundle.loadString(jsonPath);
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      final List<dynamic> categories = jsonMap['categories'] ?? [jsonMap];

      // 解析数据并缓存
      _cachedCategories = categories
          .map((category) => ShortcutCategory.fromJson(category))
          .toList();

      return _cachedCategories!;
    } catch (e) {
      throw Exception('Failed to load shortcut keys: $e');
    }
  }
}
