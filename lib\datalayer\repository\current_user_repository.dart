import 'package:turing_art/datalayer/domain/enums/user_info_change_event_type.dart';
import 'package:turing_art/datalayer/domain/models/store/store.dart';
import 'package:turing_art/datalayer/domain/models/user/creator.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';

/// 用户能力
enum UserCapability {
  // 小样导出
  exportSample("exportSample"),
  // 旅拍
  travel("travel");

  final String capabilityName;

  const UserCapability(this.capabilityName);

  // 检查用户是否拥有某个能力
  bool hasCapability(List<String> capabilities) {
    return capabilities.contains(capabilityName);
  }
}

abstract class CurrentUserRepository {
  User? get user;

  Store? get store;

  // 用于记录主账号信息
  Creator? get creator;

  bool get isLoggedIn;

  // 当前用户信息需要动态变更事件流
  Stream<UserInfoChangeEventType> get currentUserInfoChange;

  // 获取当前登录的用户ID
  Future<String?> getUserId();

  // 获取当前登录的用户员工ID
  Future<String?> getEmployeeId();

  // 当前用户绑定的Store信息刷新(独立接口，会实时变更，结构和getStore一致)
  Future<void> refreshStore();

  // 获取当前登录的用户Token
  Future<String?> getUserToken();

  // 获取当前登录的用户Token过期时间
  Future<String?> getTokenExpire();

  // 同步当前用户登录信息
  Future<void> syncCurrentUser(User user, Store? store);

  // 同步当前主账号信息
  Future<void> syncCreator(Creator? creator);

  // 清除用户认证数据
  Future<void> clearCurrentUser();

  // 检查是否需要刷新Token
  Future<bool> needToRefreshToken();

  // 检查用户是否拥有某个能力
  bool hasCapability(UserCapability capability);
}
