import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'package:turing_art/ui/feedback/use_case/upload_feedback_use_case.dart';

/// 处理从Unity发送过来的用户主动上报的异常信息
class HandleAbnormalFeedbackUseCase {
  final UploadFeedbackUseCase uploadFeedbackUseCase;
  HandleAbnormalFeedbackUseCase(this.uploadFeedbackUseCase);

  Future<void> invoke(MessageFromUnity message) async {
    final args = message.args;
    if (args == null || args.isEmpty) {
      PGLog.e('HandleAbnormalFeedbackUseCase: 接收到的消息参数不正确');
      return;
    }
    try {
      final jsonStr = args[0] as String;
      // 解析 unity 组装好的反馈信息，取出待上传文件
      final jsonMap = json.decode(jsonStr);
      final category = jsonMap["category"];
      final content = jsonMap["content"];
      final filepath = jsonMap["attachments"];
      uploadFeedbackUseCase.invoke(category, content, filepath);
    } catch (e) {
      PGLog.e('HandleAbnormalFeedbackUseCase: 处理异常反馈上传失败: $e');
    }
  }
}
