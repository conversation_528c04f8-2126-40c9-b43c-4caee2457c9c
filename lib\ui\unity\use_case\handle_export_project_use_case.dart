import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/utils/pg_log.dart';

class HandleExportProjectUseCase {
  final ExportProjectProvider _exportProjectProvider;

  HandleExportProjectUseCase(this._exportProjectProvider);

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    PGLog.d('HandleExportProjectUseCase: 收到Unity消息: ${message.method}');
    final args = message.args;
    if (args == null || args.isEmpty) {
      PGLog.e('HandleExportProjectUseCase: 参数为空');
      return;
    }

    try {
      final jsonString = args[0] as String;
      await _exportProjectProvider.updateExportProjectRecord(jsonString);
    } catch (e) {
      PGLog.d('HandleExportProjectUseCase: 处理导出失败错误: $e');
    }
  }

  Future<void> invokeBatch(MessageFromUnity message) async {
    PGLog.d('HandleExportProjectUseCase: 收到Unity消息: ${message.method}');
    final args = message.args;
    if (args == null || args.isEmpty) {
      PGLog.e('HandleExportProjectUseCase: 参数为空');
      return;
    }

    try {
      final jsonString = args[0] as String;
      await _exportProjectProvider.updateExportBatchProjectRecords(jsonString);
    } catch (e) {
      PGLog.d('HandleExportProjectUseCase: 处理导出失败错误: $e');
    }
  }

  Future<void> openExportListDialog() async {
    await ExportListDialog2.showOnUnity();
  }

  Future<void> invokeReloadProjectRecords(MessageFromUnity message) async {
    PGLog.d('HandleExportProjectUseCase: 收到Unity消息: ${message.method}');
    _exportProjectProvider.invokeReloadProjectRecords(needReload: true);
  }
}
