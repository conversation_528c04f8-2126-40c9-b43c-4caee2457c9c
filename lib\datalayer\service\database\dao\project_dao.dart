import 'package:drift/drift.dart';

import '../database.dart';

extension ProjectDao on DataBase {
  // 查询所有项目并按照降序返回
  Future<List<ProjectEntityData>> getAllProjects({
    bool isDelete = false,
  }) async {
    // 按 updateDate 降序排序，只返回未删除的项目
    final projectEntities = await (select(projectEntity)
          ..where((t) => t.isDelete.equals(isDelete))
          ..orderBy([(t) => OrderingTerm.desc(t.updateDate)]))
        .get();
    return projectEntities;
  }

  // 查询所有项目并按照降序返回
  Future<List<ProjectEntityData>> getUserAllProjects(
    String userId, {
    bool isDelete = false,
  }) async {
    // 按 updateDate 降序排序，只返回未删除的项目
    final projectEntities = await (select(projectEntity)
          ..where((t) => t.author.equals(userId) & t.isDelete.equals(isDelete))
          ..orderBy([(t) => OrderingTerm.desc(t.updateDate)]))
        .get();
    return projectEntities;
  }

  // 查询所有项目并按照降序返回
  Future<List<ProjectEntityData>> getUserAllProjectsByType(
    String userId,
    int projectType, {
    bool isDelete = false,
  }) async {
    // 按 updateDate 降序排序，只返回未删除的项目
    final projectEntities = await (select(projectEntity)
          ..where((t) =>
              t.author.equals(userId) &
              t.projectType.equals(projectType) &
              t.isDelete.equals(isDelete))
          ..orderBy([(t) => OrderingTerm.desc(t.updateDate)]))
        .get();
    return projectEntities;
  }

  // 监听所有的指定 projectId 的 projectEntity 实体。
  // 如果底层数据修改，这个流会自动发出新的项目，即跟着底层数据实时更新。
  Stream<List<ProjectEntityData>> watchEntriesInCategory(String projectId) {
    return (select(projectEntity)
          ..where(
              (t) => t.projectId.equals(projectId) & t.isDelete.equals(false)))
        .watch();
  }

  // 根据 projectId 获取单个项目
  Future<ProjectEntityData?> getProjectById(String projectId) async {
    final result = await (select(projectEntity)
          ..where(
              (t) => t.projectId.equals(projectId) & t.isDelete.equals(false)))
        .getSingleOrNull();
    return result;
  }

  /// 根据 projectId 查询工程属于哪个用户
  /// [projectId] 工程ID
  /// 返回用户ID，如果工程不存在则返回 null
  Future<String?> getProjectAuthorById(String projectId) async {
    final result = await (select(projectEntity)
          ..where(
              (t) => t.projectId.equals(projectId) & t.isDelete.equals(false)))
        .getSingleOrNull();
    return result?.author;
  }

  // 插入新项目
  Future<int> insertProject(ProjectEntityCompanion entity) {
    return into(projectEntity).insert(entity);
  }

  // 更新项目
  Future<bool> updateProject(ProjectEntityCompanion entity) {
    return update(projectEntity).replace(entity);
  }

  // 删除项目
  Future<int> deleteProject(String projectId) {
    return (delete(projectEntity)..where((t) => t.projectId.equals(projectId)))
        .go();
  }

  // 批量删除项目
  Future<void> deleteProjects(List<String> projectIds) async {
    if (projectIds.isEmpty) {
      return;
    }

    await batch((batch) {
      // 使用 deleteWhere 进行批量删除，这比循环调用 delete 更高效
      batch.deleteWhere(
        projectEntity,
        (t) => t.projectId.isIn(projectIds),
      );
    });
  }
}
