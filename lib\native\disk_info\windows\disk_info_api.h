#ifndef DISK_INFO_API_H
#define DISK_INFO_API_H

#if defined(_WIN32)
    #ifdef DISK_INFO_EXPORTS
        #define DISK_INFO_API __declspec(dllexport)
    #else
        #define DISK_INFO_API __declspec(dllimport)
    #endif
#else
    #define DISK_INFO_API
#endif

#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

DISK_INFO_API bool get_disk_space(const char* path, uint64_t& total_space, uint64_t& free_space);

/**
 * @brief Determines whether the disk containing the given file path is HDD or SSD.
 *
 * @param path UTF-8 file or directory path.
 * @param type Output disk type for the underlying physical disk.
 * @return true on success, false on failure or unsupported platform.
 */
DISK_INFO_API bool get_disk_type_from_path(const char* path, enum DiskType& type);

// Enum to represent the type of the disk
enum DiskType {
    DISK_TYPE_UNKNOWN = 0,
    DISK_TYPE_HDD = 1,
    DISK_TYPE_SSD = 2
};

// Struct to hold information about a single disk
struct DiskInfo {
    char* path; // The path or name of the disk (e.g., "C:" or "/dev/sda")
    enum DiskType type;
};

// Struct to hold a list of disks
struct DiskInfoList {
    int count;
    struct DiskInfo* disks;
};

/**
 * @brief Retrieves information about all physical disks in the system.
 *
 * This function allocates memory for the returned DiskInfoList structure.
 * The caller is responsible for freeing this memory by calling free_disk_info().
 *
 * @return A pointer to a DiskInfoList structure, or NULL on failure.
 */
DISK_INFO_API struct DiskInfoList* get_all_disks_info();

/**
 * @brief Frees the memory allocated by get_all_disks_info().
 *
 * @param info_list A pointer to the DiskInfoList structure to be freed.
 */
DISK_INFO_API void free_disk_info(struct DiskInfoList* info_list);


#ifdef __cplusplus
}
#endif

#endif // DISK_INFO_API_H