import 'package:dio/dio.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 业务错误码枚举
enum BusinessErrorCode {
  /// 积分不足
  insufficientCredits(300001, '积分不足'),

  /// 已有其他设备正在导出
  deviceExporting(4101, '已有其他设备正在导出'),

  /// 已有其他设备已导出
  deviceExported(4102, '已有其他设备已导出'),

  /// 样片不存在或已被删除
  sampleNotExist(4001, '样片不存在或已被删除');

  const BusinessErrorCode(this.code, this.description);

  /// 错误码
  final int code;

  /// 错误描述
  final String description;

  /// 根据错误码获取对应的枚举
  static BusinessErrorCode? fromCode(int code) {
    for (final errorCode in BusinessErrorCode.values) {
      if (errorCode.code == code) {
        return errorCode;
      }
    }
    return null;
  }
}

/// 通用业务错误处理器
/// 统一处理项目中的自定义业务逻辑错误
class CommonBusinessErrorHandler {
  /// 处理业务错误
  ///
  /// 参数:
  /// - error: 异常对象
  /// - operation: 操作名称，用于生成错误提示
  ///
  /// 返回: 错误信息字符串
  static (String errorMessage, BusinessErrorCode? errorCode) handleError(
      Object error, String operation) {
    if (error is DioException && error.response?.data is Map<String, dynamic>) {
      final data = error.response?.data as Map<String, dynamic>;

      if (data.containsKey('code')) {
        final errorCode = BusinessErrorCode.fromCode(data['code']);
        if (errorCode != null) {
          PGLog.w(
              '业务错误 [${errorCode.code}]: ${errorCode.description} - $operation');
          return ('${errorCode.description}，$operation失败', errorCode);
        }
      }

      // 其他业务错误，使用服务器返回的message
      if (data.containsKey('message')) {
        final message = data['message'] as String;
        PGLog.e('业务错误: $message - $operation');
        return ('$operation失败，$message', null);
      }
    }

    // 网络错误或其他异常
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          PGLog.e('网络超时错误: $operation - $error');
          return ('网络超时，请检查网络连接', null);
        case DioExceptionType.connectionError:
          PGLog.e('网络连接错误: $operation - $error');
          return ('网络连接失败，请检查网络', null);
        default:
          break;
      }
    }

    // 默认错误信息
    PGLog.e('未知错误: $operation失败 - $error');
    return ('$operation失败，请稍后重试', null);
  }
}
