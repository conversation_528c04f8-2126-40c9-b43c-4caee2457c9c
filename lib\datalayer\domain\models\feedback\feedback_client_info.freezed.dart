// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feedback_client_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FeedbackClientInfo _$FeedbackClientInfoFromJson(Map<String, dynamic> json) {
  return _FeedbackClientInfo.fromJson(json);
}

/// @nodoc
mixin _$FeedbackClientInfo {
  String? get appVersion => throw _privateConstructorUsedError;
  String? get systemVersion => throw _privateConstructorUsedError;
  String? get cpuInfo => throw _privateConstructorUsedError;
  String? get memoryInfo => throw _privateConstructorUsedError;
  String? get diskInfo => throw _privateConstructorUsedError;
  String? get gpuInfo => throw _privateConstructorUsedError;
  String? get networkInfo => throw _privateConstructorUsedError;
  String? get screenInfo => throw _privateConstructorUsedError;
  String? get platform => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FeedbackClientInfoCopyWith<FeedbackClientInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedbackClientInfoCopyWith<$Res> {
  factory $FeedbackClientInfoCopyWith(
          FeedbackClientInfo value, $Res Function(FeedbackClientInfo) then) =
      _$FeedbackClientInfoCopyWithImpl<$Res, FeedbackClientInfo>;
  @useResult
  $Res call(
      {String? appVersion,
      String? systemVersion,
      String? cpuInfo,
      String? memoryInfo,
      String? diskInfo,
      String? gpuInfo,
      String? networkInfo,
      String? screenInfo,
      String? platform});
}

/// @nodoc
class _$FeedbackClientInfoCopyWithImpl<$Res, $Val extends FeedbackClientInfo>
    implements $FeedbackClientInfoCopyWith<$Res> {
  _$FeedbackClientInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appVersion = freezed,
    Object? systemVersion = freezed,
    Object? cpuInfo = freezed,
    Object? memoryInfo = freezed,
    Object? diskInfo = freezed,
    Object? gpuInfo = freezed,
    Object? networkInfo = freezed,
    Object? screenInfo = freezed,
    Object? platform = freezed,
  }) {
    return _then(_value.copyWith(
      appVersion: freezed == appVersion
          ? _value.appVersion
          : appVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      systemVersion: freezed == systemVersion
          ? _value.systemVersion
          : systemVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      cpuInfo: freezed == cpuInfo
          ? _value.cpuInfo
          : cpuInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      memoryInfo: freezed == memoryInfo
          ? _value.memoryInfo
          : memoryInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      diskInfo: freezed == diskInfo
          ? _value.diskInfo
          : diskInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      gpuInfo: freezed == gpuInfo
          ? _value.gpuInfo
          : gpuInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      networkInfo: freezed == networkInfo
          ? _value.networkInfo
          : networkInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      screenInfo: freezed == screenInfo
          ? _value.screenInfo
          : screenInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FeedbackClientInfoImplCopyWith<$Res>
    implements $FeedbackClientInfoCopyWith<$Res> {
  factory _$$FeedbackClientInfoImplCopyWith(_$FeedbackClientInfoImpl value,
          $Res Function(_$FeedbackClientInfoImpl) then) =
      __$$FeedbackClientInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? appVersion,
      String? systemVersion,
      String? cpuInfo,
      String? memoryInfo,
      String? diskInfo,
      String? gpuInfo,
      String? networkInfo,
      String? screenInfo,
      String? platform});
}

/// @nodoc
class __$$FeedbackClientInfoImplCopyWithImpl<$Res>
    extends _$FeedbackClientInfoCopyWithImpl<$Res, _$FeedbackClientInfoImpl>
    implements _$$FeedbackClientInfoImplCopyWith<$Res> {
  __$$FeedbackClientInfoImplCopyWithImpl(_$FeedbackClientInfoImpl _value,
      $Res Function(_$FeedbackClientInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appVersion = freezed,
    Object? systemVersion = freezed,
    Object? cpuInfo = freezed,
    Object? memoryInfo = freezed,
    Object? diskInfo = freezed,
    Object? gpuInfo = freezed,
    Object? networkInfo = freezed,
    Object? screenInfo = freezed,
    Object? platform = freezed,
  }) {
    return _then(_$FeedbackClientInfoImpl(
      appVersion: freezed == appVersion
          ? _value.appVersion
          : appVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      systemVersion: freezed == systemVersion
          ? _value.systemVersion
          : systemVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      cpuInfo: freezed == cpuInfo
          ? _value.cpuInfo
          : cpuInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      memoryInfo: freezed == memoryInfo
          ? _value.memoryInfo
          : memoryInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      diskInfo: freezed == diskInfo
          ? _value.diskInfo
          : diskInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      gpuInfo: freezed == gpuInfo
          ? _value.gpuInfo
          : gpuInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      networkInfo: freezed == networkInfo
          ? _value.networkInfo
          : networkInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      screenInfo: freezed == screenInfo
          ? _value.screenInfo
          : screenInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedbackClientInfoImpl implements _FeedbackClientInfo {
  const _$FeedbackClientInfoImpl(
      {this.appVersion,
      this.systemVersion,
      this.cpuInfo,
      this.memoryInfo,
      this.diskInfo,
      this.gpuInfo,
      this.networkInfo,
      this.screenInfo,
      this.platform});

  factory _$FeedbackClientInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedbackClientInfoImplFromJson(json);

  @override
  final String? appVersion;
  @override
  final String? systemVersion;
  @override
  final String? cpuInfo;
  @override
  final String? memoryInfo;
  @override
  final String? diskInfo;
  @override
  final String? gpuInfo;
  @override
  final String? networkInfo;
  @override
  final String? screenInfo;
  @override
  final String? platform;

  @override
  String toString() {
    return 'FeedbackClientInfo(appVersion: $appVersion, systemVersion: $systemVersion, cpuInfo: $cpuInfo, memoryInfo: $memoryInfo, diskInfo: $diskInfo, gpuInfo: $gpuInfo, networkInfo: $networkInfo, screenInfo: $screenInfo, platform: $platform)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedbackClientInfoImpl &&
            (identical(other.appVersion, appVersion) ||
                other.appVersion == appVersion) &&
            (identical(other.systemVersion, systemVersion) ||
                other.systemVersion == systemVersion) &&
            (identical(other.cpuInfo, cpuInfo) || other.cpuInfo == cpuInfo) &&
            (identical(other.memoryInfo, memoryInfo) ||
                other.memoryInfo == memoryInfo) &&
            (identical(other.diskInfo, diskInfo) ||
                other.diskInfo == diskInfo) &&
            (identical(other.gpuInfo, gpuInfo) || other.gpuInfo == gpuInfo) &&
            (identical(other.networkInfo, networkInfo) ||
                other.networkInfo == networkInfo) &&
            (identical(other.screenInfo, screenInfo) ||
                other.screenInfo == screenInfo) &&
            (identical(other.platform, platform) ||
                other.platform == platform));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      appVersion,
      systemVersion,
      cpuInfo,
      memoryInfo,
      diskInfo,
      gpuInfo,
      networkInfo,
      screenInfo,
      platform);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedbackClientInfoImplCopyWith<_$FeedbackClientInfoImpl> get copyWith =>
      __$$FeedbackClientInfoImplCopyWithImpl<_$FeedbackClientInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedbackClientInfoImplToJson(
      this,
    );
  }
}

abstract class _FeedbackClientInfo implements FeedbackClientInfo {
  const factory _FeedbackClientInfo(
      {final String? appVersion,
      final String? systemVersion,
      final String? cpuInfo,
      final String? memoryInfo,
      final String? diskInfo,
      final String? gpuInfo,
      final String? networkInfo,
      final String? screenInfo,
      final String? platform}) = _$FeedbackClientInfoImpl;

  factory _FeedbackClientInfo.fromJson(Map<String, dynamic> json) =
      _$FeedbackClientInfoImpl.fromJson;

  @override
  String? get appVersion;
  @override
  String? get systemVersion;
  @override
  String? get cpuInfo;
  @override
  String? get memoryInfo;
  @override
  String? get diskInfo;
  @override
  String? get gpuInfo;
  @override
  String? get networkInfo;
  @override
  String? get screenInfo;
  @override
  String? get platform;
  @override
  @JsonKey(ignore: true)
  _$$FeedbackClientInfoImplCopyWith<_$FeedbackClientInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
