import 'dart:io';

import 'package:turing_art/ffi/ffi.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘信息原生客户端
/// 封装FFI调用，提供类型安全的接口
class DiskInfoNativeClient {
  static bool _initialized = false;

  /// 初始化原生客户端
  static Future<bool> initialize() async {
    if (_initialized) {
      return true;
    }

    try {
      // 初始化DiskInfoBindings
      _initialized = DiskInfoBindings.initialize();
      if (_initialized) {
        PGLog.i('DiskInfoNativeClient: 原生客户端初始化成功');
      } else {
        PGLog.e('DiskInfoNativeClient: 原生客户端初始化失败');
      }
      return _initialized;
    } catch (e) {
      PGLog.e('DiskInfoNativeClient: 初始化异常: $e');
      return false;
    }
  }

  /// 检查原生客户端是否可用
  static bool get isAvailable => _initialized && DiskInfoBindings.isAvailable;

  /// 获取所有磁盘信息
  /// 返回磁盘信息列表，失败时返回null
  static List<DiskInfoEntry>? getAllDisksInfo() {
    if (!isAvailable) {
      PGLog.w('DiskInfoNativeClient: 原生客户端不可用');
      return null;
    }

    try {
      final diskInfos = DiskInfoBindings.getAllDisksInfo();
      if (diskInfos != null && diskInfos.isNotEmpty) {
        PGLog.d('DiskInfoNativeClient: 获取到${diskInfos.length}个磁盘信息');
      }
      return diskInfos;
    } catch (e) {
      PGLog.e('DiskInfoNativeClient: 获取磁盘信息失败: $e');
      return null;
    }
  }

  /// 获取指定路径的磁盘类型
  /// 返回磁盘类型，失败时返回null
  static Future<DiskType?> getDiskTypeForPath(String path) async {
    if (!isAvailable) {
      PGLog.w('DiskInfoNativeClient: 原生客户端不可用');
      return null;
    }

    try {
      final normalizedPath = _normalizePath(path);
      final diskType = DiskInfoBindings.getDiskTypeFromPath(normalizedPath);
      return diskType;
    } catch (e) {
      PGLog.e('DiskInfoNativeClient: 获取磁盘类型失败: $e');
      return null;
    }
  }

  /// 获取指定路径的磁盘空间信息
  /// 返回磁盘空间信息，失败时返回null
  static Future<DiskSpaceInfo?> getDiskSpace(String path) async {
    if (!isAvailable) {
      PGLog.w('DiskInfoNativeClient: 原生客户端不可用');
      return null;
    }

    try {
      final normalizedPath = _normalizePath(path);
      final diskSpace = DiskInfoBindings.getDiskSpace(normalizedPath);
      return diskSpace;
    } catch (e) {
      PGLog.e('DiskInfoNativeClient: 获取磁盘空间失败: $e');
      return null;
    }
  }

  /// 提取路径的磁盘根路径
  /// Windows: "C:\path\to\file" -> "C:\"
  /// Unix: "/path/to/file" -> "/"
  static String extractDriveRoot(String path) {
    final normalizedPath = _normalizePath(path);

    if (Platform.isWindows) {
      // Windows系统：提取盘符
      if (normalizedPath.length >= 2 && normalizedPath[1] == ':') {
        return '${normalizedPath[0].toUpperCase()}:\\';
      }
      // 如果是UNC路径或其他格式，返回原路径
      return normalizedPath;
    } else {
      // Unix系统：都是根路径 "/"
      return '/';
    }
  }

  /// 标准化路径
  static String _normalizePath(String path) {
    if (Platform.isWindows) {
      var normalized = path.replaceAll('/', '\\');
      if (normalized.length == 2 && normalized.endsWith(':')) {
        normalized += '\\';
      }
      return normalized;
    } else {
      var normalized = path.replaceAll('\\', '/');
      if (normalized.isEmpty) {
        normalized = '/';
      }
      return normalized;
    }
  }

  /// 检查指定平台是否支持磁盘信息查询
  static bool get isPlatformSupported => Platform.isWindows;

  /// 清理资源
  static void dispose() {
    try {
      DiskInfoBindings.dispose();
      _initialized = false;
      PGLog.i('DiskInfoNativeClient: 资源已清理');
    } catch (e) {
      PGLog.e('DiskInfoNativeClient: 清理资源失败: $e');
    }
  }
}
