import 'dart:collection';

import 'generic_task.dart';

/// 通用任务队列
/// 专门设计用于高效的任务管理，满足两个核心需求：
/// 1. 根据ID快速查找/删除/更新任务
/// 2. 快速找到优先级最高的任务
class TaskQueue<T extends GenericTask> {
  // 使用Map进行快速ID查找 - O(1)
  final Map<String, T> _taskMap = <String, T>{};

  // 使用SplayTreeSet维护优先级排序 - O(log n)
  late final SplayTreeSet<T> _prioritySet;

  TaskQueue() {
    _prioritySet = SplayTreeSet<T>((a, b) {
      // 按优先级排序（数值小的优先级高）
      if (a.priority != b.priority) {
        return a.priority.compareTo(b.priority);
      }

      // 优先级相同时按提交时间排序
      final timeCompare = a.submittedAt.compareTo(b.submittedAt);
      if (timeCompare != 0) {
        return timeCompare;
      }

      // 最后按任务ID排序（确保唯一性）
      return a.taskId.compareTo(b.taskId);
    });
  }

  /// 安全地复制任务并转换为正确的类型
  T _copyTaskWithPriority(T task, int newPriority) {
    final copied = task.copyWithPriority(newPriority);
    // 由于我们知道 task 是 T 类型，其 copyWithPriority 方法应该返回相同类型
    // 这个强制转换在逻辑上是安全的
    return copied as T;
  }

  /// 添加任务 - O(log n)
  bool add(T task) {
    // 检查是否已存在
    if (_taskMap.containsKey(task.taskId)) {
      return false; // 任务已存在
    }

    _taskMap[task.taskId] = task;
    _prioritySet.add(task);

    return true;
  }

  /// 根据ID查找任务 - O(1)
  T? getById(String taskId) {
    return _taskMap[taskId];
  }

  /// 检查是否包含任务 - O(1)
  bool containsId(String taskId) {
    return _taskMap.containsKey(taskId);
  }

  /// 根据ID移除任务 - O(1) + O(log n)
  bool removeById(String taskId) {
    final task = _taskMap.remove(taskId);
    if (task != null) {
      _prioritySet.remove(task);
      return true;
    }
    return false;
  }

  /// 根据条件移除所有任务 - O(n)
  int removeWhere(bool Function(T) test) {
    final toRemove = <String>[];
    for (final entry in _taskMap.entries) {
      if (test(entry.value)) {
        toRemove.add(entry.key);
      }
    }

    int removedCount = 0;
    for (final taskId in toRemove) {
      if (removeById(taskId)) {
        removedCount++;
      }
    }
    return removedCount;
  }

  /// 更新任务优先级 - O(1) + O(log n) + O(log n)
  bool updatePriority(String taskId, int newPriority) {
    final oldTask = _taskMap[taskId];
    if (oldTask == null) {
      return false;
    }

    if (oldTask.priority == newPriority) {
      return true;
    }

    // 创建新任务
    final newTask = _copyTaskWithPriority(oldTask, newPriority);

    // 原子性更新
    _prioritySet.remove(oldTask);
    _taskMap[taskId] = newTask;
    _prioritySet.add(newTask);

    return true;
  }

  /// 获取并移除优先级最高的任务 - O(log n)
  T? removeFirst() {
    if (_prioritySet.isEmpty) {
      return null;
    }

    final first = _prioritySet.first;
    _prioritySet.remove(first);
    _taskMap.remove(first.taskId);

    return first;
  }

  /// 查看优先级最高的任务（不移除） - O(1)
  T? get first => _prioritySet.isEmpty ? null : _prioritySet.first;

  /// 查找符合条件的优先级最高的任务（不移除） - O(n)
  T? firstWhere(bool Function(T) test) {
    for (final task in _prioritySet) {
      if (test(task)) {
        return task;
      }
    }
    return null;
  }

  /// 获取并移除符合条件的优先级最高的任务 - O(n) + O(log n)
  T? removeFirstWhere(bool Function(T) test) {
    for (final task in _prioritySet) {
      if (test(task)) {
        _prioritySet.remove(task);
        _taskMap.remove(task.taskId);
        return task;
      }
    }
    return null;
  }

  /// 清空所有任务 - O(n)
  void clear() {
    _taskMap.clear();
    _prioritySet.clear();
  }

  /// 集合大小 - O(1)
  int get length => _taskMap.length;

  /// 是否为空 - O(1)
  bool get isEmpty => _taskMap.isEmpty;

  /// 是否非空 - O(1)
  bool get isNotEmpty => _taskMap.isNotEmpty;

  /// 获取所有任务（用于调试） - O(n)
  List<T> toList() {
    return _prioritySet.toList();
  }
}
