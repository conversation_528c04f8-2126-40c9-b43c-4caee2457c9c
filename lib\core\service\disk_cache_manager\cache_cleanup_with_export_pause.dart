import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/ui/export_result/use_case/export_task_events.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缓存清理与导出任务暂停的集成服务
/// 监听DiskCacheManager的状态变化，自动管理导出任务的暂停和恢复
class CacheCleanupWithExportPause extends ChangeNotifier {
  final DiskCacheManager _cacheManager;
  final ExportTaskEventBus _eventBus;
  StreamSubscription<CacheManagerState>? _stateSubscription;

  /// 是否已经暂停了导出任务
  bool _hasPausedExportTasks = false;

  /// 缓存清理开始时间
  DateTime? _cleanupStartTime;

  /// 是否已经暂停了导出任务
  bool get hasPausedExportTasks => _hasPausedExportTasks;

  /// 缓存清理开始时间
  DateTime? get cleanupStartTime => _cleanupStartTime;

  CacheCleanupWithExportPause({
    required DiskCacheManager cacheManager,
    required ExportTaskEventBus eventBus,
  })  : _cacheManager = cacheManager,
        _eventBus = eventBus {
    _initializeStateListener();
    PGLog.d('CacheCleanupWithExportPause 初始化成功，开始监听缓存状态变化');
  }

  /// 初始化状态监听器
  void _initializeStateListener() {
    _stateSubscription = _cacheManager.stateStream.listen(
      _onCacheManagerStateChanged,
      onError: (error) {
        PGLog.e('监听缓存管理器状态变化失败: $error');
      },
    );
  }

  /// 处理缓存管理器状态变化
  void _onCacheManagerStateChanged(CacheManagerState state) {
    try {
      PGLog.d('CacheCleanupWithExportPause: 检测到缓存状态变化: $state');

      switch (state) {
        case CacheManagerState.clearingCache:
          _handleCacheClearingStarted();
          break;
        case CacheManagerState.idle:
          _handleCacheClearingCompleted();
          break;
        case CacheManagerState.gettingCacheSize:
          // 获取缓存大小，不需要处理导出任务
          break;
      }
    } catch (e) {
      PGLog.e('处理缓存状态变化失败: $e');
    }
  }

  /// 处理缓存清理开始
  Future<void> _handleCacheClearingStarted() async {
    if (_hasPausedExportTasks) {
      PGLog.d('导出任务已经暂停，跳过重复暂停');
      return;
    }

    try {
      PGLog.d('缓存清理开始，暂停所有导出任务');
      _cleanupStartTime = DateTime.now();

      await _eventBus.pauseAllExportTasks(
        source: ExportTaskEventSource.cacheCleanup,
        metadata: {
          'reason': 'cache_cleanup_started',
          'startTime': _cleanupStartTime!.toIso8601String(),
          'cacheManagerState': 'clearingCache',
        },
      );

      _hasPausedExportTasks = true;
      PGLog.d('成功暂停导出任务，等待缓存清理完成');

      // 通知UI更新
      notifyListeners();
    } catch (e) {
      PGLog.e('暂停导出任务失败: $e');
      _hasPausedExportTasks = false;
      _cleanupStartTime = null;
    }
  }

  /// 处理缓存清理完成
  Future<void> _handleCacheClearingCompleted() async {
    if (!_hasPausedExportTasks) {
      PGLog.d('没有暂停的导出任务，跳过恢复操作');
      return;
    }

    try {
      final duration = _cleanupStartTime != null
          ? DateTime.now().difference(_cleanupStartTime!)
          : Duration.zero;

      PGLog.d('缓存清理完成，恢复导出任务，耗时: ${duration.inMilliseconds}ms');

      await _eventBus.resumePausedExportTasks(
        source: ExportTaskEventSource.cacheCleanup,
        metadata: {
          'reason': 'cache_cleanup_completed',
          'startTime': _cleanupStartTime?.toIso8601String(),
          'endTime': DateTime.now().toIso8601String(),
          'durationMs': duration.inMilliseconds,
          'cacheManagerState': 'idle',
        },
      );

      _hasPausedExportTasks = false;
      _cleanupStartTime = null;
      PGLog.d('成功恢复导出任务');

      // 通知UI更新
      notifyListeners();
    } catch (e) {
      PGLog.e('恢复导出任务失败: $e');
      // 即使恢复失败，也要重置状态，避免卡死
      _hasPausedExportTasks = false;
      _cleanupStartTime = null;
      notifyListeners();
    }
  }

  /// 手动触发导出任务暂停（用于紧急情况）
  Future<void> emergencyPauseExportTasks() async {
    try {
      PGLog.d('紧急暂停导出任务');
      await _eventBus.pauseAllExportTasks(
        source: ExportTaskEventSource.cacheCleanup,
        metadata: {
          'reason': 'emergency_pause',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      _hasPausedExportTasks = true;
      notifyListeners();
    } catch (e) {
      PGLog.e('紧急暂停导出任务失败: $e');
    }
  }

  /// 手动触发导出任务恢复（用于紧急情况）
  Future<void> emergencyResumeExportTasks() async {
    try {
      PGLog.d('紧急恢复导出任务');
      await _eventBus.resumePausedExportTasks(
        source: ExportTaskEventSource.cacheCleanup,
        metadata: {
          'reason': 'emergency_resume',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      _hasPausedExportTasks = false;
      _cleanupStartTime = null;
      notifyListeners();
    } catch (e) {
      PGLog.e('紧急恢复导出任务失败: $e');
    }
  }

  /// 获取当前状态信息
  Map<String, dynamic> getStatusInfo() {
    return {
      'hasPausedExportTasks': _hasPausedExportTasks,
      'cleanupStartTime': _cleanupStartTime?.toIso8601String(),
      'cacheManagerState': _cacheManager.currentState.name,
      'isClearingCache': _cacheManager.isClearingCache,
      'isGettingCacheSize': _cacheManager.isGettingCacheSize,
    };
  }

  @override
  void dispose() {
    try {
      _stateSubscription?.cancel();
      PGLog.d('CacheCleanupWithExportPause 已销毁');
    } catch (e) {
      PGLog.e('销毁 CacheCleanupWithExportPause 失败: $e');
    }
    super.dispose();
  }
}
