// SugoiNativeSDK FFI绑定文件
// 用于加载和调用SugoiNativeSDK.dll中的函数

import 'dart:ffi';
import 'dart:io';

import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

/// SDK_Env_Platform函数的FFI签名定义
/// 返回值类型根据实际情况调整（这里假设返回int）
typedef SdkEnvPlatformNative = Int32 Function();
typedef SdkEnvPlatformDart = int Function();

/// SugoiNativeSDK FFI绑定类
class SugoiNativeSDKBindings {
  static DynamicLibrary? _library;
  static SdkEnvPlatformDart? _sdkEnvPlatform;

  /// 初始化SDK绑定

  /// 使用UniversalPlatformLoader加载SugoiNativeSDK动态库
  static bool initialize() {
    try {
      // 使用统一的平台加载器加载库
      _library = UniversalPlatformLoader.loadSugoiNativeLibrary();

      // 绑定SDK_Env_Platform函数
      _sdkEnvPlatform = _library!
          .lookup<NativeFunction<SdkEnvPlatformNative>>('SDK_Env_Platform')
          .asFunction<SdkEnvPlatformDart>();

      PGLog.d('SugoiNativeSDK绑定初始化成功');
      return true;
    } catch (e) {
      PGLog.d('SugoiNativeSDK绑定初始化失败: $e');
      return false;
    }
  }

  /// 调用SDK_Env_Platform函数
  /// 返回平台环境信息
  static int? getSdkEnvPlatform() {
    if (_sdkEnvPlatform == null) {
      PGLog.d('SugoiNativeSDK未初始化，请先调用initialize()');
      return null;
    }

    try {
      final result = _sdkEnvPlatform!();
      PGLog.d('SDK_Env_Platform返回值: $result');
      return result;
    } catch (e) {
      PGLog.d('调用SDK_Env_Platform失败: $e');
      return null;
    }
  }

  /// 检查SDK是否已初始化
  static bool get isInitialized => _library != null && _sdkEnvPlatform != null;

  /// 清理资源
  static void dispose() {
    _library = null;
    _sdkEnvPlatform = null;
    PGLog.d('SugoiNativeSDK绑定已清理');
  }
}
