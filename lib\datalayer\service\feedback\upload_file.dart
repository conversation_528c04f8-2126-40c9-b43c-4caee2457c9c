import 'dart:convert';

import 'package:dio/dio.dart';

Future<void> uploadFile(
    {required String uploadCertifyJson, required String filePath}) async {
  final dio = Dio();
  final Map<String, dynamic> certMap = json.decode(uploadCertifyJson);
  final formData = FormData.fromMap({
    "key": certMap["key"],
    "policy": certMap["policy"],
    "OSSAccessKeyId": certMap["ossAccessKeyId"],
    "signature": certMap["signature"],
    "success_action_status": "200",
    "file": await MultipartFile.fromFile(filePath),
  });

  try {
    final response = await dio.post(certMap["host"],
        data: formData, options: Options(contentType: "multipart/form-data"));
    if (response.statusCode != 200) {
      throw ("Upload Error Code:${response.statusCode}");
    }
  } catch (e) {
    rethrow;
  }
}
