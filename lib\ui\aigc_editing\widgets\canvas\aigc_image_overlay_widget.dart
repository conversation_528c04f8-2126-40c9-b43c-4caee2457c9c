import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/painter/aigc_image_overlay_painter.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC图片叠加视图组件
///
/// 专门用于处理背景图和蒙层图的叠加显示
/// 简化版本，只负责图像显示，不处理手势
class AigcImageOverlayWidget extends StatefulWidget {
  /// 图像初始化完成回调
  final Function(Size imageSize)? onImageInitialized;

  /// 明确的画布尺寸（由父组件传递）
  final Size? explicitCanvasSize;

  const AigcImageOverlayWidget({
    super.key,
    this.onImageInitialized,
    this.explicitCanvasSize,
  });

  @override
  State<AigcImageOverlayWidget> createState() => _AigcImageOverlayWidgetState();
}

class _AigcImageOverlayWidgetState extends State<AigcImageOverlayWidget> {
  /// 图像尺寸
  Size? _imageSize;

  /// 上一次的画布尺寸，用于在图片切换期间保持稳定
  Size? _lastStableCanvasSize;

  @override
  void initState() {
    super.initState();

    // 等待组件完全构建后再设置监听
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 检查widget是否仍然mounted，避免在已销毁的widget上操作
      if (!mounted) {
        return;
      }

      try {
        final provider = context.read<AigcImageOverlayProvider>();
        provider.addListener(_handleProviderChanged);

        // 如果已经加载了图像，立即通知父组件
        if (provider.backgroundImage != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _notifyImageInitialized();
            }
          });
        }
      } catch (e) {
        PGLog.d('AigcImageOverlayWidget: 设置监听时发生错误: $e');
      }
    });
  }

  @override
  void dispose() {
    // 安全地移除Provider监听
    try {
      final provider = context.read<AigcImageOverlayProvider>();
      provider.removeListener(_handleProviderChanged);
    } catch (e) {
      // 忽略错误，可能是因为context已经被销毁
    }

    super.dispose();
  }

  /// 监听Provider变化
  void _handleProviderChanged() {
    if (!mounted) {
      return;
    }

    try {
      // 检查背景图片是否已加载
      final provider = context.read<AigcImageOverlayProvider>();

      if (provider.backgroundImage != null && _imageSize == null) {
        final image = provider.backgroundImage!;
        _imageSize =
            Size(image.width.ceilToDouble(), image.height.ceilToDouble());

        // 通知父组件图像已初始化
        if (widget.onImageInitialized != null) {
          widget.onImageInitialized!(_imageSize!);
        }
      }

      // 触发重绘
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // 忽略错误，可能是因为context已经被销毁
      PGLog.d('AigcImageOverlayWidget: 处理Provider变化时发生错误: $e');
    }
  }

  /// 通知图像初始化完成
  void _notifyImageInitialized() {
    if (!mounted) {
      return;
    }

    try {
      final provider = context.read<AigcImageOverlayProvider>();
      if (provider.backgroundImage != null &&
          widget.onImageInitialized != null) {
        final image = provider.backgroundImage!;
        final imageSize =
            Size(image.width.ceilToDouble(), image.height.ceilToDouble());
        widget.onImageInitialized!(imageSize);
      }
    } catch (e) {
      // 忽略错误，可能是因为context已经被销毁
      PGLog.d('AigcImageOverlayWidget: 通知图像初始化时发生错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<AigcImageOverlayProvider, AigcEditingControlProvider,
        AigcCanvasPainterProvider>(
      builder:
          (context, overlayProvider, controlProvider, canvasProvider, child) {
        // 获取画布尺寸 - 优先使用父组件明确传递的尺寸
        Size scaledCanvasSize;
        if (widget.explicitCanvasSize != null) {
          // 使用父组件明确传递的尺寸
          scaledCanvasSize = widget.explicitCanvasSize!;
        } else {
          // 备用方案：自己计算
          final contentSize = canvasProvider.contentSize;
          if (contentSize != null) {
            scaledCanvasSize = Size(
              contentSize.width * canvasProvider.scale,
              contentSize.height * canvasProvider.scale,
            );
            // 更新稳定的画布尺寸
            _lastStableCanvasSize = scaledCanvasSize;
          } else {
            // 如果contentSize为null（可能正在切换图片），使用上一次稳定的尺寸
            if (_lastStableCanvasSize != null) {
              scaledCanvasSize = _lastStableCanvasSize!;
            } else {
              scaledCanvasSize = Size(
                800 * canvasProvider.scale,
                600 * canvasProvider.scale,
              );
            }
          }
        }

        return CustomPaint(
          painter: AigcImageOverlayPainter(
            displayMode: controlProvider.displayMode,
            showMask: overlayProvider.showMask,
            context: context,
            maskBackgroundColor: controlProvider.maskBackgroundColor.color,
            currentScale: canvasProvider.scale,
            scaledCanvasSize: scaledCanvasSize,
          ),
          size: scaledCanvasSize,
        );
      },
    );
  }
}
