import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_category.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 设置配置迁移工具类
///
/// 负责处理设置配置的版本兼容性，确保旧版本的用户配置能够正确迁移到新版本
///
/// 使用示例:
/// ```dart
/// // 检查是否需要迁移
/// if (await SettingConfigMigrations.needsMigration(localConfig)) {
///   // 执行迁移
///   final migratedConfig = await SettingConfigMigrations.migrate(localConfig);
///
///   // 获取迁移统计信息
///   final stats = SettingConfigMigrations.getMigrationStats(localConfig, migratedConfig);
///   print('迁移完成: $stats');
/// }
/// ```
///
/// 迁移功能包括:
/// - 移除过时的配置分类（如旧的 cache_setting）
/// - 保留用户的自定义配置值
/// - 添加新的配置分类和选项
/// - 设置 isShowForUser 字段控制UI显示
/// - 确保配置结构与最新版本一致
class SettingConfigMigrations {
  static const String _defaultSettingPath =
      'assets/static/setting_config_json.json';

  /// 执行设置配置迁移
  ///
  /// [localConfig] 本地存储的配置
  /// 返回迁移后的配置
  static Future<SettingConfig> migrate(SettingConfig localConfig) async {
    try {
      PGLog.i('开始设置配置数据迁移...');

      // 加载最新的默认配置
      final defaultJsonString =
          await rootBundle.loadString(_defaultSettingPath);
      final defaultConfig =
          SettingConfig.fromJson(json.decode(defaultJsonString));

      // 获取默认配置中所有有效的分类key
      final validCategoryKeys =
          defaultConfig.categories.map((c) => c.key).toSet();

      // 过滤本地配置，只保留在默认配置中存在的分类
      final filteredLocalCategories = localConfig.categories
          .where((category) => validCategoryKeys.contains(category.key))
          .toList();

      PGLog.i('配置过滤完成: 原有${localConfig.categories.length}个分类，'
          '过滤后保留${filteredLocalCategories.length}个分类');

      // 记录被移除的分类
      final removedCategories = localConfig.categories
          .where((category) => !validCategoryKeys.contains(category.key))
          .map((category) => category.key)
          .toList();

      if (removedCategories.isNotEmpty) {
        PGLog.i('移除的过时分类: ${removedCategories.join(', ')}');
      }

      // 创建本地分类的映射，便于查找
      final localCategoryMap = <String, SettingCategory>{};
      for (final category in filteredLocalCategories) {
        localCategoryMap[category.key] = category;
      }

      // 合并配置：使用默认配置的结构，但保留本地配置的值
      final mergedCategories = <SettingCategory>[];
      for (final defaultCategory in defaultConfig.categories) {
        final mergedCategory = _mergeCategory(
            defaultCategory, localCategoryMap[defaultCategory.key]);
        mergedCategories.add(mergedCategory);
      }

      final migratedConfig = SettingConfig(categories: mergedCategories);

      PGLog.i('设置配置数据迁移完成: 最终包含${mergedCategories.length}个分类');

      return migratedConfig;
    } catch (e) {
      PGLog.e('设置配置数据迁移失败: $e');
      // 如果迁移失败，返回默认配置
      return await _getDefaultConfig();
    }
  }

  /// 合并单个分类配置
  ///
  /// [defaultCategory] 默认配置中的分类
  /// [localCategory] 本地配置中的分类（可能为null）
  /// 返回合并后的分类
  static SettingCategory _mergeCategory(
    SettingCategory defaultCategory,
    SettingCategory? localCategory,
  ) {
    if (localCategory != null) {
      // 如果本地有这个分类，合并配置项的值，但使用本地已有的值，而不是默认配置的值
      final mergedItems =
          _mergeItems(defaultCategory.items, localCategory.items);

      // 直接使用默认配置中的 isShowForUser 值
      return defaultCategory.copyWith(
        items: mergedItems,
      );
    } else {
      // 如果本地没有这个分类，直接使用默认配置（包括其 isShowForUser 值）
      return defaultCategory;
    }
  }

  /// 合并配置项列表
  ///
  /// [defaultItems] 默认配置中的配置项
  /// [localItems] 本地配置中的配置项
  /// 返回合并后的配置项列表
  static List<SettingItemModel> _mergeItems(
    List<SettingItemModel> defaultItems,
    List<SettingItemModel> localItems,
  ) {
    final mergedItems = <SettingItemModel>[];
    final localItemMap = <String, SettingItemModel>{};

    // 创建本地配置项的映射
    for (final item in localItems) {
      localItemMap[item.key] = item;
    }

    // 合并每个配置项
    for (final defaultItem in defaultItems) {
      final localItem = localItemMap[defaultItem.key];
      if (localItem != null) {
        // 保留本地配置的值，并根据value设置choices的isSelected状态
        if (defaultItem.choices.isNotEmpty) {
          // 有choices的情况，更新choices的isSelected状态
          final updatedChoices = defaultItem.choices.map((choice) {
            return choice.copyWith(isSelected: choice.value == localItem.value);
          }).toList();

          mergedItems.add(defaultItem.copyWith(
            value: localItem.value,
            choices: updatedChoices,
          ));
        } else {
          // 没有choices的情况，只设置value
          mergedItems.add(defaultItem.copyWith(
            value: localItem.value,
          ));
        }
        PGLog.d('保留用户配置: ${defaultItem.key} = ${localItem.value}');
      } else {
        // 使用默认配置项
        mergedItems.add(defaultItem);
        PGLog.d('使用默认配置: ${defaultItem.key} = ${defaultItem.value}');
      }
    }

    return mergedItems;
  }

  /// 获取默认配置
  static Future<SettingConfig> _getDefaultConfig() async {
    try {
      final defaultJsonString =
          await rootBundle.loadString(_defaultSettingPath);
      return SettingConfig.fromJson(json.decode(defaultJsonString));
    } catch (e) {
      PGLog.e('加载默认配置失败: $e');
      return const SettingConfig(categories: []);
    }
  }

  /// 检查是否需要迁移
  ///
  /// [localConfig] 本地配置
  /// 返回是否需要迁移
  static Future<bool> needsMigration(SettingConfig localConfig) async {
    try {
      final defaultConfig = await _getDefaultConfig();
      final defaultCategoryKeys =
          defaultConfig.categories.map((c) => c.key).toSet();
      final localCategoryKeys =
          localConfig.categories.map((c) => c.key).toSet();

      // 如果本地配置包含不再支持的分类，则需要迁移
      final hasObsoleteCategories =
          localCategoryKeys.any((key) => !defaultCategoryKeys.contains(key));

      // 如果默认配置包含本地配置没有的新分类，则需要迁移
      final hasNewCategories =
          defaultCategoryKeys.any((key) => !localCategoryKeys.contains(key));

      // 检查是否有分类缺少 isShowForUser 字段
      final hasOldStructure = localConfig.categories
          .any((category) => !_hasIsShowForUserField(category));

      // 检查是否有分类的 isShowForUser 字段值发生了变更
      final hasShowForUserChanges =
          _hasShowForUserChanges(localConfig, defaultConfig);

      final needsMigration = hasObsoleteCategories ||
          hasNewCategories ||
          hasOldStructure ||
          hasShowForUserChanges;

      if (needsMigration) {
        PGLog.i('检测到需要配置迁移: '
            '过时分类=$hasObsoleteCategories, '
            '新增分类=$hasNewCategories, '
            '旧结构=$hasOldStructure, '
            'isShowForUser变更=$hasShowForUserChanges');
      }

      return needsMigration;
    } catch (e) {
      PGLog.e('检查迁移需求失败: $e');
      return true; // 出错时默认需要迁移
    }
  }

  /// 检查分类是否包含 isShowForUser 字段
  ///
  /// 注意：由于 freezed 生成的类可能无法直接检查字段是否存在，
  /// 这里通过序列化检查 JSON 结构来判断
  static bool _hasIsShowForUserField(SettingCategory category) {
    try {
      final json = category.toJson();
      return json.containsKey('isShowForUser');
    } catch (e) {
      return false;
    }
  }

  /// 检查是否有分类的 isShowForUser 字段值发生了变更
  ///
  /// [localConfig] 本地配置
  /// [defaultConfig] 默认配置
  /// 返回是否有 isShowForUser 字段值的变更
  static bool _hasShowForUserChanges(
      SettingConfig localConfig, SettingConfig defaultConfig) {
    try {
      // 创建默认配置分类的映射
      final defaultCategoryMap = <String, SettingCategory>{};
      for (final category in defaultConfig.categories) {
        defaultCategoryMap[category.key] = category;
      }

      // 检查本地配置中的每个分类
      for (final localCategory in localConfig.categories) {
        final defaultCategory = defaultCategoryMap[localCategory.key];
        if (defaultCategory != null) {
          // 直接使用默认配置中的 isShowForUser 值
          final expectedIsShowForUser = defaultCategory.isShowForUser;

          // 如果本地配置的 isShowForUser 值与期望值不同，则需要迁移
          if (localCategory.isShowForUser != expectedIsShowForUser) {
            PGLog.d('分类 ${localCategory.key} 的 isShowForUser 需要更新: '
                '当前=${localCategory.isShowForUser}, 期望=$expectedIsShowForUser');
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      PGLog.e('检查 isShowForUser 变更失败: $e');
      return true; // 出错时默认需要迁移
    }
  }

  /// 获取迁移统计信息
  ///
  /// [beforeConfig] 迁移前的配置
  /// [afterConfig] 迁移后的配置
  /// 返回迁移统计信息
  static Map<String, dynamic> getMigrationStats(
    SettingConfig beforeConfig,
    SettingConfig afterConfig,
  ) {
    final beforeKeys = beforeConfig.categories.map((c) => c.key).toSet();
    final afterKeys = afterConfig.categories.map((c) => c.key).toSet();

    final removedCategories = beforeKeys.difference(afterKeys).toList();
    final addedCategories = afterKeys.difference(beforeKeys).toList();
    final retainedCategories = beforeKeys.intersection(afterKeys).toList();

    return {
      'beforeCount': beforeConfig.categories.length,
      'afterCount': afterConfig.categories.length,
      'removedCategories': removedCategories,
      'addedCategories': addedCategories,
      'retainedCategories': retainedCategories,
      'removedCount': removedCategories.length,
      'addedCount': addedCategories.length,
      'retainedCount': retainedCategories.length,
    };
  }
}
