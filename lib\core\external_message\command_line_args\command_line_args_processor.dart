import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 命令行参数处理器
/// 将命令行参数转换为外部消息格式，与ExternalMessageManager集成
class CommandLineArgsProcessor {
  /// 处理命令行参数，转换为外部消息
  static ExternalMessage? processCommandLineArgs(List<String> args) {
    if (args.isEmpty) {
      return null;
    }

    PGLog.d('开始处理命令行参数: $args');

    // 处理dart-entrypoint-args传递的单个字符串情况
    List<String> processedArgs;
    if (args.length == 1 && args[0].contains(' ')) {
      // 如果只有一个参数且包含空格，说明是dart-entrypoint-args传递的完整字符串
      processedArgs = _splitCommandLineString(args[0]);
      PGLog.d('检测到单个字符串参数，分割后: $processedArgs');
    } else {
      // 正常的参数数组
      processedArgs = args;
    }

    // 解析命令行参数
    final parsedArgs = _parseArgs(processedArgs);

    // 根据参数类型创建相应的外部消息
    return _createExternalMessage(parsedArgs);
  }

  /// 分割命令行字符串为参数数组
  static List<String> _splitCommandLineString(String commandLine) {
    final List<String> result = [];
    final StringBuffer currentArg = StringBuffer();
    bool inQuotes = false;
    bool escapeNext = false;

    for (int i = 0; i < commandLine.length; i++) {
      final char = commandLine[i];

      if (escapeNext) {
        currentArg.write(char);
        escapeNext = false;
        continue;
      }

      if (char == '\\') {
        // 检查下一个字符是否需要转义
        if (i + 1 < commandLine.length) {
          final nextChar = commandLine[i + 1];
          // 只有在特定字符前的反斜杠才被视为转义字符
          if (nextChar == '"' ||
              nextChar == "'" ||
              nextChar == '\\' ||
              nextChar == ' ') {
            escapeNext = true;
            continue;
          }
        }
        // 否则，反斜杠作为普通字符处理（如Windows路径）
        currentArg.write(char);
        continue;
      }

      if (char == '"' || char == "'") {
        inQuotes = !inQuotes;
        continue;
      }

      if (char == ' ' && !inQuotes) {
        if (currentArg.isNotEmpty) {
          result.add(currentArg.toString());
          currentArg.clear();
        }
        continue;
      }

      currentArg.write(char);
    }

    if (currentArg.isNotEmpty) {
      result.add(currentArg.toString());
    }

    return result;
  }

  /// 解析命令行参数
  static _ParsedArgs _parseArgs(List<String> args) {
    final parsedArgs = _ParsedArgs();

    for (int i = 0; i < args.length; i++) {
      final arg = args[i];

      if (arg.startsWith('--')) {
        // 处理长选项
        _processLongOption(arg, args, i, parsedArgs);
      } else if (arg.startsWith('-')) {
        // 处理短选项
        _processShortOption(arg, args, i, parsedArgs);
      } else {
        // 位置参数，可能是文件路径
        _processPositionalArg(arg, parsedArgs);
      }
    }

    return parsedArgs;
  }

  /// 处理长选项
  static void _processLongOption(
      String arg, List<String> args, int index, _ParsedArgs parsedArgs) {
    final parts = arg.split('=');
    if (parts.length == 2) {
      // 格式: --key=value
      final key = parts[0];
      final value = parts[1];
      _setArgValue(key, value, parsedArgs);
    } else if (index + 1 < args.length && !args[index + 1].startsWith('-')) {
      // 格式: --key value
      final value = args[index + 1];
      _setArgValue(arg, value, parsedArgs);
    } else {
      // 格式: --flag
      _setFlag(arg, parsedArgs);
    }
  }

  /// 处理短选项
  static void _processShortOption(
      String arg, List<String> args, int index, _ParsedArgs parsedArgs) {
    if (index + 1 < args.length && !args[index + 1].startsWith('-')) {
      final value = args[index + 1];
      _setArgValue(arg, value, parsedArgs);
    } else {
      _setFlag(arg, parsedArgs);
    }
  }

  /// 处理位置参数
  static void _processPositionalArg(String arg, _ParsedArgs parsedArgs) {
    // 检查是否是文件路径
    if (arg.contains('\\') || arg.contains('/') || arg.contains('.')) {
      // 可能是文件路径
      parsedArgs.filePaths.add(arg);
    } else {
      parsedArgs.positionalArgs.add(arg);
    }
  }

  /// 设置参数值
  static void _setArgValue(String key, String value, _ParsedArgs parsedArgs) {
    switch (key) {
      case '--action':
        parsedArgs.action = value;
        break;
      case '--file':
      case '-f':
        // 自动检测是否包含管道符分割符
        if (value.contains('|')) {
          _parseFileWithHistory(value, parsedArgs);
        } else {
          // 普通文件路径
          parsedArgs.filePaths.add(value);
        }
        break;
      case '--project':
      case '-p':
        parsedArgs.projectId = value;
        break;
      case '--name':
      case '-n':
        parsedArgs.projectName = value;
        break;
      case '--tapj':
        parsedArgs.tapjFilePath = value;
        break;
      case '--images':
        // 处理图像文件路径列表（使用分号分隔）
        if (value.contains('|')) {
          parsedArgs.imagePaths.addAll(value.split('|'));
        } else {
          parsedArgs.imagePaths.add(value);
        }
        break;
      default:
        parsedArgs.customArgs[key] = value;
        break;
    }
  }

  /// 解析带历史ID的文件参数
  /// 使用统一的FileItem.fromString方法
  static void _parseFileWithHistory(String value, _ParsedArgs parsedArgs) {
    final fileItem = FileItem.fromString(value);
    if (fileItem != null) {
      parsedArgs.fileItems.add(_FileItemArgs(
        path: fileItem.originalPath,
        historyId: fileItem.historyId,
        isSelected: fileItem.isSelected,
      ));
    }
  }

  /// 设置标志
  static void _setFlag(String flag, _ParsedArgs parsedArgs) {
    switch (flag) {
      case '--smart':
        parsedArgs.shouldSmart = true;
        break;
      case '--auto-navigate':
        parsedArgs.autoNavigate = true;
        break;
      case '--newProject':
        parsedArgs.newProject = true;
        break;
      default:
        parsedArgs.flags.add(flag);
        break;
    }
  }

  /// 创建外部消息
  static ExternalMessage? _createExternalMessage(_ParsedArgs parsedArgs) {
    // 如果有tapj文件路径，直接创建包含tapj字段的导入消息
    if (parsedArgs.tapjFilePath != null &&
        parsedArgs.tapjFilePath!.isNotEmpty) {
      final data = <String, dynamic>{'tapj': parsedArgs.tapjFilePath!};

      // 如果有newProject标志，添加到data中
      if (parsedArgs.newProject) {
        data['newProject'] = true;
      }

      return ExternalMessage(
        type: ExternalMessageType.importProject,
        data: data,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );
    }

    // 检查是否有图像路径用于智能导入
    if (parsedArgs.shouldSmart && parsedArgs.imagePaths.isNotEmpty) {
      return _createSmartMessage(parsedArgs);
    }

    // 检查是否有文件路径或文件项
    if (parsedArgs.filePaths.isEmpty && parsedArgs.fileItems.isEmpty) {
      PGLog.w('没有指定文件路径或文件项，忽略命令行参数');
      return null;
    }
    return null;
  }

  /// 创建智能导入消息
  static ExternalMessage _createSmartMessage(_ParsedArgs parsedArgs) {
    final smartData = SmartData.fromStringPaths(
      imagePaths: parsedArgs.imagePaths,
      projectName: parsedArgs.projectName,
      autoNavigate: parsedArgs.autoNavigate,
      newProject: parsedArgs.newProject,
    );

    return ExternalMessage(
      type: ExternalMessageType.smart,
      data: smartData.toJson(),
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }
}

/// 解析后的命令行参数
class _ParsedArgs {
  String? action;
  String? projectId;
  String? projectName;
  List<String> filePaths = [];
  List<String> positionalArgs = [];
  List<String> flags = [];
  Map<String, String> customArgs = {};

  bool shouldSmart = false;
  bool autoNavigate = false;
  bool newProject = false;
  List<_FileItemArgs> fileItems = [];
  String? tapjFilePath; // tapj文件路径
  List<String> imagePaths = []; // 图像文件路径列表
}

/// 文件项参数
class _FileItemArgs {
  final String path;
  final String? historyId;
  final bool isSelected;

  _FileItemArgs({
    required this.path,
    this.historyId,
    this.isSelected = false,
  });
}
