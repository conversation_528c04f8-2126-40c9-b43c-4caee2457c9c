import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 获得当前导出进度的实现
/// 暂定通过本地文件地址映射的方式实现
class ExportRecordsRepositoryImpl implements ExportRecordsRepository {
  static const String _dummyPath = 'assets/static/dummy_export_tasks.json';

  @override
  Future<List<ExportRecord>> fetchExportRecord() async {
    try {
      final jsonStr = await rootBundle.loadString(_dummyPath);
      final List<dynamic> jsonList = jsonDecode(jsonStr);
      return jsonList.map((e) => ExportRecord.fromJson(e)).toList();
    } catch (e) {
      PGLog.e("加载测试数据失败: $e");
      return [];
    }
  }

  @override
  Future<void> deleteExportRecord(String guid) async {
    // TODO: 修改Json中的记录实现删除导出记录
  }

  @override
  Future<void> updateExportRecord(ExportRecord record) async {
    // TODO: 修改Json中的记录实现更新导出记录
  }

  @override
  Future<List<ExportRecord>> fetchExportRecordsByGuids(
    List<String> guids,
  ) async {
    // TODO: 解析Json中的记录实现获得导出记录
    return [];
  }

  @override
  Future<List<String>> fetchExportTaskFilesFinalPath(
      String exportTaskId) async {
    // TODO: 解析Json中的记录实现获得导出任务的所有目标地址
    return [];
  }
}
