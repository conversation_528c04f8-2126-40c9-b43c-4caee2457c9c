// 自动生成的FFI绑定文件
// 请勿手动修改此文件

import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/models/raw_decoder_model.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

// FFI函数签名定义
typedef RawDecoderInitNative = Int32 Function(
    Int32 numThreads, Pointer<Utf8> bundlePath, Pointer<Utf8> key);
typedef RawDecoderInitDart = int Function(
    int numThreads, Pointer<Utf8> bundlePath, Pointer<Utf8> key);

typedef RawDecoderProcessNative = Int32 Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<RawDecoderOptions> options,
    Pointer<Utf8> errorMessage,
    Int32 errorMessageSize);
typedef RawDecoderProcessDart = int Function(
    Pointer<Utf8> inputFile,
    Pointer<Utf8> outputFile,
    Pointer<RawDecoderOptions> options,
    Pointer<Utf8> errorMessage,
    int errorMessageSize);

typedef RawDecoderFreeNative = Void Function();
typedef RawDecoderFreeDart = void Function();

typedef RawDecoderGetDefaultOptionsNative = Void Function(
    Pointer<RawDecoderOptions> options);
typedef RawDecoderGetDefaultOptionsDart = void Function(
    Pointer<RawDecoderOptions> options);

typedef RawDecoderIsInitializedNative = Int32 Function();
typedef RawDecoderIsInitializedDart = int Function();

// 动态库加载和函数绑定
class RawDecoderBindings {
  static DynamicLibrary? _dylib;
  static late RawDecoderInitDart _rawDecoderInit;
  static late RawDecoderProcessDart _rawDecoderProcess;
  static late RawDecoderFreeDart _rawDecoderFree;
  static late RawDecoderGetDefaultOptionsDart _rawDecoderGetDefaultOptions;
  static late RawDecoderIsInitializedDart _rawDecoderIsInitialized;

  static void _loadLibrary() {
    if (_dylib != null) return;

    _dylib = UniversalPlatformLoader.loadSugoiNativeLibrary();

    // 绑定函数
    _rawDecoderInit = _dylib!
        .lookup<NativeFunction<RawDecoderInitNative>>('RawDecoder_Init')
        .asFunction<RawDecoderInitDart>();

    _rawDecoderProcess = _dylib!
        .lookup<NativeFunction<RawDecoderProcessNative>>('RawDecoder_Process')
        .asFunction<RawDecoderProcessDart>();

    _rawDecoderFree = _dylib!
        .lookup<NativeFunction<RawDecoderFreeNative>>('RawDecoder_Free')
        .asFunction<RawDecoderFreeDart>();

    _rawDecoderGetDefaultOptions = _dylib!
        .lookup<NativeFunction<RawDecoderGetDefaultOptionsNative>>(
            'RawDecoder_GetDefaultOptions')
        .asFunction<RawDecoderGetDefaultOptionsDart>();

    _rawDecoderIsInitialized = _dylib!
        .lookup<NativeFunction<RawDecoderIsInitializedNative>>(
            'RawDecoder_IsInitialized')
        .asFunction<RawDecoderIsInitializedDart>();
  }

  // 公共接口
  static int init(int numThreads, String? bundlePath, String? key) {
    _loadLibrary();
    final bundlePathPtr = bundlePath?.toNativeUtf8() ?? nullptr;
    final keyPtr = key?.toNativeUtf8() ?? nullptr;
    try {
      return _rawDecoderInit(numThreads, bundlePathPtr, keyPtr);
    } finally {
      if (bundlePathPtr != nullptr) {
        malloc.free(bundlePathPtr);
      }
    }
  }

  static int process(
    String inputFile,
    String outputFile,
    Pointer<RawDecoderOptions>? options,
    int errorMessageSize,
  ) {
    _loadLibrary();
    final inputFilePtr = inputFile.toNativeUtf8();
    final outputFilePtr = outputFile.toNativeUtf8();
    final errorMessagePtr = malloc.allocate<Utf8>(errorMessageSize);

    try {
      return _rawDecoderProcess(
        inputFilePtr,
        outputFilePtr,
        options ?? nullptr,
        errorMessagePtr,
        errorMessageSize,
      );
    } catch (e) {
      PGLog.e('RawDecoderBindings process error: $e');
      return RawDecoderErrorCode.errorUnknown;
    } finally {
      malloc.free(inputFilePtr);
      malloc.free(outputFilePtr);
      malloc.free(errorMessagePtr);
    }
  }

  static void free() {
    _loadLibrary();
    _rawDecoderFree();
  }

  static void getDefaultOptions(Pointer<RawDecoderOptions> options) {
    _loadLibrary();
    _rawDecoderGetDefaultOptions(options);
  }

  static bool isInitialized() {
    _loadLibrary();
    return _rawDecoderIsInitialized() == 1;
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo() {
    return UniversalPlatformLoader.getLibraryInfo(
      'PGRawDecoder',
      subDirectory: 'raw_decoder',
    );
  }
}
