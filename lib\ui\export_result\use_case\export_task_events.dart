import 'package:turing_art/utils/pg_log.dart';

/// 导出任务事件类型
enum ExportTaskEventType {
  /// 暂停所有导出任务
  pauseAll,

  /// 恢复之前暂停的导出任务
  resumePaused,
}

/// 导出任务事件基类
abstract class ExportTaskEvent {
  final ExportTaskEventType type;
  final String source;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const ExportTaskEvent({
    required this.type,
    required this.source,
    required this.timestamp,
    this.metadata,
  });

  @override
  String toString() {
    return 'ExportTaskEvent{type: $type, source: $source, timestamp: $timestamp, metadata: $metadata}';
  }
}

/// 暂停所有导出任务事件
class PauseAllExportTasksEvent extends ExportTaskEvent {
  PauseAllExportTasksEvent({
    required super.source,
    super.metadata,
  }) : super(
          type: ExportTaskEventType.pauseAll,
          timestamp: DateTime.now(),
        );
}

/// 恢复暂停的导出任务事件
class ResumePausedExportTasksEvent extends ExportTaskEvent {
  ResumePausedExportTasksEvent({
    required super.source,
    super.metadata,
  }) : super(
          type: ExportTaskEventType.resumePaused,
          timestamp: DateTime.now(),
        );
}

/// 导出任务事件监听器接口
abstract class ExportTaskEventListener {
  /// 处理导出任务事件
  Future<void> onExportTaskEvent(ExportTaskEvent event);
}

/// 导出任务事件总线
/// 负责分发导出任务相关的事件
class ExportTaskEventBus {
  static final ExportTaskEventBus _instance = ExportTaskEventBus._internal();
  factory ExportTaskEventBus() => _instance;
  ExportTaskEventBus._internal();

  final List<ExportTaskEventListener> _eventListeners = [];
  final List<ExportTaskEvent> _eventHistory = [];

  /// 最大历史记录数量
  static const int _maxHistorySize = 100;

  /// 添加事件监听器
  void addEventListener(ExportTaskEventListener listener) {
    if (!_eventListeners.contains(listener)) {
      _eventListeners.add(listener);
      PGLog.d('ExportTaskEventBus: 添加事件监听器: ${listener.runtimeType}');
    }
  }

  /// 移除事件监听器
  void removeEventListener(ExportTaskEventListener listener) {
    _eventListeners.remove(listener);
    PGLog.d('ExportTaskEventBus: 移除事件监听器: ${listener.runtimeType}');
  }

  /// 发布事件
  Future<void> publish(ExportTaskEvent event) async {
    try {
      PGLog.d('ExportTaskEventBus: 发布事件: $event');

      // 添加到历史记录
      _addToHistory(event);

      // 通知所有事件监听器
      for (final listener in _eventListeners) {
        try {
          await listener.onExportTaskEvent(event);
        } catch (e) {
          PGLog.e(
              'ExportTaskEventBus: 事件监听器处理事件失败: ${listener.runtimeType}, 错误: $e');
        }
      }

      PGLog.d('ExportTaskEventBus: 事件处理完成: $event');
    } catch (e) {
      PGLog.e('ExportTaskEventBus: 发布事件失败: $e');
    }
  }

  /// 发布暂停所有导出任务事件
  Future<void> pauseAllExportTasks({
    required String source,
    Map<String, dynamic>? metadata,
  }) async {
    final event = PauseAllExportTasksEvent(
      source: source,
      metadata: metadata,
    );
    await publish(event);
  }

  /// 发布恢复暂停的导出任务事件
  Future<void> resumePausedExportTasks({
    required String source,
    Map<String, dynamic>? metadata,
  }) async {
    final event = ResumePausedExportTasksEvent(
      source: source,
      metadata: metadata,
    );
    await publish(event);
  }

  /// 添加事件到历史记录
  void _addToHistory(ExportTaskEvent event) {
    _eventHistory.add(event);
    if (_eventHistory.length > _maxHistorySize) {
      _eventHistory.removeAt(0);
    }
  }

  /// 获取事件历史记录
  List<ExportTaskEvent> get eventHistory => List.unmodifiable(_eventHistory);

  /// 清空事件历史记录
  void clearHistory() {
    _eventHistory.clear();
  }

  /// 获取指定类型的事件历史记录
  List<ExportTaskEvent> getEventsByType(ExportTaskEventType type) {
    return _eventHistory.where((event) => event.type == type).toList();
  }

  /// 获取指定来源的事件历史记录
  List<ExportTaskEvent> getEventsBySource(String source) {
    return _eventHistory.where((event) => event.source == source).toList();
  }
}

/// 导出任务事件源常量
class ExportTaskEventSource {
  /// 项目状态变化
  static const String projectStateChange = 'project_state_change';

  /// 缓存清理
  static const String cacheCleanup = 'cache_cleanup';

  /// 手动操作
  static const String manualOperation = 'manual_operation';

  /// 系统维护
  static const String systemMaintenance = 'system_maintenance';

  /// 其他来源
  static const String other = 'other';
}
