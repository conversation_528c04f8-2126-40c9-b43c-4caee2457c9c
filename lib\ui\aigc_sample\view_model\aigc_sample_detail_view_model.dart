import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_path_info.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_models.dart';
import 'package:turing_art/datalayer/domain/enums/export_path_type.dart';
import 'package:turing_art/datalayer/domain/enums/export_status.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/api/common_error_handler.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/aigc_sample_detail_polling_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_sample/services/file_path_service.dart';
import 'package:turing_art/ui/aigc_sample/services/folder_picker_service.dart';
import 'package:turing_art/ui/export_result/handler/export_path_viewer_handler.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcSampleDetailViewModel extends ChangeNotifier {
  // 数据仓库
  final AigcSampleRepository _repository;
  final AccountRepository _accountRepository;
  final MediaUploadRepository _uploadRepository;
  final MediaRepository _mediaRepository;
  final AigcSampleDetailPollingProvider _pollingProvider;
  final NetworkProvider _networkProvider;
  final OpsCustomTableRepository _customTableRepository;

  // 处理服务
  final AigcService _processingService;

  // 导出管理器
  final AigcMySampleExportManager _exportManager;

  // 导出路径查看处理器
  late final ExportPathViewerHandler _exportPathViewerHandler;

  // 详情弹窗关闭回调
  final VoidCallback? _onDetailClosed;
  // 再次打样回调
  final VoidCallback? _onAgainButtonClicked;
  // 导出按钮点击回调
  final VoidCallback? _onExportButtonClicked;

  // 当前选中的效果索引
  int _selectedEffectIndex = 0;
  int get selectedEffectIndex => _selectedEffectIndex;

  // 文件打开服务
  final FilePathService _filePathService = FilePathService.forPlatform();
  // 文件夹选择服务
  final FolderPickerService _folderPickerService =
      FolderPickerService.forPlatform();

  // 打样详情数据变化监听器
  StreamSubscription<String>? _dataChangeSubscription;
  // 大图任务监听器
  StreamSubscription<AigcTaskResultMessage>? _largeImageStreamSubscription;
  // 重试导出刷新事件监听器
  StreamSubscription<RetryExportRefreshEvent>? _retryRefreshSubscription;

  // 当前效果
  AigcSampleEffectModel? get currentEffect {
    if (currentSampleDetail != null &&
        selectedEffectIndex < currentSampleDetail!.effects.length) {
      return currentSampleDetail!.effects[selectedEffectIndex];
    }
    return null;
  }

  // 当前样片详情
  AigcSampleModel? _currentSampleDetail;
  AigcSampleModel? get currentSampleDetail => _currentSampleDetail;

  // 当前样片id
  final String _currentProofingId;
  String get currentProofingId => _currentProofingId;

  // 导出loading状态
  bool _isLoding = false;
  bool get isLoding => _isLoding;
  String? _errorTip;
  String? get errorTip => _errorTip;

  // 重要提示信息（确保显示的toast）
  String? _importantTip;
  String? get importantTip => _importantTip;
  bool _importantTipShown = false; // 标记重要提示是否已显示

  int _proofingPerEffectCost = 0;
  int _exportCost = 0;
  int get proofingCost => _proofingPerEffectCost * 3; // 打样3张效果图消耗的积分
  int get exportCost => _exportCost;

  ExportStatus get currentExportStatus {
    if (selectedEffectIndex < currentSampleDetail!.effects.length) {
      return ExportStatus.fromString(
          currentSampleDetail!.effects[selectedEffectIndex].exportStatus);
    }
    return ExportStatus.unexported;
  }

  // 第一次外部传入样片详情，不用请求
  AigcSampleDetailViewModel({
    required AigcSampleRepository repository,
    required AccountRepository accountRepository,
    required MediaUploadRepository uploadRepository,
    required MediaRepository mediaRepository,
    required AigcService processingService,
    required AigcMySampleExportManager exportManager,
    required String proofingId,
    required AigcSampleDetailPollingProvider pollingProvider,
    required NetworkProvider networkProvider,
    required OpsCustomTableRepository customTableRepository,
    AigcSampleModel? sampleModel,
    VoidCallback? onDetailClosed,
    VoidCallback? onAgainButtonClicked,
    VoidCallback? onExportButtonClicked,
  })  : _repository = repository,
        _accountRepository = accountRepository,
        _uploadRepository = uploadRepository,
        _mediaRepository = mediaRepository,
        _processingService = processingService,
        _exportManager = exportManager,
        _currentSampleDetail = sampleModel,
        _currentProofingId = proofingId,
        _pollingProvider = pollingProvider,
        _networkProvider = networkProvider,
        _customTableRepository = customTableRepository,
        _onDetailClosed = onDetailClosed,
        _onAgainButtonClicked = onAgainButtonClicked,
        _onExportButtonClicked = onExportButtonClicked {
    // 初始化导出路径查看处理器
    _exportPathViewerHandler = ExportPathViewerHandler(
      _exportManager,
      _filePathService,
    );
    _fetchAigcPointConfig();
    _initializeProofingDetailDataStream();
    _initializeLargeImageStream();
    _initializeRetryRefreshStream();
    // 加载详情,保持刷新
    _loadSampleDetail();
  }

  Future<void> _fetchAigcPointConfig() async {
    try {
      final config = await _customTableRepository.getAigcPointConfig();
      _proofingPerEffectCost =
          config?.points?.firstWhere((e) => e.type == 'proofing').value ?? 0;
      _exportCost =
          config?.points?.firstWhere((e) => e.type == 'export').value ?? 0;
      notifyListeners();
    } catch (e) {
      // ignore
    }
  }

  /// 初始化数据流监听
  void _initializeProofingDetailDataStream() {
    _dataChangeSubscription = _repository.dataChangeStream.listen((eventType) {
      // 检查是否是详情刷新事件
      if (eventType.startsWith(AigcRequestConst.detailRefresh)) {
        // 解析事件中的proofingId
        final parts = eventType.split(':');
        if (parts.length > 1) {
          final proofingId = parts[1];
          // 只处理当前正在查看的proofingId的更新
          if (proofingId != _currentProofingId) {
            PGLog.d(
                '收到非当前查看的详情刷新事件，忽略: $proofingId, 当前查看的详情ID: $_currentProofingId');
            return;
          }
        }

        PGLog.d('收到详情当前展示的打样详情刷新事件，更新UI');

        // 1比较当前详情和缓存中的最新详情（详情改变则赋值最新）
        if (!isDetailChanged()) {
          return;
        }

        // 2先停止轮询，获取最新数据后会重新检查是否需要轮询
        _pollingProvider.stopPolling();

        // 3通知监听器刷新UI
        notifyListeners();

        // 4检查是否需要重新开始轮询
        _checkDetailPollingStatus();
      } else if (eventType == AigcRequestConst.detailRefreshFailForNotExist) {
        // 样片不存在，停止轮询，并提示
        _pollingProvider.stopPolling();
        _stopLoading('打样结果已被删除，请刷新页面');
      }
    });
  }

  /// 设置导出需要上传大图任务监听器
  void _initializeLargeImageStream() {
    PGLog.d('AigcSampleDetailViewModel: 设置导出需要上传大图任务监听器');

    _largeImageStreamSubscription =
        _processingService.resultStream.listen((message) {
      PGLog.d(
          'AigcSampleDetailViewModel: 收到结果消息 - processorKey: ${message.processorKey}, payload: ${message.payload.inputPath}');

      // 从payload中获取任务类型
      final taskType = message.payload.taskType;
      if (taskType == AigcTaskType.image) {
        PGLog.d(
            'AigcSampleDetailViewModel: 处理导出需要上传大图任务结果 - success: ${message.success}, resultData: ${message.resultData}');
        _onLargeImageUpdated(message);
      }
    });
  }

  /// 初始化重试导出刷新事件监听
  void _initializeRetryRefreshStream() {
    _retryRefreshSubscription =
        _exportManager.retryRefreshStream.listen((event) {
      // 只有当前详情页面的打样ID在重试列表中时才刷新
      if (event.proofingIds.contains(_currentProofingId)) {
        PGLog.d('收到重试导出刷新事件，当前打样ID: $_currentProofingId，开始刷新详情');
        _loadSampleDetail();
      }
    });
  }

  /// 处理导出需要上传大图任务结果
  Future<void> _onLargeImageUpdated(AigcTaskResultMessage message) async {
    if (message.success) {
      // 1.上传大图
      final success = await _uploadLargeImage(message.payload.outputPath);
      if (success) {
        // 2.继续导出
        await _continueExport();
      } else {
        // 上传失败，重置loading状态
        _stopLoading('上传大图失败,请稍后重试');
      }
    } else {
      // 大图任务失败，重置loading状态
      _stopLoading('获取大图失败,请稍后重试');
    }
  }

  bool isDetailChanged() {
    final latestDetail =
        _repository.getLocalCacheSampleDetail(_currentProofingId);
    if (latestDetail == null) {
      PGLog.e('缓存中未找到详情数据，无法更新UI');
      return false;
    }
    PGLog.d('当前详情: $_currentSampleDetail, 最新详情: $latestDetail');
    // 2.1检查详情是否有变化
    if (_currentSampleDetail != null &&
        !_currentSampleDetail!.hasStateChanged(latestDetail)) {
      PGLog.d('详情数据未发生变化，无需更新UI');
      return false;
    }
    PGLog.d('详情数据发生变化，需更新UI');

    // 有变化的前提下，检查是否包含失败状态，需要退钱
    if (latestDetail.status == AigcRequestConst.failed ||
        latestDetail.effects
            .any((e) => e.exportStatus == AigcRequestConst.failed)) {
      _accountRepository.refreshAllAccount().catchError((e) {
        PGLog.e('刷新账户信息失败: $e');
      });
    }

    _currentSampleDetail = latestDetail;
    return true;
  }

  /// 加载样片详情
  Future<void> _loadSampleDetail({bool isDeleteRefresh = false}) async {
    try {
      _startLoading();
      // 1.获取最新数据
      final detail = await _repository.getAigcSampleDetail(_currentProofingId);

      // 检查详情是否有变化
      if (_currentSampleDetail != null &&
          !_currentSampleDetail!.hasStateChanged(detail)) {
        PGLog.d('详情数据未发生变化，无需更新UI');
        // 仍然需要检查是否需要轮询
        _checkDetailPollingStatus();
        _stopLoading(null);
        return;
      }
      PGLog.d('详情数据发生变化，需更新UI, 当前详情: $_currentSampleDetail, 最新详情: $detail');

      // 更新当前详情数据
      _currentSampleDetail = detail;

      // 2.检查是否需要开始轮询
      _checkDetailPollingStatus();

      // 3.通知监听器刷新UI
      notifyListeners();
      _stopLoading(null);
    } catch (e) {
      PGLog.e("加载样片详情失败: $e");
      // 不是样片不存在的情况，则自己提示错误
      final (errorMessage, erroType) =
          CommonBusinessErrorHandler.handleError(e, '加载样片详情');
      _stopLoading(errorMessage);
      if (erroType == BusinessErrorCode.sampleNotExist) {
        // 样片不存在，停止轮询，则关闭弹窗
        if (isDeleteRefresh) {
          onDetailClosed();
        }
      }
    }
  }

  /// 设置选中的效果索引
  void setSelectedEffectIndex(int index) {
    _selectedEffectIndex = index;
    notifyListeners();
  }

  /// 重新生成AIGC样本
  Future<void> regenerateAigcSample(String proofingId) async {
    try {
      _startLoading();
      await _repository.regenerateAigcSample(proofingId);
      // 重新加载样片详情以更新状态
      await _loadSampleDetail();
      _stopLoading(null);
      // 消费后刷新账户信息（与当前UI无关，也不需要等待）
      _accountRepository.refreshAllAccount();
    } catch (e) {
      PGLog.e("重新生成AIGC样本失败: $e");
      final (errorMessage, _) =
          CommonBusinessErrorHandler.handleError(e, '重新生成打样');
      _stopLoading(errorMessage);
    }
  }

  /// 检查详情轮询状态
  void _checkDetailPollingStatus() {
    if (_currentSampleDetail == null) {
      return;
    }

    // 检查是否有任何effect的photo_url或thumb_url为空
    bool needPolling = false;
    for (final effect in _currentSampleDetail!.effects) {
      final exportStatus = ExportStatus.fromString(effect.exportStatus);
      // 新生成需要轮询，导出状态running，则需要轮询（failed不需要轮询，因为失败后UI上展示叹号）
      if (effect.photoUrl.isEmpty ||
          effect.thumbUrl.isEmpty ||
          exportStatus == ExportStatus.running) {
        needPolling = true;
        break;
      }
    }

    if (needPolling) {
      _pollingProvider.startPolling(_currentProofingId);
      PGLog.d('开始轮询详情，ID: $_currentProofingId');
    } else {
      _pollingProvider.stopPolling();
      PGLog.d('停止轮询详情，所有效果图已完成');
    }
  }

  bool isExportButtonEnabled() {
    if (currentEffect == null) {
      return false;
    }
    // 打样中
    if (currentEffect!.photoUrl.isEmpty) {
      return false;
    }
    // 正在导出中
    if (currentExportStatus == ExportStatus.running) {
      return false;
    }
    return true;
  }

  /// 获取导出路径类型
  ExportPathType getExportPathType() {
    return UserPreferencesService.getExportPathType();
  }

  /// 设置导出路径类型
  void setExportPathType(ExportPathType pathType) {
    UserPreferencesService.setExportPathType(pathType);
    notifyListeners();
  }

  /// 获取自定义导出路径
  String getCustomExportPath() {
    return UserPreferencesService.getCustomExportPath();
  }

  /// 设置自定义导出路径
  void setCustomExportPath(String path) {
    UserPreferencesService.setCustomExportPath(path);
    notifyListeners();
  }

  /// 获取当前导出路径显示的标题
  String getExportPathTitle() {
    return getExportPathType().description;
  }

  /// 获取当前实际导出路径(查看文件夹时使用)
  Future<CheckPathResult> getActualExportPath() async {
    final exportPathInfo = ExportPathInfo.fromSampleModel(
        _currentSampleDetail!, currentEffect!.effectCode);
    return await _exportManager.getProofingExportFilePath(
        _currentSampleDetail!.projectId, exportPathInfo);
  }

  /// 处理导出前的路径检查和初始化
  /// 如果用户没有设置自定义导出路径，则使用桌面路径作为默认路径
  /// 同时设置不再显示导出路径对话框
  Future<void> handleExportWithPathCheck() async {
    // 检查是否已设置自定义导出路径
    String customPath = UserPreferencesService.getCustomExportPath();

    if (customPath.isEmpty) {
      // 如果没有设置自定义路径，使用桌面路径作为默认路径
      final desktopPath = await _filePathService.getDesktopPath();
      UserPreferencesService.setCustomExportPath(desktopPath);
      PGLog.d('用户未设置自定义导出路径，使用桌面路径作为默认路径: $desktopPath');
    }

    // 设置不再显示导出路径对话框
    UserPreferencesService.setShowExportPathDialog(show: false);
  }

  /// 导出AIGC样本
  /// 1.检查是否需要上传3072大图，并上传
  /// 2.调用导出接口
  /// 3.刷新导出管理器（加入轮询下载队列）
  /// 4.刷新当前样片详情UI,刷新导出状态
  Future<void> exportAigcSample() async {
    if (currentSampleDetail == null || !isExportButtonEnabled() || _isLoding) {
      return;
    }

    // 开始导出，设置loading状态
    _startLoading();

    try {
      // 1.检查是否需要上传3072大图，并上传
      if (!currentSampleDetail!.isLargeImageUploaded) {
        // 没有上传，提交获取大图任务
        await _submitLargeImageTask();
      } else {
        // 已经上传，继续导出
        await _continueExport();
      }
    } catch (e) {
      PGLog.e("导出AIGC样本失败: $e");
      _handleExportError(e);
    }
  }

  /// 上传大图
  Future<bool> _uploadLargeImage(String imagePath) async {
    final imageFile = File(imagePath);
    final imageUploadResult =
        await _uploadRepository.uploadSingleFile(imageFile);

    final uploadImageUrl = imageUploadResult.publicUrl;

    if (!imageUploadResult.success || uploadImageUrl == null) {
      PGLog.e('上传原图失败: ${imageUploadResult.error}');
      return false;
    }
    // 3.更新样片详情
    await _repository.updateAigcSampleInfo(_currentProofingId, uploadImageUrl);
    return true;
  }

  /// 继续导出
  Future<void> _continueExport() async {
    try {
      // 2.调用导出接口
      final effectCode = currentEffect!.effectCode;
      await _repository.exportAigcSample(_currentProofingId, effectCode);
      // 3.刷新导出管理器（加入轮询下载队列）
      _exportManager.refreshExportList();
      // 调用回调，触发列表刷新
      _onExportButtonClicked?.call();
      // 4.刷新当前样片详情UI,刷新导出状态
      await _loadSampleDetail();
      // 导出成功，重置loading状态
      _stopLoading(null);
    } catch (e) {
      PGLog.e("导出失败: $e");
      _handleExportError(e);
    }
  }

  void _handleExportError(Object e) {
    final (errorMessage, errorCode) =
        CommonBusinessErrorHandler.handleError(e, '导出');
    _stopLoading(errorMessage);
    // 重复导出错误，设置重要提示确保显示
    if (errorCode == BusinessErrorCode.deviceExported) {
      _setImportantTip(errorMessage);
      _loadSampleDetail();
    }
  }

  void _startLoading() {
    _isLoding = true;
    notifyListeners();
  }

  Future<void> _stopLoading(String? errorTip) async {
    _isLoding = false;
    _errorTip = errorTip;
    final isNetworkConnected = await _networkProvider.isConnected();
    if (!isNetworkConnected) {
      _errorTip = '网络错误，请检查网络连接';
    }
    notifyListeners();
  }

  /// 清除导出提示
  void clearExportTip() {
    _errorTip = null;
  }

  /// 设置重要提示（确保显示的toast）
  void _setImportantTip(String tip) {
    _importantTip = tip;
    _importantTipShown = false;
    notifyListeners();
  }

  /// 标记重要提示已显示
  void markImportantTipShown() {
    _importantTipShown = true;
    _importantTip = null;
    notifyListeners();
  }

  /// 检查是否有未显示的重要提示
  bool hasUnshownImportantTip() {
    return _importantTip != null && !_importantTipShown;
  }

  /// 提交获取大图任务
  Future<void> _submitLargeImageTask() async {
    if (currentSampleDetail == null ||
        currentSampleDetail!.clientResourceId.isEmpty ||
        currentSampleDetail!.projectId.isEmpty) {
      _stopLoading('样片或者项目id不存在');
      return;
    }
    final resourceFile = _mediaRepository.getResourceFilePath(
      currentSampleDetail!.projectId,
      currentSampleDetail!.clientResourceId,
      MediaResourceType.largeResource,
    );
    final outputPath = resourceFile.path;

    String? inputPath;

    final originalResource = await _mediaRepository.getFileResourceAsync(
      currentSampleDetail!.projectId,
      currentSampleDetail!.clientResourceId,
      MediaResourceType.originalResource,
    );

    inputPath = originalResource?.path;
    if (inputPath == null || !File(inputPath).existsSync()) {
      // 原图不存在，则使用预览图
      final previewResource = _mediaRepository.getFileResource(
        currentSampleDetail!.projectId,
        currentSampleDetail!.clientResourceId,
        MediaResourceType.previewResource,
      );
      inputPath = previewResource?.path;
      if (inputPath == null || !File(inputPath).existsSync()) {
        _stopLoading('原图和预览图不存在,等待原作者上传');
        return;
      }
    }

    _processingService.submitTask(
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: currentSampleDetail!.clientResourceId,
      taskType: AigcTaskType.image,
      executeNow: true,
    );
  }

  /// 删除AIGC样本效果（详情里面的效果列表使用）
  Future<void> deleteAigcSampleEffect(
      String proofingId, String effectCode) async {
    try {
      _startLoading();
      await _repository.deleteAigcSampleEffect(proofingId, effectCode);
      // 重新加载样片详情以更新数据
      await _loadSampleDetail(isDeleteRefresh: true);

      // 删除后需要调整selectedEffectIndex，防止越界
      if (_currentSampleDetail != null &&
          _currentSampleDetail!.effects.isNotEmpty) {
        final maxIndex = _currentSampleDetail!.effects.length - 1;
        if (_selectedEffectIndex > maxIndex) {
          // 如果当前选中的索引超出范围，选择最后一个
          _selectedEffectIndex = maxIndex;
        }
      } else {
        // 如果没有效果了，重置为0
        _selectedEffectIndex = 0;
      }

      _stopLoading(null);
    } catch (e) {
      PGLog.e("删除AIGC样本效果失败: $e");
      final (errorMessage, _) =
          CommonBusinessErrorHandler.handleError(e, '删除AIGC样本效果');
      _stopLoading(errorMessage);
    }
  }

  /// 打开文件夹
  Future<bool> openFolder(String path) async {
    final success = await _filePathService.openFolder(path);
    PGLog.d('打开文件夹: $path, success: $success');
    return success;
  }

  /// 统一处理AIGC导出路径查看
  Future<void> handleExportPathView() async {
    if (_currentSampleDetail == null || currentEffect == null) {
      return;
    }
    final exportPathInfo = ExportPathInfo.fromSampleModel(
        _currentSampleDetail!, currentEffect!.effectCode);
    await _exportPathViewerHandler.handleAigcExportPathView(
        _currentSampleDetail!.projectId,
        exportPathInfo,
        currentEffect!.exportPhotoUrl);
  }

  // 查看或更改导出文件夹,返回是否需要刷新UI
  Future<(bool, String)> openOrChangeExportFolder() async {
    final currentPathType = getExportPathType();
    final checkPathResult = await getActualExportPath();
    final currentPath = checkPathResult.resultPath;
    String errorTip = '';
    if (!checkPathResult.isExpectedPathExists) {
      PGLog.w('预期导出路径不存在，使用桌面路径作为导出基础路径');
      errorTip = '预期导出路径不存在，使用桌面路径作为导出基础路径';
    }
    // 都是打开文件夹
    final folderPath = Directory(path.dirname(currentPath)).path;
    if (currentPathType == ExportPathType.original) {
      // 原图路径模式：只查看，不能更改)
      return (await _filePathService.openFolder(folderPath), errorTip);
    } else {
      // 自定义路径模式：可以更改,不需要提示错误
      final selectedDirectory = await _folderPickerService.pickFolder(
        dialogTitle: '选择自定义导出路径',
        initialDirectory: folderPath.isNotEmpty ? folderPath : null,
        lockParentWindow: true,
      );

      if (selectedDirectory != null) {
        // 用户选择了新路径，更新自定义路径
        setCustomExportPath(selectedDirectory);
        return (true, '');
      } else {
        // 用户取消了选择，不做任何操作
        return (false, '');
      }
    }
  }

  /// 详情弹窗关闭时调用
  void onDetailClosed() {
    // 停止轮询
    _pollingProvider.stopPolling();
    // 调用回调，触发列表刷新
    _onDetailClosed?.call();
  }

  /// 再次打样
  void onAgainButtonClicked() {
    _onAgainButtonClicked?.call();
  }

  /// 导出按钮点击
  void onExportButtonClicked() {
    _onExportButtonClicked?.call();
  }

  /// 检查网络连接状态
  Future<bool> checkNetworkConnection() async {
    return await _networkProvider.isConnected();
  }

  /// 在成功操作后刷新账户信息（不等待，避免影响UI）
  void refreshAccountAfterSuccess() {
    _accountRepository.refreshAllAccount().catchError((e) {
      PGLog.e('刷新账户信息失败: $e');
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription?.cancel();
    _largeImageStreamSubscription?.cancel();
    _retryRefreshSubscription?.cancel();
    super.dispose();
  }
}
