import 'dart:ui' as ui;

import 'package:flutter/material.dart';

/// 背景绘制策略接口
///
/// 遵循策略模式，定义不同的背景绘制算法
abstract class AigcBackgroundStrategy {
  /// 绘制背景
  /// [canvas] 画布
  /// [size] 绘制区域大小
  void drawBackground(Canvas canvas, Size size);
}

/// 纯色背景策略
class SolidColorBackgroundStrategy implements AigcBackgroundStrategy {
  /// 背景颜色
  final Color backgroundColor;

  SolidColorBackgroundStrategy(this.backgroundColor);

  @override
  void drawBackground(Canvas canvas, Size size) {
    final paint = Paint()..color = backgroundColor;
    canvas.drawRect(Offset.zero & size, paint);
  }
}

/// 图片背景绘制模式
enum ImageBackgroundMode {
  /// 拉伸模式 - 图片会被拉伸以填充整个区域
  stretch,

  /// 平铺模式 - 图片会被重复平铺以填充整个区域
  tile
}

/// 图片背景策略
class ImageBackgroundStrategy implements AigcBackgroundStrategy {
  /// 背景图片
  final ui.Image image;

  /// 图片适配模式
  final BoxFit fit;

  /// 图片背景绘制模式
  final ImageBackgroundMode mode;

  /// 是否使用透明paint颜色
  final bool useTransparentPaint;

  ImageBackgroundStrategy(
    this.image, {
    this.fit = BoxFit.cover,
    this.mode = ImageBackgroundMode.stretch,
    this.useTransparentPaint = false,
  });

  @override
  void drawBackground(Canvas canvas, Size size) {
    final paint = Paint();

    // 根据useTransparentPaint参数决定是否设置paint颜色
    if (useTransparentPaint) {
      paint.color = Colors.transparent;
    }

    if (mode == ImageBackgroundMode.tile) {
      // 平铺模式 - 重复绘制图片，不考虑缩放比例
      _drawTiledImage(canvas, size, paint);
    } else {
      // 拉伸模式 - 单次绘制拉伸图片
      _drawStretchedImage(canvas, size, paint);
    }
  }

  /// 绘制平铺图片
  void _drawTiledImage(Canvas canvas, Size size, Paint paint) {
    // 使用图片原始尺寸
    final imageWidth = image.width.ceilToDouble();
    final imageHeight = image.height.ceilToDouble();

    // 计算需要绘制的行数和列数
    final int rows = (size.height / imageHeight).ceil() + 1;
    final int columns = (size.width / imageWidth).ceil() + 1;

    // 源图片区域（使用原始图片的完整区域）
    final Rect sourceRect = Rect.fromLTWH(0, 0, imageWidth, imageHeight);

    // 平铺绘制图片
    for (int i = 0; i < rows; i++) {
      for (int j = 0; j < columns; j++) {
        final Rect destRect = Rect.fromLTWH(
          j * imageWidth,
          i * imageHeight,
          imageWidth,
          imageHeight,
        );
        canvas.drawImageRect(image, sourceRect, destRect, paint);
        // 确保绘制区域不超出边界
        final clippedRect =
            destRect.intersect(Rect.fromLTWH(0, 0, size.width, size.height));
        if (!clippedRect.isEmpty) {
          canvas.drawImageRect(image, sourceRect, destRect, paint);
        }
      }
    }
  }

  /// 绘制拉伸图片
  void _drawStretchedImage(Canvas canvas, Size size, Paint paint) {
    // 计算图片绘制区域
    final Rect outputRect = Offset.zero & size;
    final Size imageSize =
        Size(image.width.ceilToDouble(), image.height.ceilToDouble());

    // 根据适配模式计算图片绘制区域
    final FittedSizes fittedSizes = applyBoxFit(fit, imageSize, size);
    final Size sourceSize = fittedSizes.source;
    final Size destinationSize = fittedSizes.destination;

    // 计算源图片区域和目标区域
    final Rect sourceRect =
        Alignment.center.inscribe(sourceSize, Offset.zero & imageSize);
    final Rect destinationRect =
        Alignment.center.inscribe(destinationSize, outputRect);

    // 绘制图片
    canvas.drawImageRect(image, sourceRect, destinationRect, paint);
  }
}
