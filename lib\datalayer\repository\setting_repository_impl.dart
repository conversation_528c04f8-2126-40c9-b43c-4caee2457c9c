import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/setting/migrations/setting_config_migrations.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

class SettingRepositoryImpl extends SettingRepository {
  static const String _settingFileName = 'setting_config.json';
  static const String _defaultSettingPath =
      'assets/static/setting_config_json.json';

  SettingConfig? _cachedConfig; // 缓存的配置
  Future<SettingConfig>? _operationFuture; // 统一的操作Future，防止并发
  bool _needsSaveAfterLoad = false; // 标记是否需要在加载后保存

  /// 获取本地设置文件路径
  Future<String> get _localSettingPath async {
    final directory = FileManager().getUserRootDir();
    if (directory == null) {
      throw Exception('User root directory not found');
    }
    return path.join(directory.path, _settingFileName);
  }

  @override
  Future<SettingConfig> loadSettingConfig() async {
    // 如果有缓存直接返回
    if (_cachedConfig != null) {
      return _cachedConfig!;
    }

    // 如果有操作正在进行，等待完成
    if (_operationFuture != null) {
      return await _operationFuture!;
    }

    // 开始加载配置
    _operationFuture = _doLoadSettingConfig();

    try {
      final config = await _operationFuture!;
      _operationFuture = null; // 清除操作状态

      // 如果需要保存（例如迁移后），异步执行保存
      if (_needsSaveAfterLoad) {
        _needsSaveAfterLoad = false;
        // 使用异步保存，不阻塞返回
        _asyncSaveConfig(config);
      }

      return config;
    } catch (e) {
      _operationFuture = null; // 清除操作状态
      _needsSaveAfterLoad = false;
      rethrow;
    }
  }

  Future<SettingConfig> _doLoadSettingConfig() async {
    try {
      final settingPath = await _localSettingPath;
      final settingFile = File(settingPath);

      SettingConfig config;
      if (settingFile.existsSync()) {
        // 尝试读取文件，如果文件正在被写入可能会读到空内容
        String jsonString;
        int retryCount = 0;
        const maxRetries = 3;
        const retryDelay = Duration(milliseconds: 100);

        do {
          jsonString = await settingFile.readAsString();

          // 如果读取到空内容且不是第一次尝试，等待后重试
          if (jsonString.trim().isEmpty && retryCount < maxRetries) {
            PGLog.w(
                'Setting file is empty, retrying... (${retryCount + 1}/$maxRetries)');
            await Future.delayed(retryDelay);
            retryCount++;
          } else {
            break;
          }
        } while (retryCount < maxRetries);

        // 检查文件内容是否为空或损坏
        if (jsonString.trim().isEmpty) {
          PGLog.w(
              'Setting file is empty after retries, loading default config');
          config = await _loadDefaultSettingConfig();
        } else {
          try {
            final localConfig = SettingConfig.fromJson(json.decode(jsonString));

            if (localConfig.categories.isEmpty) {
              // 加载默认配置
              final defaultJsonString =
                  await rootBundle.loadString(_defaultSettingPath);
              config = SettingConfig.fromJson(json.decode(defaultJsonString));
            } else {
              // 检查是否需要迁移
              if (await SettingConfigMigrations.needsMigration(localConfig)) {
                // 进行数据兼容处理
                config = await SettingConfigMigrations.migrate(localConfig);

                // 记录迁移统计信息
                final stats = SettingConfigMigrations.getMigrationStats(
                    localConfig, config);
                PGLog.i('设置配置迁移完成: ${stats.toString()}');
              } else {
                config = localConfig;
              }
            }
          } catch (e) {
            PGLog.e('Error parsing setting JSON: $e, loading default config');
            config = await _loadDefaultSettingConfig();
          }
        }
      } else {
        // 加载默认配置
        config = await _loadDefaultSettingConfig();
      }

      _cachedConfig = config;

      // 只有在文件不存在或者需要迁移时才标记需要保存
      if (!settingFile.existsSync() ||
          await SettingConfigMigrations.needsMigration(config)) {
        _needsSaveAfterLoad = true;
      }

      return config;
    } catch (e) {
      PGLog.e('Error loading setting config: $e');
      final config = await _loadDefaultSettingConfig();
      _cachedConfig = config;
      _needsSaveAfterLoad = true; // 标记需要保存默认配置
      return config;
    }
  }

  Future<SettingConfig> _loadDefaultSettingConfig() async {
    final defaultJsonString = await rootBundle.loadString(_defaultSettingPath);
    return SettingConfig.fromJson(json.decode(defaultJsonString));
  }

  @override
  Future<void> saveSettingConfig(SettingConfig config) async {
    await _saveConfigWithRetry(config, isAsync: false);
  }

  /// 异步保存配置，不阻塞调用者
  void _asyncSaveConfig(SettingConfig config) {
    // 使用Future.microtask确保异步执行
    Future.microtask(() async {
      try {
        await _saveConfigWithRetry(config, isAsync: true);
        PGLog.d('Configuration saved asynchronously after migration');
      } catch (e) {
        PGLog.e('Error in async save config: $e');
      }
    });
  }

  /// 统一的保存配置方法，带重试机制
  Future<void> _saveConfigWithRetry(SettingConfig config,
      {bool isAsync = false, int maxRetries = 3}) async {
    // 如果有操作正在进行，等待完成
    if (_operationFuture != null) {
      await _operationFuture!;
    }

    SettingConfig? previousConfig = _cachedConfig; // 备份之前配置
    int retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        // 开始保存配置
        _operationFuture = _doSaveSettingConfig(config);
        await _operationFuture!;
        _operationFuture = null; // 清除操作状态

        // 保存成功，跳出重试循环
        return;
      } catch (e) {
        _operationFuture = null; // 清除操作状态
        retryCount++;

        if (retryCount <= maxRetries) {
          // 还有重试机会，恢复之前的缓存配置
          if (previousConfig != null) {
            _cachedConfig = previousConfig;
          }

          // 计算延迟时间（指数退避）
          final delayMs = 100 * (1 << (retryCount - 1)); // 100ms, 200ms, 400ms
          PGLog.w(
              'Save config failed (attempt $retryCount/$maxRetries), retrying in ${delayMs}ms: $e');

          if (retryCount <= maxRetries) {
            await Future.delayed(Duration(milliseconds: delayMs));
          }
        } else {
          // 所有重试都失败了
          if (previousConfig != null) {
            _cachedConfig = previousConfig; // 恢复之前的配置
          }

          if (isAsync) {
            // 异步保存失败只记录日志，不抛异常
            PGLog.e(
                'Failed to save config after $maxRetries retries (async): $e');
            return;
          } else {
            // 同步保存失败抛出异常
            PGLog.e('Failed to save config after $maxRetries retries: $e');
            rethrow;
          }
        }
      }
    }
  }

  Future<SettingConfig> _doSaveSettingConfig(SettingConfig config) async {
    try {
      _cachedConfig = config; // 更新缓存

      final settingPath = await _localSettingPath;
      final settingFile = File(settingPath);

      await settingFile.parent.create(recursive: true);

      final jsonString = json.encode(config.toJson());

      // 验证JSON字符串不为空且格式正确
      if (jsonString.trim().isEmpty) {
        throw Exception('Generated JSON string is empty');
      }

      // 验证JSON可以被正确解析
      try {
        json.decode(jsonString);
      } catch (e) {
        throw Exception('Generated JSON is invalid: $e');
      }

      // 直接写入文件
      await settingFile.writeAsString(jsonString, flush: true);

      PGLog.d(
          'Setting config saved successfully with ${config.categories.length} categories');

      return config; // 返回配置以保持一致性
    } catch (e) {
      PGLog.e('Error saving setting config: $e');
      rethrow;
    }
  }

  @override
  Future<ExportConfig> getExportConfig(ExportConfig exportConfig) async {
    try {
      final exportQuality = await _getItemValue(
          SettingCategoryConstant.export, SettingKeyConstant.exportQuality);
      final quality = ExportQualityConstant.toValue(exportQuality);

      // Convert quality value to ExportQualityType
      final qualityType = ExportQualityType.values.firstWhere(
        (type) => ExportQualityType.toValue(type) == quality,
        orElse: () => ExportQualityType.high,
      );

      return exportConfig.copyWith(
        quality: qualityType,
      );
    } catch (e) {
      PGLog.e('Error getting export config: $e');
      return exportConfig;
    }
  }

  @override
  Future<ProjectCreationMode> getProjectCategoryConfig() async {
    try {
      final projectCreationMode = await _getItemValue(
          SettingCategoryConstant.projectCreation,
          SettingKeyConstant.projectCreationMode);

      return ProjectCreationMode.fromValue(projectCreationMode);
    } catch (e) {
      PGLog.e('Error getting project category config: $e');
      return ProjectCreationMode.single; // 默认返回单个项目模式
    }
  }

  @override
  Future<CacheCleanSettingResult> getCacheCleanSetting() async {
    try {
      final cacheDays = await _getItemValue(
          SettingCategoryConstant.cache, SettingKeyConstant.cacheDays);
      final cacheSize = await _getItemValue(
          SettingCategoryConstant.cache, SettingKeyConstant.autoCleanSize);
      return CacheCleanSettingResult(
          maxDays: int.parse(cacheDays),
          maxSize: int.parse(cacheSize),
          cachePath: '');
    } catch (e) {
      PGLog.e('Error getting cache setting: $e');
      // 默认返回3天，40GB
      return const CacheCleanSettingResult(
          maxDays: 3, maxSize: 40, cachePath: '');
    }
  }

  @override
  Future<ExportFileSettingResult> getExportFileSetting() async {
    try {
      final exportToOriginalFolder = await _getItemValue(
          SettingCategoryConstant.exportFile,
          SettingKeyConstant.exportToOriginalFolder);
      final exportFolderSuffix = await _getItemValue(
          SettingCategoryConstant.exportFile,
          SettingKeyConstant.exportFolderSuffix);
      return ExportFileSettingResult(
          exportToOriginalFolder: exportToOriginalFolder == '0',
          exportFolderSuffix: exportFolderSuffix);
    } catch (e) {
      PGLog.e('Error getting export file setting: $e');
      return const ExportFileSettingResult(
          exportToOriginalFolder: false, exportFolderSuffix: '_TuringArt');
    }
  }

  @override
  Future<bool> getTouchpadModeEnabled() async {
    try {
      final touchpadModeEnabled = await _getItemValue(
          SettingCategoryConstant.touchpadMode,
          SettingKeyConstant.touchpadModeEnabled);
      return touchpadModeEnabled == SwitchState.on;
    } catch (e) {
      PGLog.e('Error getting touchpad mode setting: $e');
      return false; // 默认关闭触控板模式
    }
  }

  Future<String> _getItemValue(String categoryKey, String itemKey) async {
    try {
      final settingConfig = _cachedConfig ?? await loadSettingConfig();

      return settingConfig.categories
          .firstWhere((element) => element.key == categoryKey)
          .items
          .firstWhere((element) => element.key == itemKey)
          .value;
    } catch (e) {
      PGLog.e('Error getting item value: $e');
      return '';
    }
  }
}
