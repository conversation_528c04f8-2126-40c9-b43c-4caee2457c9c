import 'dart:io';

sealed class ProcessFilesUiStatus {
  const ProcessFilesUiStatus();

  /// 成功状态
  const factory ProcessFilesUiStatus.success() = SuccessStatus;

  /// 错误状态
  const factory ProcessFilesUiStatus.error({
    required ProcessFilesErrorType errorType,
  }) = ErrorStatus;
}

/// 成功状态
final class SuccessStatus extends ProcessFilesUiStatus {
  const SuccessStatus();
}

/// 错误状态
final class ErrorStatus extends ProcessFilesUiStatus {
  final ProcessFilesErrorType errorType;

  const ErrorStatus({required this.errorType});

  String get message {
    switch (errorType) {
      case ProcessFilesErrorType.processing:
        return '正在处理中';
      case ProcessFilesErrorType.diskSpace:
        return Platform.isWindows
            ? '检测到您的C盘空间不足,建议您清理磁盘空间后继续使用图灵精修'
            : '检测到您的磁盘空间不足,建议您清理磁盘空间后继续使用图灵精修';
      case ProcessFilesErrorType.generateProject:
        return '生成项目失败';
      case ProcessFilesErrorType.projectIdMissing:
        return '项目ID缺失';
      case ProcessFilesErrorType.setupWorkspace:
        return '设置工作区失败';
      case ProcessFilesErrorType.importImages:
        return '导入图片失败';
      case ProcessFilesErrorType.writeHistoryFiles:
        return '写入历史文件失败';
      case ProcessFilesErrorType.importPresetFromHistory:
        return '导入历史预设失败';
      case ProcessFilesErrorType.navigation:
        return '导航到编辑页面失败';
      case ProcessFilesErrorType.other:
        return '其他错误';
    }
  }
}

/// 处理文件错误类型
enum ProcessFilesErrorType {
  processing, // 处理中
  diskSpace, // 磁盘空间不足
  generateProject, // 生成项目失败
  projectIdMissing, // 项目ID缺失
  setupWorkspace, // 设置工作区失败
  importImages, // 导入图片失败
  writeHistoryFiles, // 写入历史文件失败
  importPresetFromHistory, // 导入历史预设失败
  navigation, // 导航到编辑页面失败
  other, // 其他错误
}
