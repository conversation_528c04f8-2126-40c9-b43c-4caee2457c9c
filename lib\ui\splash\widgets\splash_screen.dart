import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pg_turing_collect_event/collect/customaction/start.dart'
    as record_start;
import 'package:pg_turing_collect_event/collect/customaction/start_info.dart'
    as start_info;
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:turing_art/core/manager/device_rating_manager.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ffi/ffi.dart';
import 'package:turing_art/ui/check_env/windows_env_checker.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/device_info_util.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/unity/unity_controller.dart';
import '../../../core/manager/expiry_manager.dart';
import '../../../datalayer/service/share_preferences/shared_preferences_service.dart';
import '../../../routing/routes.dart';
import '../../../ui/core/themes/fonts.dart';
import '../../../utils/app_constants.dart';
import '../../../utils/app_info.dart';
import '../../../utils/pg_dialog/pg_dialog.dart';
import '../../../utils/pg_log.dart';
import '../../check_env/app_mismatch_reason.dart';
import '../../check_env/app_unavailable_dialog.dart';
import 'preload_desktop_unity.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _showUnityWidget = false; // 控制是否显示 Unity 组件
  bool _unityIsInitialized = false; // Unity 是否完全初始化完成
  bool _isFirstLaunch = false; // 是否是首次启动
  Timer? _startupTimeoutTimer; // 启动超时计时器

  // 当前用户id，目前用于埋点上报
  String? _userId;

  late final ISentrySpan _startupTransaction;
  ISentrySpan? _expiryCheckSpan;
  ISentrySpan? _unityCreateSpan; // Unity 创建子事务
  ISentrySpan? _unityInitSpan; // Unity 初始化子事务

  // 添加加载文本动画相关变量
  int _loadingTextIndex = 0;
  final List<String> _loadingTexts = [
    '新质视觉引擎启动中.',
    '新质视觉引擎启动中..',
    '新质视觉引擎启动中...'
  ];
  Timer? _loadingTextTimer;
  // final appMatchResult = checkWindowsAppMatch(_getSdkPlatformInfo);

  @override
  void initState() {
    super.initState();

    _startupTransaction = Sentry.startTransaction(
      '启动流程事务',
      '开始',
      bindToScope: true,
    );
    _initialize();
    _startStartupTimeoutTimer();

    // 启动文本动画计时器
    _loadingTextTimer =
        Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {
          _loadingTextIndex = (_loadingTextIndex + 1) % _loadingTexts.length;
        });
      }
    });
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;
  }

  void _startStartupTimeoutTimer() {
    _startupTimeoutTimer = Timer(const Duration(minutes: 1), () {
      if (mounted && !_unityIsInitialized) {
        Sentry.captureMessage(
          '应用启动超时',
          level: SentryLevel.error,
          withScope: (scope) {
            scope.setTag('is_first_launch', _isFirstLaunch.toString());
          },
        );
        PGLog.e('应用启动超时：启动时间超过1分钟');
      }
    });
  }

  Future<void> _initialize() async {
    try {
      // 开始过期检查子事务
      _expiryCheckSpan = _startupTransaction.startChild('过期检查');

      // 检查是否是首次启动
      final firstLaunchTime = SharedPreferencesService.getFirstLaunchTime();
      _isFirstLaunch = firstLaunchTime.isEmpty;

      await context.read<ExpiryManager>().checkAndSetFirstLaunch();
      _expiryCheckSpan?.finish();

      if (!AppConstants.isDesktop) {
        // 非 Windows、MacOS 平台的简单启动流程
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            _startupTransaction.finish();
            context.go(Routes.projectHome);
          }
        });
      } else {
        PGLog.d('SplashScreen - Windows 平台，加载 Unity 后 3 秒跳转');
        // Windows、MacOS 平台启动 Unity 创建子事务
        _unityCreateSpan = _startupTransaction.startChild('Unity 组件创建');

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            setState(() {
              _showUnityWidget = true;
            });
            PGLog.d('SplashScreen - 开始加载 Unity 组件');
            _recordUnityEvent(record_start.Action.unity_start);
          }
        });
      }
    } catch (e, stackTrace) {
      _expiryCheckSpan?.finish(status: const SpanStatus.internalError());
      _unityCreateSpan?.finish(status: const SpanStatus.internalError());
      _unityInitSpan?.finish(status: const SpanStatus.internalError());
      _startupTransaction.finish(status: const SpanStatus.internalError());
      Sentry.captureException(e, stackTrace: stackTrace);
      PGLog.e('Splash initialization error: $e');
    }
  }

  /// 处理 Unity 创建完成的回调
  void _handleUnityCreated(dynamic sender) {
    PGLog.d('SplashScreen - Unity 创建完成');
    // 完成 Unity 创建子事务
    _unityCreateSpan?.finish();

    // 开始 Unity 初始化子事务
    _unityInitSpan = _startupTransaction.startChild(
      _isFirstLaunch ? 'Unity首次初始化' : 'Unity常规初始化',
    );
  }

  /// 处理 Unity 完全初始化完成的回调
  void _handleUnityReady() {
    PGLog.d('SplashScreen - Unity 完全初始化完成');
    _recordUnityEvent(record_start.Action.unity_suc);
    _startupTimeoutTimer?.cancel(); // 取消超时计时器
    final unityController = context.read<UnityController>();
    PGLog.d('SplashScreen - Preparing to set Unity visibility to false');
    unityController.setUnityVisibility(visible: false);
    if (mounted) {
      setState(() {
        _unityIsInitialized = true;
      });
      // 完成 Unity 初始化子事务
      _unityInitSpan?.finish();
      // 完成整个启动事务
      _startupTransaction.finish();
      // 检查是否已有缓存的评分信息
      if (DeviceRatingManager.ratingResult == null) {
        PGLog.d('SplashScreen - 没有缓存的评分信息，发送获取设备信息请求并等待响应');
        MessageToUnity getDeviceInfoDetailMsg = MessageToUnity(
          method: 'GetDeviceInfoDetail',
          completed: const Uuid().v4(),
        );
        unityController.sendMessage(getDeviceInfoDetailMsg);
      } else {
        PGLog.d('SplashScreen - 已有缓存的评分信息，跳过获取设备信息');
        _checkDeviceAndNavigate();
      }
    }
  }

  /// 根据设备信息判断，跳转项目首页
  void _checkDeviceAndNavigate() async {
    final appMatchResult = await _getAppMatchResult();

    // 只有在未发送过设备评级消息时才发送
    if (!DeviceRatingManager.hasDeviceRatingMessageSent) {
      final deviceRatingMessage = DeviceRatingManager.getDetailedSummary();
      Sentry.captureMessage(deviceRatingMessage, level: SentryLevel.info);
      await DeviceRatingManager.markDeviceRatingMessageSent();
      PGLog.i('首次发送设备评级信息: $deviceRatingMessage');
    }

    _recordDeviceInfo(DeviceRatingManager.deviceInfo);

    if (!DeviceRatingManager.isAvailable) {
      PGLog.d('SplashScreen - 设备不可用，跳过跳转');
      // 获取内存和显存大小
      final deviceInfo = DeviceRatingManager.deviceInfo;
      if (deviceInfo != null) {
        final systemMemorySize = deviceInfo.systemMemorySize;
        final graphicsMemorySize = deviceInfo.graphicsMemorySize;

        if (systemMemorySize != null) {
          final memoryGB = systemMemorySize / 1024.0; // 转换为GB
          PGLog.d('SplashScreen - 系统内存大小: ${memoryGB.toStringAsFixed(1)}GB');
        }

        if (graphicsMemorySize != null) {
          final graphicsMemoryGB = graphicsMemorySize / 1024.0; // 转换为GB
          PGLog.d(
              'SplashScreen - 显存大小: ${graphicsMemoryGB.toStringAsFixed(1)}GB');
        }
      }
      final ram = deviceInfo?.systemMemorySize;
      final vram = deviceInfo?.graphicsMemorySize;
      PGDialog.showCustomDialog(
          child: AppUnavailableDialog(
              checkResult: PoorPerformance(ram: ram, vram: vram)),
          width: 345,
          height: 360);
    } else if (appMatchResult is CheckAppNotOK) {
      // 检查当前 Windows 平台是可以运行当前图灵精修版本，如果不能则需要弹窗提示
      PGDialog.showCustomDialog(
          child: AppUnavailableDialog(checkResult: appMatchResult),
          width: 345,
          height: 360);
      return;
    } else {
      if (mounted) {
        context.go(Routes.projectHome);
      }
    }
  }

  Future<CheckAppMatchResult> _getAppMatchResult() async {
    final sdkPlatformInfo = await _getSdkPlatformInfo() ?? 0;
    String platform = '';
    if (sdkPlatformInfo == 1) {
      platform = 'win7';
    } else if (sdkPlatformInfo == 2) {
      platform = 'win10';
    }
    return checkWindowsAppMatch(platform);
  }

  /// 获取SugoiNativeSDK平台信息
  Future<int?> _getSdkPlatformInfo() async {
    try {
      // 初始化SugoiNativeSDK
      final sdkInitialized = await SugoiNativeSDKService.initialize();

      if (sdkInitialized) {
        // 获取SDK详细信息
        final sdkPlatformInfo = SugoiNativeSDKService.getPlatformInfo();
        PGLog.d('SplashScreen - SDK详细信息: $sdkPlatformInfo');

        return sdkPlatformInfo;
      } else {
        PGLog.w('SplashScreen - SugoiNativeSDK初始化失败');
        return null;
      }
    } catch (e) {
      PGLog.e('SplashScreen - 获取SDK平台信息失败: $e');
      return null;
    }
  }

  Future<void> _handleUnityMessage(MessageFromUnity message) async {
    final appMatchResult = await _getAppMatchResult();
    PGLog.d('SplashScreen - Unity message: $message');
    if (message.method == UnityMessage.onDeviceInfoDetail.value) {
      PGLog.d('SplashScreen - 收到设备信息详情: $message');

      try {
        // 解析设备信息数据
        final String jsonStr = message.args![0] as String;
        final Map<String, dynamic> deviceData =
            json.decode(jsonStr) as Map<String, dynamic>;

        // 创建 SystemDeviceInfo 对象
        final systemDeviceInfo = SystemDeviceInfo.fromJson(deviceData);

        // 调用设备评级管理器记录设备信息
        await DeviceRatingManager.recordDeviceInfo(systemDeviceInfo);

        _checkDeviceAndNavigate();
        PGLog.i('SplashScreen - 设备信息已传递给设备评级管理器');
      } catch (e) {
        PGLog.e('SplashScreen - 处理设备信息失败: $e');
        Sentry.captureException(e);
        if (appMatchResult is CheckAppNotOK) {
          // 检查当前 Windows 平台是可以运行当前图灵精修版本，如果不能则需要弹窗提示
          PGDialog.showCustomDialog(
              child: AppUnavailableDialog(checkResult: appMatchResult),
              width: 345,
              height: 360);
        } else {
          if (mounted) {
            context.go(Routes.projectHome);
          }
        }
      }
    }
  }

  // 记录设备信息到埋点系统
  void _recordDeviceInfo(SystemDeviceInfo? deviceInfo) {
    if (deviceInfo == null) {
      PGLog.e('SplashScreen - 设备信息为空，无法记录');
      return;
    }

    final infoList = [
      DeviceInfoUtil().osVersion,
      deviceInfo.processorType,
      deviceInfo.processorCount,
      deviceInfo.systemMemorySize,
      deviceInfo.graphicsDeviceName,
      deviceInfo.graphicsMemorySize,
      DeviceInfoUtil().screenSize
    ];
    final recordMsg = infoList.join('~');
    start_info.recordStartInfo(
        subElementId: '',
        userId: _userId ?? '',
        contentId: recordMsg,
        action: start_info.Action.init);
  }

  // 记录 Unity 事件到埋点系统
  void _recordUnityEvent(record_start.Action action) {
    String contentId = '';
    switch (action) {
      case record_start.Action.unity_start:
        contentId =
            '${record_start.Action.unity_start.value}_${DateTimeUtil.getCurrentTimestampMs()}';
        break;
      case record_start.Action.unity_suc:
        contentId =
            '${record_start.Action.unity_suc.value}_${DateTimeUtil.getCurrentTimestampMs()}';

        break;
      default:
        return; // 如果不需要处理其他事件，直接返回
    }
    record_start.recordStart(
        subElementId: '',
        userId: _userId ?? '',
        contentId: contentId,
        action: action);
  }

  @override
  void dispose() {
    _loadingTextTimer?.cancel();
    _startupTimeoutTimer?.cancel(); // 取消超时计时器
    // 确保在组件销毁时完成所有未完成的事务
    try {
      _unityCreateSpan?.finish(status: const SpanStatus.cancelled());
      _unityInitSpan?.finish(status: const SpanStatus.cancelled());
      _startupTransaction.finish(status: const SpanStatus.cancelled());
    } catch (_) {
      // 忽略已完成的事务
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        fit: StackFit.expand,
        children: [
          if (AppConstants.isDesktop && _showUnityWidget) ...[
            preloadDesktopUnity(
              onUnityCreated: _handleUnityCreated,
              onUnityReady: _handleUnityReady,
              onUnityMessageCallback: _handleUnityMessage,
            ),
          ],
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: SizedBox(
                    width: 166,
                    height: 217,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 150,
                          height: 150,
                          child: Image.asset(
                            'assets/icons/lauch_logo.png',
                            fit: BoxFit.contain,
                          ),
                        ),
                        SizedBox(
                          width: 166,
                          height: 48,
                          child: Image.asset(
                            'assets/icons/launch_title.png',
                            fit: BoxFit.contain,
                          ),
                        ),
                        const SizedBox(height: 2),
                        // 版本信息
                        SizedBox(
                          height: 17,
                          width: 80,
                          child: Text(
                            AppInfoExtension.getVersionString(),
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFFE1E2E5),
                              fontFamily: Fonts.fontFamilySF,
                              fontSize: 12,
                              fontWeight: Fonts.semiBold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                // Unity 初始化状态提示
                if (AppConstants.isWindows)
                  Text(
                    _unityIsInitialized
                        ? '一切就绪，开始创作吧！🎨'
                        : _loadingTexts[_loadingTextIndex],
                    style: TextStyle(
                      color: const Color(0xFFEBEDF5).withAlpha(165),
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 12,
                      fontWeight: Fonts.regular,
                    ),
                  ),
              ],
            ),
          ),
          // 版权信息
          Positioned(
            left: 0,
            right: 0,
            bottom: 24,
            child: Center(
              child: Text(
                AppInfoExtension.getCopyrightString(),
                style: TextStyle(
                  color: const Color(0xFFEBEDF5).withAlpha(100),
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
