import 'package:flutter/material.dart';
import 'package:turing_art/core/external_message/handler/external_message_result.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

import '../../core/themes/fonts.dart';

class FileChooseOption extends StatelessWidget {
  final VoidCallback onOptionSelected;
  final HomeViewModel homeViewModel;

  static const double _optionWidth = 260.0;
  static const double _optionHeight = 104.0;
  static const double _containerWidth = 536.0;

  const FileChooseOption({
    super.key,
    required this.onOptionSelected,
    required this.homeViewModel,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: _containerWidth,
      height: _optionHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildOptionButton(
            icon: 'assets/icons/project_file_choose_image.png',
            label: '从"照片"导入',
            onTap: () async {
              onOptionSelected();
              final dealImageFilesResult =
                  await homeViewModel.pickImagesFromGallery();
              if (dealImageFilesResult != null) {
                if (dealImageFilesResult.isTapjImport) {
                  // 处理tapj文件导入
                  final tapjData = dealImageFilesResult.tapjData!;
                  final importResult = await homeViewModel.handleImportProject(
                    context,
                    tapjData.importData,
                    tapjData.historyFiles,
                  );

                  // 在上层处理Toast显示
                  if (context.mounted) {
                    if (importResult is ExternalMessageError) {
                      PGDialog.showToastInMainThread(importResult.error);
                    } else if (importResult is ExternalMessageSuccess) {
                      PGDialog.showToastInMainThread(importResult.message);
                    }
                  }
                } else {
                  // 处理普通图片文件
                  homeViewModel.processSelectedFiles2(
                    dealImageFilesResult.validFiles,
                    dealImageFilesResult.projectName,
                    null,
                  );
                }
              }
            },
          ),
          const SizedBox(width: 16),
          _buildOptionButton(
            icon: 'assets/icons/project_file_choose_file.png',
            label: '从"文件"导入',
            onTap: () async {
              onOptionSelected();
              final dealImageFilesResult =
                  await homeViewModel.pickImagesFromFiles();
              if (dealImageFilesResult != null) {
                if (dealImageFilesResult.isTapjImport) {
                  // 处理tapj文件导入
                  final tapjData = dealImageFilesResult.tapjData!;
                  final importResult = await homeViewModel.handleImportProject(
                    context,
                    tapjData.importData,
                    tapjData.historyFiles,
                  );

                  // 在上层处理Toast显示
                  if (context.mounted) {
                    if (importResult is ExternalMessageError) {
                      PGDialog.showToastInMainThread(importResult.error);
                    } else if (importResult is ExternalMessageSuccess) {
                      PGDialog.showToastInMainThread(importResult.message);
                    }
                  }
                } else {
                  // 处理普通图片文件
                  homeViewModel.processSelectedFiles2(
                    dealImageFilesResult.validFiles,
                    dealImageFilesResult.projectName,
                    null,
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: _optionWidth,
        height: _optionHeight,
        decoration: BoxDecoration(
          color: const Color(0xFF222526),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(width: 32),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF2A2D2E),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.asset(
                icon,
                width: 40,
                height: 40,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: Fonts.semiBold,
                fontFamily: Fonts.defaultFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
