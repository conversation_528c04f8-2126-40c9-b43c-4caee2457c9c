import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/export_result/use_case/delete_export_task_use_case.dart';
import 'package:turing_art/ui/export_result/use_case/fetch_export_report_use_case2.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 删除工程，这里处理的非常草率
/// 后续流程中需要针对当前正在删除的工程判断是否正在导出，删除后还需要考虑回滚预扣费相关操作
@Deprecated('该接口已经废弃，请使用 DeleteProjectUseCase2')
class DeleteProjectUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final FetchExportReportUseCase2 _fetchExportReport;
  final DeleteExportTaskUseCase _deleteExportTask;

  DeleteProjectUseCase(
    this._projectRepository,
    this._currentUserRepository,
    this._fetchExportReport,
    this._deleteExportTask,
  );

  Future<bool> invoke(String projectId) async {
    try {
      final user = _currentUserRepository.user;
      if (user == null) {
        PGLog.e('用户未登录');
        return false;
      }

      // 查询当前工程是否在导出
      // final exportList = await _fetchExportReport.invoke();
      // 如果当前工程正在导出，先删除导出任务,找到所有的潜在的正在导出的任务
      // final working = exportList
      //     .where((element) =>
      //         (element.exportState != 4 && element.exportState != 5))
      //     .toSet();

      // if (working.isNotEmpty) {
      //   // 查看正在进行的任务中是否包含当前需要删除的工作区的文件
      //   final workspace =
      //       await _projectRepository.getWorkspaceByProjectId(projectId);
      //   final workspaceFiles =
      //       workspace?.files.map((e) => e.fileId).toSet() ?? {};

      //   // 查找包含工作区文件的导出任务
      //   final conflictTasks = working.where((task) {
      //     final taskFileIds =
      //         task.exportFiles.map((f) => f.fileParam.fileId).toSet();
      //     return taskFileIds.intersection(workspaceFiles).isNotEmpty;
      //   }).toList();

      //   // 如果存在冲突的导出任务，返回导出中状态
      //   if (conflictTasks.isNotEmpty) {
      //     _deleteExportTask.invoke(conflictTasks);
      //   }
      // }

      // 删除项目文件夹
      FileManager().deleteProjectDirectory(projectId);

      // 删除项目数据库
      _projectRepository.deleteProject(projectId);

      return true;
    } catch (e) {
      PGLog.e('删除项目失败: $e');
      return false;
    }
  }
}
