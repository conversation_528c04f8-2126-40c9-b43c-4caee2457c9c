// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProjectInfoImpl _$$ProjectInfoImplFromJson(Map<String, dynamic> json) =>
    _$ProjectInfoImpl(
      name: json['name'] as String,
      uuid: json['uuid'] as String,
      version: json['version'] as String,
      author: json['author'] as String,
      exportConfig:
          ExportConfig.fromJson(json['exportConfig'] as Map<String, dynamic>),
      projectType: $enumDecode(_$ProjectTypeEnumMap, json['projectType']),
      coverImages: (json['coverImages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      fileCount: (json['fileCount'] as num?)?.toInt() ?? 0,
      description: json['description'] as String? ?? "",
      createdDate: json['createdDate'] == null
          ? null
          : DateTime.parse(json['createdDate'] as String),
      updateDate: json['updateDate'] == null
          ? null
          : DateTime.parse(json['updateDate'] as String),
      workspaceVersion: (json['workspaceVersion'] as num?)?.toInt() ?? 1,
      isDelete: json['isDelete'] as bool? ?? false,
    );

Map<String, dynamic> _$$ProjectInfoImplToJson(_$ProjectInfoImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'uuid': instance.uuid,
      'version': instance.version,
      'author': instance.author,
      'exportConfig': instance.exportConfig,
      'projectType': _$ProjectTypeEnumMap[instance.projectType]!,
      'coverImages': instance.coverImages,
      'fileCount': instance.fileCount,
      'description': instance.description,
      'createdDate': instance.createdDate?.toIso8601String(),
      'updateDate': instance.updateDate?.toIso8601String(),
      'workspaceVersion': instance.workspaceVersion,
      'isDelete': instance.isDelete,
    };

const _$ProjectTypeEnumMap = {
  ProjectType.edit: 'edit',
  ProjectType.aiGen: 'aiGen',
};
