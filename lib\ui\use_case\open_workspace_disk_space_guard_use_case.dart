import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/core/service/disk_info_service/disk_info_service.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 工作区磁盘空间检查用例
class OpenWorkspaceDiskSpaceGuardUseCase {
  static const double _minRequiredSpace =
      workspaceDiskSpaceMinFreeSpace; // 1.5GB in bytes

  OpenWorkspaceDiskSpaceGuardUseCase();

  /// 检查添加文件后是否有足够的磁盘空间
  /// @return true 表示空间足够，false 表示空间不足
  Future<bool> invoke() async {
    final startTime = DateTime.now();

    try {
      // 获取磁盘剩余空间
      final appDir = await FileManager().appDir;
      final spaceInfo = await DiskInfo.getDiskSpace(appDir.path);
      if (spaceInfo == null) {
        PGLog.e('无法获取工作区目录的剩余空间');
        return false;
      }
      PGLog.i('工作区剩余空间: ${spaceInfo.freeSpace / (1024 * 1024)}MB');

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      if (spaceInfo.freeSpace < _minRequiredSpace) {
        PGLog.w(
            '空间不足，需要至少 ${_minRequiredSpace / (1024 * 1024 * 1024)}GB 的剩余空间,耗时: ${duration.inMilliseconds}ms');
        return false;
      }
      PGLog.i('磁盘空间检查完成，耗时: ${duration.inMilliseconds}ms');

      return true;
    } catch (e) {
      PGLog.e('检查磁盘空间时发生错误: $e');
      return false;
    }
  }
}
