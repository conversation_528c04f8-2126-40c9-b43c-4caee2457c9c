import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:uuid/uuid.dart';

import '../../../datalayer/domain/models/message_to_unity/message_to_unity.dart';
import '../../../datalayer/repository/project_repository.dart';
import '../../../datalayer/repository/setting_repository.dart';
import '../../../utils/pg_log.dart';

// 导出设置的Json字符串
class GenerateSetupExportConfigUnityMessageUseCase {
  final ProjectRepository _repository;
  final SettingRepository _settingRepository;

  GenerateSetupExportConfigUnityMessageUseCase(
      this._repository, this._settingRepository);

  Future<MessageToUnity?> invoke(String projectId) async {
    try {
      final project = await _repository.getProjectById(projectId);
      if (project == null) {
        return null;
      }

      final exportConfigObj = await _getExportConfigJson();
      String json = jsonEncode(exportConfigObj);
      MessageToUnity msg = MessageToUnity(
        method: 'SetupExportConfig',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate setup export config message use case error : $e');
      return null;
    }
  }

  // 获取导出配置的Json字符串
  Future<Map<String, dynamic>> _getExportConfigJson() async {
    final exportFileSetting = await _settingRepository.getExportFileSetting();
    final exportConfig =
        await _settingRepository.getExportConfig(ExportConfig.defaultConfig());
    Map<String, dynamic> exportConfigObj = exportConfig.toJson();
    exportConfigObj['folderSuffixName'] =
        exportFileSetting.exportFolderSuffix.isEmpty
            ? '_TuringArt'
            : exportFileSetting.exportFolderSuffix;
    exportConfigObj['isSameLevelExportPath'] =
        exportFileSetting.exportToOriginalFolder;
    return exportConfigObj;
  }
}
