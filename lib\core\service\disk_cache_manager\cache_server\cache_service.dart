import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_result/cache_cleanup_result.dart';

/// 缓存服务等级枚举
enum CacheServiceLevel {
  /// 高优先级 - 最后清理
  high,

  /// 中优先级 - 中等清理
  medium,

  /// 低优先级 - 优先清理
  low,
}

/// 缓存服务接口
/// 每个模块需要实现此接口来注册自己的缓存服务
abstract class CacheService {
  /// 服务名称
  String get serviceName;

  /// 服务等级
  CacheServiceLevel get level;

  /// 获取缓存大小（字节）
  Future<int> getCacheSize();

  /// 清理缓存
  /// [targetSize] 目标清理后的大小（字节），如果为null则清理所有
  Future<ServiceCleanupResult> clearCache({int? targetSize});
}
