{"_meta": {"description": "This file contains a serialized version of schema entities for drift.", "version": "1.1.0"}, "options": {"store_date_time_values_as_text": false}, "entities": [{"id": 0, "references": [], "type": "table", "data": {"name": "project_entity", "was_declared_in_moor": false, "columns": [{"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "project_id", "getter_name": "projectId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "version", "getter_name": "version", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "author", "getter_name": "author", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cover_images", "getter_name": "coverImages", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "description", "getter_name": "description", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "created_date", "getter_name": "createdDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "update_date", "getter_name": "updateDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "project_type", "getter_name": "projectType", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "output_folder", "getter_name": "outputFolder", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "export_file_type", "getter_name": "exportFileType", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "quality", "getter_name": "quality", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(100)", "default_client_dart": null, "dsl_features": []}, {"name": "is_replace", "getter_name": "isReplace", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_replace\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "transfer_s_r_g_b", "getter_name": "transferSRGB", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"transfer_s_r_g_b\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "workspace_version", "getter_name": "workspaceVersion", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(1)", "default_client_dart": null, "dsl_features": []}, {"name": "is_delete", "getter_name": "isDelete", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_delete\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["project_id"]}}, {"id": 1, "references": [], "type": "table", "data": {"name": "user_entity", "was_declared_in_moor": false, "columns": [{"name": "id", "getter_name": "id", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "username", "getter_name": "username", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "uid", "getter_name": "uid", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "phone_number", "getter_name": "phoneNumber", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token", "getter_name": "token", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token_expired_date", "getter_name": "tokenExpiredDate", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token_end", "getter_name": "tokenEnd", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "first_login", "getter_name": "firstLogin", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "last_login_time", "getter_name": "lastLoginTime", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "reg_date_time", "getter_name": "regDateTime", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "cc", "getter_name": "cc", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Con<PERSON>(86)", "default_client_dart": null, "dsl_features": []}, {"name": "role", "getter_name": "role", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('creator')", "default_client_dart": null, "dsl_features": []}, {"name": "used", "getter_name": "used", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('0')", "default_client_dart": null, "dsl_features": []}, {"name": "enable", "getter_name": "enable", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(1)", "default_client_dart": null, "dsl_features": []}, {"name": "last_login_store_id", "getter_name": "lastLoginStoreId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["id"]}}, {"id": 2, "references": [0], "type": "table", "data": {"name": "workspace_entity", "was_declared_in_moor": false, "columns": [{"name": "workspace_id", "getter_name": "workspaceId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "workspace_name", "getter_name": "workspaceName", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "current_file_id", "getter_name": "currentFileId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "create_time", "getter_name": "createTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "last_edit_time", "getter_name": "lastEditTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "project_id", "getter_name": "projectId", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES project_entity (project_id)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "filter_value", "getter_name": "filterValue", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('{}')", "default_client_dart": null, "dsl_features": []}, {"name": "sort_value", "getter_name": "sortValue", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('{}')", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["workspace_id"]}}, {"id": 3, "references": [2], "type": "table", "data": {"name": "workspace_file_entity", "was_declared_in_moor": false, "columns": [{"name": "file_id", "getter_name": "fileId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "edited", "getter_name": "edited", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"edited\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "stars", "getter_name": "stars", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "exported", "getter_name": "exported", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"exported\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "org_path", "getter_name": "orgPath", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_time", "getter_name": "exportTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "format", "getter_name": "format", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broken", "getter_name": "broken", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"broken\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "last_edit_time", "getter_name": "lastEditTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "create_time", "getter_name": "createTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "size", "getter_name": "size", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "width", "getter_name": "width", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "height", "getter_name": "height", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "orientation", "getter_name": "orientation", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "icc_type", "getter_name": "iccType", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "is_raw", "getter_name": "isRaw", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_raw\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "raw_path", "getter_name": "rawPath", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "converted", "getter_name": "converted", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"converted\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "workspace_id", "getter_name": "workspaceId", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES workspace_entity (workspace_id)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "file_name", "getter_name": "fileName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "iconized", "getter_name": "iconized", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"iconized\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "mid_iconized", "getter_name": "midIconized", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"mid_iconized\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "capture_time", "getter_name": "captureTime", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "is_over_size", "getter_name": "isOverSize", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"is_over_size\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "face_count", "getter_name": "faceCount", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "bin_format", "getter_name": "binFormat", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "raw_auto_expose", "getter_name": "rawAutoExpose", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"raw_auto_expose\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "raw_auto_adjust_type", "getter_name": "rawAutoAdjustType", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "is_deleted", "getter_name": "isDeleted", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"is_deleted\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "n8_exported", "getter_name": "n8Exported", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "n8_export_time", "getter_name": "n8ExportTime", "moor_type": "bigInt", "nullable": true, "customConstraints": null, "default_dart": "Constant(BigInt.from(0))", "default_client_dart": null, "dsl_features": []}, {"name": "file_exported_state", "getter_name": "fileExportedState", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["file_id"]}}, {"id": 4, "references": [], "type": "table", "data": {"name": "export_token_entity", "was_declared_in_moor": false, "columns": [{"name": "image_p_hash", "getter_name": "imagePHash", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "user_id", "getter_name": "userId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token_id", "getter_name": "tokenId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "key", "getter_name": "key", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "expire_at", "getter_name": "expireAt", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "image_name", "getter_name": "imageName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "create_time", "getter_name": "createTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_type", "getter_name": "exportType", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('retouch')", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["user_id", "image_p_hash", "export_type"]}}, {"id": 5, "references": [], "type": "table", "data": {"name": "creator_info_entity", "was_declared_in_moor": false, "columns": [{"name": "uid", "getter_name": "uid", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "creator_mobile", "getter_name": "creator<PERSON><PERSON><PERSON>", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["uid"]}}, {"id": 6, "references": [], "type": "table", "data": {"name": "file_operation_history_entity", "was_declared_in_moor": false, "columns": [{"name": "id", "getter_name": "id", "moor_type": "int", "nullable": false, "customConstraints": null, "defaultConstraints": "PRIMARY KEY AUTOINCREMENT", "default_dart": null, "default_client_dart": null, "dsl_features": ["auto-increment"]}, {"name": "file_id", "getter_name": "fileId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "create_time", "getter_name": "createTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "extra_data", "getter_name": "extraData", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": []}}, {"id": 7, "references": [], "type": "table", "data": {"name": "export_task_entity", "was_declared_in_moor": false, "columns": [{"name": "guid", "getter_name": "guid", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "user_id", "getter_name": "userId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cache_path", "getter_name": "cachePath", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_file_config", "getter_name": "exportFileConfig", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "show_name", "getter_name": "showName", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_paths", "getter_name": "exportPaths", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_state", "getter_name": "exportState", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "operate_time", "getter_name": "operateTime", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "create_time", "getter_name": "createTime", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "item_count", "getter_name": "itemCount", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "success_num", "getter_name": "successNum", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "error_message", "getter_name": "errorMessage", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "error_num", "getter_name": "errorNum", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["guid"]}}, {"id": 8, "references": [], "type": "table", "data": {"name": "export_task_file_entity", "was_declared_in_moor": false, "columns": [{"name": "guid", "getter_name": "guid", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_task_id", "getter_name": "exportTaskId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "history_id", "getter_name": "historyId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "file_param", "getter_name": "fileParam", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_file_config", "getter_name": "exportFileConfig", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "error_message", "getter_name": "errorMessage", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": []}, {"name": "error_num", "getter_name": "errorNum", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}, {"name": "final_path", "getter_name": "finalPath", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "export_state", "getter_name": "exportState", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "const Constant(0)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["guid"]}}]}