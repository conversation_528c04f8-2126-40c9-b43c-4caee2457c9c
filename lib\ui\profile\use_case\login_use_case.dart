import 'package:turing_art/datalayer/domain/models/login_info/login_info.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 登录异常
class LoginException implements Exception {
  final int code;
  final String message;

  LoginException(this.code, this.message);
}

/// 登录结果
class LoginResult {
  final bool isSuccess;
  final String message;
  final LoginInfo? loginInfo;

  LoginResult({
    required this.isSuccess,
    required this.message,
    this.loginInfo,
  });

  factory LoginResult.success(LoginInfo info) {
    return LoginResult(
      isSuccess: true,
      message: '登录成功',
      loginInfo: info,
    );
  }

  factory LoginResult.failure(String message) {
    return LoginResult(
      isSuccess: false,
      message: message,
      loginInfo: null,
    );
  }
}

/// 登录用例
class LoginUseCase {
  final AuthRepository _authRepository;
  final CurrentUserRepository _currentUserRepository;
  final OpsCustomTableRepository _customTableRepository;

  LoginUseCase(this._authRepository, this._currentUserRepository,
      this._customTableRepository);

  /// 执行登录
  /// [phoneNumber] 手机号
  /// [code] 验证码
  /// [storeId] 门店ID
  /// 返回登录结果
  Future<LoginResult> invoke({
    required String phoneNumber,
    required String? cc,
    required String code,
    required String storeId,
  }) async {
    // 参数验证
    if (phoneNumber.isEmpty) {
      return LoginResult.failure('请输入正确的手机号');
    }
    if (code.isEmpty) {
      return LoginResult.failure('请输入验证码');
    }
    // 拉取自定义表信息,保证白名单信息及时更新
    await _customTableRepository.getCustomTable(
      code: 'AppConfig',
      refresh: true,
    );

    try {
      final loginInfo = await _authRepository.logIn(
        phoneNumber,
        cc,
        code,
        storeId,
      );
      if (loginInfo != null) {
        // 同步用户信息
        await _currentUserRepository.syncCurrentUser(
          loginInfo.user,
          loginInfo.store,
        );
        // 同步主账号信息,不然子账号登录后，主账号信息不会显示
        await _currentUserRepository.syncCreator(loginInfo.creator);

        // 保存选中的员工ID
        UserPreferencesService.setLastLoginEmployeeId(loginInfo.user.id);

        return LoginResult.success(loginInfo);
      } else {
        return LoginResult.failure('登录失败，请稍后再试');
      }
    } catch (e) {
      String errorMessage = '登录失败';

      // 处理 LoginException 异常
      if (e is LoginException) {
        if (e.code == 10537) {
          errorMessage = '验证码错误，请重新输入';
        } else if (e.code == 401) {
          errorMessage = '手机号验证失败，请重新输入';
        } else {
          errorMessage = '登录失败，请稍后再试';
        }
      } else {
        errorMessage = '登录失败，请稍后再试';
      }

      PGLog.e('登录失败: $e');
      return LoginResult.failure(errorMessage);
    }
  }

  /// 发送验证码
  /// [phoneNumber] 手机号
  /// 返回发送结果
  Future<bool> sendVerificationCode(String phoneNumber, String? cc) async {
    if (phoneNumber.isEmpty) {
      return false;
    }

    try {
      return await _authRepository.sendVerificationCode(phoneNumber, cc);
    } catch (e) {
      PGLog.e('发送验证码失败: $e');
      return false;
    }
  }
}
