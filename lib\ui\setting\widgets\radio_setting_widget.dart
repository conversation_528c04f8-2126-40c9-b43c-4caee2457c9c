import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_choice_model.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';

/// 通用的单选设置组件
class RadioSettingWidget extends StatefulWidget {
  final String categoryKey;
  final SettingItemModel settingItem;

  const RadioSettingWidget({
    super.key,
    required this.categoryKey,
    required this.settingItem,
  });

  @override
  State<RadioSettingWidget> createState() => _RadioSettingWidgetState();
}

class _RadioSettingWidgetState extends State<RadioSettingWidget> {
  late ValueNotifier<int> _selectedIndexNotifier;

  @override
  void initState() {
    super.initState();
    // 初始化选中项
    final viewModel = context.read<SettingViewModel>();
    final selectedIndex = viewModel.getSelectedIndex(
      widget.categoryKey,
      widget.settingItem.key,
    );
    _selectedIndexNotifier = ValueNotifier(selectedIndex);
  }

  @override
  void dispose() {
    _selectedIndexNotifier.dispose();
    super.dispose();
  }

  void _selectChoice(int index, SettingChoiceModel choice) {
    _selectedIndexNotifier.value = index;

    // 更新设置
    final settingViewModel = context.read<SettingViewModel>();
    settingViewModel.updateChoiceSelection(
      widget.categoryKey,
      widget.settingItem.key,
      choice.value,
    );
  }

  Widget _buildRadioOption({
    required String title,
    required int index,
    required SettingChoiceModel choice,
  }) {
    return ValueListenableBuilder<int>(
      valueListenable: _selectedIndexNotifier,
      builder: (context, selectedIndex, child) {
        final bool isSelected = selectedIndex == index;

        return GestureDetector(
          onTap: () => _selectChoice(index, choice),
          behavior: HitTestBehavior.opaque,
          child: Row(
            children: [
              Image.asset(
                isSelected
                    ? "assets/icons/single_choice_selected.png"
                    : "assets/icons/single_choice_unselected.png",
                width: 16,
                height: 16,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  color:
                      isSelected ? Colors.white : Colors.white.withOpacity(0.7),
                  height: 16 / 12,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          widget.settingItem.title,
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 14,
            color: Colors.white.withOpacity(0.7),
            height: 16 / 14,
          ),
        ),

        const SizedBox(height: 16),

        // 选项
        Row(
          children: widget.settingItem.choices
              .asMap()
              .entries
              .map((entry) => [
                    _buildRadioOption(
                      title: entry.value.title,
                      index: entry.key,
                      choice: entry.value,
                    ),
                    if (entry.key < widget.settingItem.choices.length - 1)
                      const SizedBox(width: 32),
                  ])
              .expand((element) => element)
              .toList(),
        ),
      ],
    );
  }
}
