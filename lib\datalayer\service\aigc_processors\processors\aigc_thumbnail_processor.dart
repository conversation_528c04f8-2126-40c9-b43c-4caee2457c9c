import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/config/ai_sdk_config.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_task_processor.dart';
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/ffi/services/raw_decoder_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC缩略图生成任务处理器
class AigcThumbnailProcessor extends GenericTaskProcessor<AigcTaskMessage> {
  @override
  String get processorKey => 'thumbnail';

  @override
  Future<void> process(
    AigcTaskMessage message,
    SendPort sendToMain,
    String workerId,
  ) async {
    final startTime = DateTime.now();

    try {
      final fileName = path.basename(message.payload.inputPath);
      PGLog.d('🖼️ 工作器 $workerId 开始处理缩略图文件: $fileName');

      // 发送开始进度
      sendProgress(message, '开始处理缩略图文件', 0.0, sendToMain);

      // 检查输入文件是否存在
      if (!File(message.payload.inputPath).existsSync()) {
        throw Exception('输入文件不存在: ${message.payload.inputPath}');
      }

      // 检测文件类型并选择处理方式
      final output = await _processImageFile(message, sendToMain);

      // 发送成功结果
      sendSuccess(
          message,
          AigcThumbnailTaskResult(
            thumbnailPath: output,
          ),
          startTime,
          sendToMain);
    } catch (e) {
      sendError(message, e.toString(), startTime, sendToMain);
    }
  }

  /// 根据文件类型处理图片文件
  Future<String> _processImageFile(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    final ext = message.payload.inputPath.split('.').last.toLowerCase();

    _prepareOutputPath(message.payload.outputPath);

    // 判断是否为普通图片文件（JPG/JPEG/PNG）
    if (ImageConstants.normalImageExtensions.contains(ext)) {
      return await _processNormalImage(message, sendToMain);
    } else {
      // 使用Raw解码器处理RAW文件
      return await _processRawFile(message, sendToMain);
    }
  }

  /// 如果文件夹不存在就需要创建出来
  void _prepareOutputPath(String outputPath) {
    final outputDir = path.dirname(outputPath);

    // 创建输出目录
    final outputDirectory = Directory(outputDir);
    if (!outputDirectory.existsSync()) {
      outputDirectory.createSync(recursive: true);
    }
  }

  /// 验证输出文件是否成功生成
  void _validateOutputFile(String outputPath) {
    if (!File(outputPath).existsSync()) {
      throw Exception('输出文件生成失败: $outputPath');
    }
  }

  /// 处理普通图片文件（JPG/JPEG/PNG）- 生成缩略图
  Future<String> _processNormalImage(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    sendProgress(message, '初始化图像处理器', 0.1, sendToMain);

    // 确保图像处理器已初始化
    await _ensureImageProcessorInitialized(sendToMain, message);

    // 输出路径
    final outputPath = message.payload.outputPath;

    sendProgress(message, '开始处理缩略图', 0.2, sendToMain);

    try {
      sendProgress(message, '生成缩略图(${ImageConstants.aigcThumbnailSize}px)', 0.3,
          sendToMain);

      // 使用EXIF缩略图提取，如果失败则从大图生成
      await ImageProcessorService.extractExifThumbnail(
        message.payload.inputPath,
        ImageConstants.aigcThumbnailSize,
        outputPath,
      );

      // 有可能获取不到exif里面的缩略图数据，那么还是需要从大图拿取一次
      if (!File(outputPath).existsSync()) {
        const thumbnailConfig = ImageProcessorConfig(
          targetWidth: ImageConstants.aigcThumbnailSize,
          targetHeight: ImageConstants.aigcThumbnailSize,
          maintainAspect: true,
          quality: 95,
          outputFormat: 'jpg',
          interpolation: ImageInterpolation.nearest,
        );

        debugPrint('🐅 缩略图输入: ${message.payload.inputPath} -- 输出: $outputPath');

        await ImageProcessorService.processFile(
          message.payload.inputPath,
          outputPath,
          config: thumbnailConfig,
        );
      }

      sendProgress(message, '缩略图生成完成', 0.6, sendToMain);

      // 验证输出文件是否生成
      _validateOutputFile(outputPath);

      sendProgress(message, '缩略图校验完成', 1.0, sendToMain);

      // 返回缩略图路径作为主要输出
      return outputPath;
    } on ImageProcessorException catch (e) {
      throw Exception('解码失败: ${e.message} (错误码: ${e.errorCode})');
    } catch (e) {
      throw Exception('文件处理失败: $e');
    }
  }

  /// 确保图像处理器已初始化
  Future<void> _ensureImageProcessorInitialized(
    SendPort sendToMain,
    AigcTaskMessage message,
  ) async {
    if (!ImageProcessorService.isInitialized) {
      sendProgress(message, '初始化图像处理器', 0.1, sendToMain);

      try {
        await ImageProcessorService.initialize();
        sendProgress(message, '图像处理器初始化完成', 0.15, sendToMain);
      } catch (e) {
        debugPrint('图像处理器初始化失败，将使用Flutter回退: $e');
        // 不抛出异常，让调用方决定是否回退
      }
    } else {
      sendProgress(message, '图像处理器已就绪', 0.1, sendToMain);
    }
  }

  /// 确保Raw解码器已初始化
  Future<void> _ensureRawDecoderInitialized(
    SendPort sendToMain,
    AigcTaskMessage message,
  ) async {
    if (!RawDecoderService.isInitialized) {
      sendProgress(message, '初始化RAW解码器', 0.1, sendToMain);

      try {
        await RawDecoderService.initialize(
          key: AISDKDevConfig.activeConfig['sdkKey'],
        );
        sendProgress(message, 'RAW解码器初始化完成', 0.2, sendToMain);
      } catch (e) {
        throw Exception('RAW解码器初始化失败: $e');
      }
    } else {
      sendProgress(message, 'RAW解码器已就绪', 0.1, sendToMain);
    }
  }

  /// 使用Raw解码器处理文件 - 生成缩略图
  Future<String> _processRawFile(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    // 确保Raw解码器已初始化
    await _ensureRawDecoderInitialized(sendToMain, message);
    await _ensureImageProcessorInitialized(sendToMain, message);

    // 输出路径
    final outputPath = message.payload.outputPath;

    sendProgress(message, '开始RAW解码', 0.2, sendToMain);

    try {
      // 生成RAW缩略图
      sendProgress(message, '生成RAW缩略图(${ImageConstants.aigcThumbnailSize}px)',
          0.3, sendToMain);

      const thumbnailConfig = RawDecoderConfig(
        bitDepth: 8, // 8位深度适合显示
        maxDim: ImageConstants.aigcThumbnailSize, // 限制最大尺寸，提高处理速度
        thumbnail: true, // 启用缩略图模式
        denoise: false, // 启用降噪
        clarity: false, // 不启用锐化，保持处理速度
        exposure: 0.0, // 无曝光补偿
        outputFormat: 'jpg', // JPEG格式，文件更小
      );

      await RawDecoderService.processFile(
        message.payload.inputPath,
        outputPath,
        config: thumbnailConfig,
      );

      sendProgress(message, 'RAW缩略图生成完成', 0.6, sendToMain);

      // 验证输出文件是否生成
      _validateOutputFile(outputPath);

      sendProgress(message, 'RAW缩略图校验完成', 1.0, sendToMain);

      // 返回缩略图路径作为主要输出
      return outputPath;
    } on RawDecoderException catch (e) {
      throw Exception('RAW解码失败: ${e.message} (错误码: ${e.errorCode})');
    } catch (e) {
      throw Exception('文件处理失败: $e');
    }
  }
}
