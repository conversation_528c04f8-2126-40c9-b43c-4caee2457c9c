import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/profile/use_case/login_use_case.dart';
import 'package:turing_art/ui/profile/use_case/logout_use_case.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';

/// 认证相关用例提供者
class AuthUseCaseProvider {
  /// 登录用例
  final LoginUseCase loginUseCase;

  /// 登出用例
  final LogoutUseCase logoutUseCase;

  /// 构造函数
  AuthUseCaseProvider({
    required AuthRepository authRepository,
    required CurrentUserRepository currentUserRepository,
    required AccountRepository accountRepository,
    required OpsCustomTableRepository customTableRepository,
    required ProjectRepository projectRepository,
    PurchaseStateProvider? purchaseStateProvider,
    AccountRightsStateProvider? accountRightsStateProvider,
    UnityController? unityController,
    UnityUseCaseProvider? unityUseCaseProvider,
    NavigatorService? navigatorService,
  })  : loginUseCase = LoginUseCase(
          authRepository,
          currentUserRepository,
          customTableRepository,
        ),
        logoutUseCase = LogoutUseCase(
          authRepository,
          currentUserRepository,
          accountRepository,
          projectRepository,
          purchaseStateProvider: purchaseStateProvider,
          accountRightsStateProvider: accountRightsStateProvider,
          unityController: unityController,
          unityUseCaseProvider: unityUseCaseProvider,
          navigatorService: navigatorService,
        );
}
