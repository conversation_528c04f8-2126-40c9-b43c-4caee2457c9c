import 'dart:ui' as ui;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_painter_path_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_painter_path_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/painter/aigc_path_painter.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC绘制路径组件
///
/// 负责处理鼠标左右键绘制和路径显示
class AigcPainterPathWidget extends StatefulWidget {
  /// 构造函数
  const AigcPainterPathWidget({super.key});

  @override
  State<AigcPainterPathWidget> createState() => _AigcPainterPathWidgetState();
}

class _AigcPainterPathWidgetState extends State<AigcPainterPathWidget> {
  /// 路径绘制ViewModel
  late AigcPainterPathViewModel _viewModel;

  /// 是否正在绘制
  bool _isDrawing = false;

  /// 是否正在擦除
  bool _isErasing = false;

  @override
  void initState() {
    super.initState();

    // 初始化ViewModel
    final pathProvider = context.read<AigcPainterPathProvider>();
    final controlProvider = context.read<AigcEditingControlProvider>();
    final regionalFrameProvider = context.read<AigcRegionalFrameProvider>();
    _viewModel = AigcPainterPathViewModel(
      provider: pathProvider,
      controlProvider: controlProvider,
      regionalFrameProvider: regionalFrameProvider,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<AigcPainterPathProvider, AigcEditingControlProvider,
        AigcCanvasPainterProvider>(
      builder: (context, pathProvider, controlProvider, canvasProvider, child) {
        if (controlProvider.isInSmartBoxSelectionMode) {
          return const SizedBox.shrink();
        }
        return Listener(
          onPointerDown: _handlePointerDown,
          onPointerMove: _handlePointerMove,
          onPointerUp: _handlePointerUp,
          onPointerCancel: _handlePointerCancel,
          child: CustomPaint(
            painter: AigcPathPainter(
              drawingBatches: pathProvider.drawingBatches,
              currentBatch: pathProvider.currentBatch,
              showUserPaths: pathProvider.showUserPaths,
              originalCanvasSize: canvasProvider.contentSize,
              brushSize: controlProvider.brushSize,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  /// 处理指针按下事件
  void _handlePointerDown(PointerDownEvent event) {
    if (_isDrawing || _isErasing) {
      return;
    }

    // 检查Ctrl键是否按下，如果按下则不处理绘制操作
    final bool isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    if (isCtrlPressed) {
      return;
    }

    final localPosition = event.localPosition;
    final canvasProvider = context.read<AigcCanvasPainterProvider>();
    final currentScale = canvasProvider.scale;

    // 检查鼠标按键类型
    if (event.buttons == kPrimaryMouseButton) {
      // 左键 - 开始绘制
      _handleLeftButtonDown(localPosition, currentScale);
    } else if (event.buttons == kSecondaryMouseButton) {
      // 右键 - 开始擦除
      _startErasing(localPosition, currentScale);
    }
  }

  /// 处理指针移动事件
  void _handlePointerMove(PointerMoveEvent event) {
    final localPosition = event.localPosition;

    if (_isDrawing) {
      _handleLeftButtonDrag(localPosition);
    } else if (_isErasing) {
      _continueErasing(localPosition);
    }
  }

  /// 处理指针抬起事件
  void _handlePointerUp(PointerUpEvent event) {
    if (_isDrawing) {
      _handleLeftButtonUp();
    } else if (_isErasing) {
      _endErasing();
    }
  }

  /// 处理指针取消事件
  void _handlePointerCancel(PointerCancelEvent event) {
    if (_isDrawing) {
      _handleLeftButtonUp();
    } else if (_isErasing) {
      _endErasing();
    }
  }

  /// 处理鼠标左键按下
  void _handleLeftButtonDown(Offset localPosition, double scale) {
    if (mounted) {
      setState(() {
        _isDrawing = true;
      });
    }
    _viewModel.startDrawing(localPosition, scale);
    // 更新触摸事件处理器
    _viewModel.updateTouchLogicFrame(localPosition);
  }

  /// 处理鼠标左键拖拽
  void _handleLeftButtonDrag(Offset localPosition) {
    if (_isDrawing) {
      _viewModel.continueDrawing(localPosition);
    }
  }

  /// 处理鼠标左键松开
  void _handleLeftButtonUp() {
    if (_isDrawing) {
      if (mounted) {
        setState(() {
          _isDrawing = false;
        });
      }
      _viewModel.endDrawing();

      // 生成图片并回调，然后隐藏路径
      _generateAndCallbackPathImage(true).then((_) {
        // 图片生成完成后隐藏所有路径
        _hideAllPaths();
      });
    }
  }

  /// 开始擦除
  void _startErasing(Offset position, double scale) {
    if (mounted) {
      setState(() {
        _isErasing = true;
      });
    }
    _viewModel.startErasing(position, scale);
    // 更新触摸事件处理器
    _viewModel.updateTouchLogicFrame(position);
  }

  /// 继续擦除
  void _continueErasing(Offset position) {
    if (_isErasing) {
      _viewModel.continueErasing(position);
    }
  }

  /// 结束擦除
  void _endErasing() {
    if (_isErasing) {
      if (mounted) {
        setState(() {
          _isErasing = false;
        });
      }
      _viewModel.endErasing();

      // 生成图片并回调，然后隐藏路径
      _generateAndCallbackPathImage(false).then((_) {
        // 图片生成完成后隐藏所有路径
        _hideAllPaths();
      });
    }
  }

  /// 隐藏所有路径
  void _hideAllPaths() {
    _viewModel.clearAllDrawing();
  }

  /// 生成路径图片并回调到Provider
  Future<void> _generateAndCallbackPathImage(bool isBrushMode) async {
    if (!mounted) {
      return;
    }

    try {
      final controlProvider = context.read<AigcEditingControlProvider>();
      final regionalFrameProvider = context.read<AigcRegionalFrameProvider>();

      // 获取AigcCanvasPainterProvider的contentSize作为图片尺寸
      final canvasProvider = context.read<AigcCanvasPainterProvider>();
      final Size? contentSize = canvasProvider.contentSize;

      // 验证尺寸有效性
      if (contentSize == null ||
          contentSize.width <= 0 ||
          contentSize.height <= 0) {
        return;
      }

      final Size size = contentSize;

      // 获取最后一次绘制的路径
      final pathProvider = context.read<AigcPainterPathProvider>();

      DrawingBatch? lastBatch;
      if (pathProvider.drawingBatches.isNotEmpty) {
        lastBatch = pathProvider.drawingBatches.last;
      }

      if (lastBatch == null || lastBatch.points.isEmpty) {
        return;
      }

      // 创建用于生成图片的画家
      final pathPainter = AigcPathPainter(
        drawingBatches: [lastBatch], // 只传递最后一次的路径
        currentBatch: null,
        showUserPaths: true,
        originalCanvasSize: canvasProvider.contentSize,
        brushSize: _viewModel.getRelativeDrawImageBrushSize(
            controlProvider.brushSize, canvasProvider.scale),
      );

      // 生成透明背景的图片
      final ui.Image? pathImage = await _viewModel.generateTransparentPathImage(
          pathPainter, size, canvasProvider.scale);
      if (pathImage == null) {
        return;
      }

      // 临时保存图片到文件
      await _saveImageToTempFolder(pathImage, isBrushMode);

      // 转换为字节数据
      final Uint8List? imageBytes =
          await _viewModel.convertImageToBytes(pathImage);

      if (imageBytes != null) {
        // 回调到AigcEditingImageProvider
        if (mounted) {
          try {
            final position = regionalFrameProvider.currentLogicFrame
                ?.getRelativePosition(canvasProvider.scale);
            final rect = regionalFrameProvider.currentLogicFrame
                ?.getRelativeRect(canvasProvider.scale);
            AigcRegionalFrameDataInfo? regionalFrameDataInfo;
            if (position != null && rect != null) {
              regionalFrameDataInfo = AigcRegionalFrameDataInfo(
                position: position,
                rect: rect,
              );
            }
            context
                .read<AigcEditingImageProvider>()
                .updateMattingMaskBrushStroke(
                  regionalFrameDataInfo,
                  imageBytes,
                  isBrushMode: isBrushMode,
                );
            // 添加历史记录
          } catch (e) {
            // 忽略错误，可能是因为context已经被销毁
            PGLog.d('AigcPainterPathWidget: 回调Provider时发生错误: $e');
          }
        }
      }
    } catch (e) {
      PGLog.d('生成路径图片时出错: $e');
    }
  }

  /// 将图片保存到临时文件夹
  Future<void> _saveImageToTempFolder(ui.Image image, bool isBrushMode) async {
    // try {
    //   debugPrint('开始保存图片到临时文件夹...');

    //   // 使用系统临时目录
    //   final tempDir = Directory.systemTemp;

    //   // 生成文件名
    //   final timestamp = DateTime.now().millisecondsSinceEpoch;
    //   final mode = isBrushMode ? 'brush' : 'erase';
    //   final fileName = 'aigc_path_image_${mode}_$timestamp.png';
    //   final filePath = path.join("${tempDir.path}/turing/", fileName);

    //   // 将图片转换为PNG格式的字节数据
    //   final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    //   if (byteData == null) {
    //     debugPrint('无法获取PNG格式的图片数据');
    //     return;
    //   }

    //   // 保存到文件
    //   final file = File(filePath);
    //   await file.writeAsBytes(byteData.buffer.asUint8List());

    //   debugPrint('图片已保存到: $filePath');
    //   debugPrint('文件大小: ${byteData.lengthInBytes} 字节');
    // } catch (e) {
    //   debugPrint('保存图片到临时文件夹时出错: $e');
    // }
  }
}
