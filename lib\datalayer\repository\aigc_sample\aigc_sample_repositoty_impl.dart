import 'dart:async';

import 'package:dio/dio.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_status_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_request.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_local_data_service.dart';
import 'package:turing_art/datalayer/service/aigc_sample/aigc_sample_service.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcSampleRepositoryImpl extends AigcSampleRepository {
  final AigcSampleService _aigcSampleService;
  final AigcLocalDataService _aigcLocalDataService = AigcLocalDataService();

  // StreamController用于通知数据变更
  final StreamController<String> _dataChangeController =
      StreamController<String>.broadcast();

  // 缓存详情数据，用于判断是否需要刷新（列表是返回状态列表更新上层数据并刷新）
  final Map<String, AigcSampleModel> _sampleDetailCache = {};

  // 缓存打样状态数据，用于判断是否需要刷新（项目id对应打样状态列表）
  List<AigcPcPresetsLoopModel> _sampleStatusCache = [];

  // 缓存打样导出状态数据，用于判断是否需要刷新（项目id对应导出状态列表）（打样列表包含别人的和自己的）
  List<AigcSampleExportStatusModel> _exportStatusCache = [];

  // 缓存打样导出状态数据，用于判断是否需要刷新（我的打样列表）
  List<AigcSampleExportStatusModel> _myExportStatusCache = [];

  AigcSampleRepositoryImpl(
    this._aigcSampleService,
  );

  @override
  Stream<String> get dataChangeStream => _dataChangeController.stream;

  @override
  Future<AigcSampleModel> createAigcSample(AigcSampleRequest request) async {
    final model = await _aigcSampleService.createAigcSample(request);
    return model;
  }

  @override
  Future<List<AigcSampleModel>> getAigcSampleList(String projectId) async {
    try {
      var samples = await _aigcSampleService.getAigcSampleList(projectId);
      // 保存到本地
      await _aigcLocalDataService.saveAigcSamples(projectId, samples);
      PGLog.d('获取打样列表成功，保存打样列表到本地: ${samples.length}');
      return samples;
    } catch (e) {
      PGLog.e('刷新打样列表失败: $e');
      // 如果网络请求失败，则从本地读取
      final samples = await _aigcLocalDataService.getAigcSamples(projectId);
      PGLog.d('获取打样列表失败，从本地读取打样列表: ${samples.length}');
      return samples;
    }
  }

  @override
  AigcSampleModel? getLocalCacheSampleDetail(String proofingId) {
    return _sampleDetailCache[proofingId];
  }

  @override
  Future<AigcSampleModel> getAigcSampleDetail(String proofingId) async {
    try {
      final detail = await _aigcSampleService.getAigcSampleDetail(proofingId);

      // 更新缓存
      _sampleDetailCache[proofingId] = detail;

      // 通知详情刷新，并传递proofingId
      _dataChangeController
          .add('${AigcRequestConst.detailRefresh}:$proofingId');

      return detail;
    } catch (e) {
      PGLog.e('刷新打样详情失败: $e');
      // 如果出错，尝试从缓存获取
      if (_sampleDetailCache.containsKey(proofingId)) {
        return _sampleDetailCache[proofingId]!;
      }
      // 其实用于detail轮询的时候，或者主动刷新详情的时候，如果样片不存在，则通知上层停止轮询并提示
      if (_dealSampleIsNotExistsError(e)) {
        _dataChangeController
            .add(AigcRequestConst.detailRefreshFailForNotExist);
      }
      rethrow;
    }
  }

  /// 统一处理样片不存在的情况(可能跨设备已经被删除)
  bool _dealSampleIsNotExistsError(Object e) {
    if (e is DioException && e.response?.data is Map<String, dynamic>) {
      final data = e.response?.data as Map<String, dynamic>;
      if (data.containsKey('code') && data['code'] == 4001) {
        return true;
      }
      return false;
    }
    return false;
  }

  @override
  Future<void> regenerateAigcSample(String proofingId) async {
    await _aigcSampleService.regenerateAigcSample(proofingId);
    // 清除对应的详情缓存（重新打样后一定会刷新对应详情）
    _sampleDetailCache.remove(proofingId);
  }

  @override
  Future<void> deleteAigcSample(String proofingId) async {
    await _aigcSampleService.deleteAigcSample(proofingId);
    // 清除对应的详情缓存
    _sampleDetailCache.remove(proofingId);
  }

  @override
  Future<void> deleteAigcSampleEffect(
      String proofingId, String effectCode) async {
    await _aigcSampleService.deleteAigcSampleEffect(proofingId, effectCode);
    // 清除对应的详情缓存（删除打样效果后一定会刷新详情）
    _sampleDetailCache.remove(proofingId);
  }

  @override
  Future<void> deleteAigcSampleExport(
      List<AigcSampleListExportRequest> request) async {
    return _aigcSampleService.deleteAigcSampleExport(request);
  }

  @override
  Future<List<AigcSampleExportProjectModel>> getAigcSampleExportList() async {
    return _aigcSampleService.getAigcSampleExportList();
  }

  @override
  Future<void> exportAigcSample(String proofingId, String effectCode) async {
    await _aigcSampleService.exportAigcSample(proofingId, effectCode);
  }

  @override
  Future<List<AigcPcPresetsLoopModel>> getAigcSampleProofingStatus(
      List<String> ids,
      [String? projectId]) async {
    try {
      final statusList =
          await _aigcSampleService.getAigcSampleProofingStatus(ids);

      // 更新缓存,用于判断是否需要刷新
      // if (projectId != null && statusList.isNotEmpty) {
      //   _sampleStatusCache[projectId] = statusList;
      // }
      _sampleStatusCache = statusList;
      // 通知状态更新，并传递projectId(可能切换了项目,需要回传)
      // final projectInfo = projectId != null ? ':$projectId' : '';
      _dataChangeController.add(AigcRequestConst.listProofingStatusRefresh);

      return statusList;
    } catch (e) {
      PGLog.e('获取打样状态失败: $e');
      return [];
    }
  }

  @override
  Future<List<AigcSampleExportStatusModel>> getAigcSampleExportStatus(
      List<AigcSampleListExportRequest> requests,
      {bool isMyExportList = false}) async {
    try {
      final statusList =
          await _aigcSampleService.getAigcSampleExportStatus(requests);

      // 更新缓存,用于判断是否需要刷新
      if (statusList.isNotEmpty) {
        if (!isMyExportList) {
          _exportStatusCache = statusList;
          // 通知导出状态更新，并传递projectId(可能切换了项目,需要回传)
          _dataChangeController.add(
              '${AigcRequestConst.proofingListExportStatusRefresh}:$isMyExportList');
        } else {
          _myExportStatusCache = statusList;
          // 我的打样列表导出状态刷新
          _dataChangeController.add(AigcRequestConst.myListExportStatusRefresh);
        }
      }
      return statusList;
    } catch (e) {
      PGLog.e('获取打样导出状态失败: $e');
      return [];
    }
  }

  @override
  List<AigcSampleExportStatusModel> getLocalAigcSampleExportStatusList(
      String? projectId) {
    if (projectId != null && projectId.isNotEmpty) {
      return _exportStatusCache;
    }
    return _myExportStatusCache;
  }

  @override
  List<AigcPcPresetsLoopModel> getLocalAigcSampleProofingStatusList(
      String projectId) {
    return _sampleStatusCache;
  }

  @override
  Future<List<AigcSampleProjectModel>> getAigcSampleProjectList() async {
    return _aigcSampleService.getAigcSampleProjectList();
  }

  @override
  Future<void> updateAigcSampleInfo(String proofingId, String imageUrl) async {
    return _aigcSampleService.updateAigcSampleInfo(proofingId, imageUrl);
  }

  @override
  Future<void> updateAigcSampleDownloadStatus(
      List<AigcSampleListExportRequest> requests) async {
    return _aigcSampleService.updateAigcSampleDownloadStatus(requests);
  }
}
