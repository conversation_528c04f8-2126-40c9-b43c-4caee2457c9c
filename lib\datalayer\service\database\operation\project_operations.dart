import 'package:turing_art/datalayer/service/database/dao/project_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/workspace_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/workspace_file_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'database_operation.dart';

class GetAllProjectsOperation
    extends DatabaseOperation<List<ProjectEntityData>> {
  @override
  String get operation => 'getAllProjects';

  @override
  Future<List<ProjectEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getAllProjects(isDelete: params['isDelete']);
  }
}

class GetUserAllProjectsOperation
    extends DatabaseOperation<List<ProjectEntityData>> {
  @override
  String get operation => 'getUserAllProjects';

  @override
  Future<List<ProjectEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getUserAllProjects(params['userId']);
  }
}

class GetUserAllProjectsByTypeOperation
    extends DatabaseOperation<List<ProjectEntityData>> {
  @override
  String get operation => 'getUserAllProjectsByType';

  @override
  Future<List<ProjectEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getUserAllProjectsByType(params['userId'], params['projectType']);
  }
}

class GetProjectByIdOperation extends DatabaseOperation<ProjectEntityData?> {
  @override
  String get operation => 'getProjectById';

  @override
  Future<ProjectEntityData?> execute(DataBase db, Map<String, dynamic> params) {
    return db.getProjectById(params['projectId']);
  }
}

class InsertProjectOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertProject';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertProject(params['project']);
  }
}

class UpdateProjectOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateProject';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateProject(params['project']);
  }
}

class DeleteProjectOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteProject';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteProject(params['projectId']);
  }
}

class InsertProjectWithWorkspaceOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertProjectWithWorkspace';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.insertProject(params['project']);
      await db.insertWorkspace(params['workspace']);
      if ((params['files'] as List).isNotEmpty) {
        await db.insertOrUpdateWorkspaceFiles(params['files']);
      }
    });
  }
}

class UpdateProjectWithWorkspaceOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateProjectWithWorkspace';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.updateProject(params['project']);
      await db.updateWorkspace(params['workspace']);
      if ((params['files'] as List).isNotEmpty) {
        await db.insertOrUpdateWorkspaceFiles(params['files']);
      }
    });
  }
}

class DeleteProjectAndWorkspaceAndFilesOperation
    extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteProjectAndWorkspaceAndFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.deleteProject(params['projectId']);
      await db.deleteWorkspace(params['projectId']);
      await db.deleteAllWorkspaceFiles(params['projectId']);
    });
  }
}

class DeleteProjectsAndWorkspaceAndFilesOperation
    extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteProjectsAndWorkspacesAndFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.deleteProjects(params['projectIds']);
      await db.deleteWorkspaces(params['projectIds']);
      await db.deleteAllWorkspacesFiles(params['projectIds']);
    });
  }
}

class GetProjectAuthorByIdOperation extends DatabaseOperation<String?> {
  @override
  String get operation => 'getProjectAuthorById';

  @override
  Future<String?> execute(DataBase db, Map<String, dynamic> params) {
    return db.getProjectAuthorById(params['projectId']);
  }
}
