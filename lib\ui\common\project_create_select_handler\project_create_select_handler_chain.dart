import 'dart:async';
import 'dart:io';

import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理器结果类型，用于指示处理结果和后续操作
enum HandlerResultType {
  /// 继续处理链中的下一个处理器
  continueChain,

  /// 中断处理链，返回成功
  breakWithSuccess,

  /// 中断处理链，返回错误
  breakWithError,

  /// 需要等待某些操作，暂停处理链
  waitForCallback,
}

/// 处理器结果
class HandlerResult {
  HandlerResult({
    required this.type,
    this.errorType = ProcessFilesErrorType.other,
  });

  /// 成功结果
  static HandlerResult success() =>
      HandlerResult(type: HandlerResultType.continueChain);

  /// 中断并返回成功
  static HandlerResult breakWithSuccess() =>
      HandlerResult(type: HandlerResultType.breakWithSuccess);

  /// 中断并返回错误
  static HandlerResult breakWithError(
          {ProcessFilesErrorType errorType = ProcessFilesErrorType.other}) =>
      HandlerResult(
          type: HandlerResultType.breakWithError, errorType: errorType);

  /// 等待回调
  static HandlerResult waitForCallback() =>
      HandlerResult(type: HandlerResultType.waitForCallback);

  final HandlerResultType type;
  final ProcessFilesErrorType errorType;
}

/// 项目处理上下文，包含处理过程中需要的所有数据
class ProjectCreateSelectHandlerContext {
  ProjectCreateSelectHandlerContext({
    required this.files,
    this.projectName,
    this.projectId,
    this.isMigrated,
    this.historyFiles,
    this.fileList,
    this.isOverwriteMode = false,
    this.forBatch = false, // 是否为批处理模式
  });

  final List<File> files;
  final String? projectName;
  final String? projectId;
  final bool? isMigrated;
  final Map<String, List<int>>? historyFiles;
  final List<FileItem>? fileList;
  final bool isOverwriteMode; // 是否为覆盖模式
  final bool forBatch; // 是否为批处理模式

  // 处理过程中产生的数据
  String? createdProjectId;
  bool isUnityInitialized = false;
  bool isWaitingForUnity = false;
  Map<String, String>? fileIdToHistoryIdMap; // 文件ID到历史ID的映射

  // 错误类型
  ProcessFilesErrorType errorType = ProcessFilesErrorType.other;

  // 完成后回调
  Function? onComplete;

  // 项目创建成功后回调，传递创建的项目ID
  Function(String createdProjectId)? onProjectCreated;

  // 创建一个新的上下文对象，复制原有数据并更新部分字段
  ProjectCreateSelectHandlerContext copyWith({
    List<File>? files,
    String? projectName,
    String? projectId,
    bool? isMigrated,
    Map<String, List<int>>? historyFiles,
    List<FileItem>? fileList,
    bool? isOverwriteMode,
    bool? forBatch, // 新增：批处理模式参数
    String? createdProjectId,
    bool? isUnityInitialized,
    bool? isWaitingForUnity,
    Map<String, String>? fileIdToHistoryIdMap,
    ProcessFilesErrorType? errorType,
    Function? onComplete,
    Function(String createdProjectId)? onProjectCreated,
  }) {
    final result = ProjectCreateSelectHandlerContext(
      files: files ?? this.files,
      projectName: projectName ?? this.projectName,
      projectId: projectId ?? this.projectId,
      isMigrated: isMigrated ?? this.isMigrated,
      historyFiles: historyFiles ?? this.historyFiles,
      fileList: fileList ?? this.fileList,
      isOverwriteMode: isOverwriteMode ?? this.isOverwriteMode,
      forBatch: forBatch ?? this.forBatch, // 新增：复制批处理模式字段
    );
    result.createdProjectId = createdProjectId ?? this.createdProjectId;
    result.isUnityInitialized = isUnityInitialized ?? this.isUnityInitialized;
    result.isWaitingForUnity = isWaitingForUnity ?? this.isWaitingForUnity;
    result.fileIdToHistoryIdMap =
        fileIdToHistoryIdMap ?? this.fileIdToHistoryIdMap;
    result.errorType = errorType ?? this.errorType;
    result.onComplete = onComplete ?? this.onComplete;
    result.onProjectCreated = onProjectCreated ?? this.onProjectCreated;
    return result;
  }
}

/// 项目处理器接口
abstract class ProjectCreateSelectHandler {
  /// 处理方法
  /// 返回值：处理结果，指示后续操作
  Future<HandlerResult> handle(ProjectCreateSelectHandlerContext context);

  /// 设置下一个处理器
  void setNext(ProjectCreateSelectHandler handler);

  /// 获取下一个处理器
  ProjectCreateSelectHandler? getNext();

  /// 获取处理器类型
  @override
  Type get runtimeType;

  /// 获取处理器名称，用于日志
  String get name;
}

/// 项目处理器基类
abstract class BaseProjectCreateSelectHandler
    implements ProjectCreateSelectHandler {
  ProjectCreateSelectHandler? _next;

  @override
  void setNext(ProjectCreateSelectHandler handler) {
    _next = handler;
  }

  @override
  ProjectCreateSelectHandler? getNext() {
    return _next;
  }

  @override
  String get name => runtimeType.toString();

  /// 执行下一个处理器
  Future<HandlerResult> handleNext(
      ProjectCreateSelectHandlerContext context) async {
    if (_next != null) {
      return await _next!.handle(context);
    }
    return HandlerResult.success();
  }
}

/// 项目处理器链
class ProjectHandlerChain {
  ProjectHandlerChain();

  ProjectCreateSelectHandler? _firstHandler;
  ProjectCreateSelectHandler? _lastHandler;
  bool _isProcessing = false;

  /// 添加处理器到链末尾
  ProjectHandlerChain addHandler(ProjectCreateSelectHandler handler) {
    if (_firstHandler == null) {
      _firstHandler = handler;
      _lastHandler = handler;
    } else {
      _lastHandler!.setNext(handler);
      _lastHandler = handler;
    }
    return this;
  }

  /// 是否正在处理
  bool get isProcessing => _isProcessing;

  /// 执行处理链
  /// 返回初始处理结果，如果需要等待回调，将通过onComplete回调返回最终结果
  Future<ProcessFilesUiStatus> process(
      ProjectCreateSelectHandlerContext context) async {
    if (_firstHandler == null) {
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    }

    if (_isProcessing) {
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.processing,
      );
    }

    _isProcessing = true;

    try {
      final result = await _processChain(context);

      // 如果是等待回调，不立即返回最终结果
      if (result.type == HandlerResultType.waitForCallback) {
        return const ProcessFilesUiStatus.success();
      }

      // 根据结果类型返回对应的状态
      final status = _getStatusFromResult(result);

      return status;
    } catch (e) {
      PGLog.e('项目处理链执行出错: $e');
      const status = ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );

      return status;
    } finally {
      _isProcessing = false;
    }
  }

  /// 内部处理链执行方法
  Future<HandlerResult> _processChain(
      ProjectCreateSelectHandlerContext context) async {
    if (_firstHandler == null) {
      return HandlerResult.breakWithError();
    }

    try {
      return await _firstHandler!.handle(context);
    } catch (e) {
      PGLog.e('项目处理链执行出错: $e');
      return HandlerResult.breakWithError();
    }
  }

  /// 从处理结果获取UI状态
  ProcessFilesUiStatus _getStatusFromResult(HandlerResult result) {
    switch (result.type) {
      case HandlerResultType.breakWithSuccess:
      case HandlerResultType.continueChain:
        return const ProcessFilesUiStatus.success();
      case HandlerResultType.breakWithError:
        return ProcessFilesUiStatus.error(
          errorType: result.errorType,
        );
      case HandlerResultType.waitForCallback:
        // 这种情况不应该出现在这里，已经在外层处理
        return const ProcessFilesUiStatus.success();
    }
  }
}
