import 'dart:ffi';
import 'dart:typed_data';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

/// SalientMatters FFI绑定类
/// 负责底层动态库的加载和原生函数的调用
class SalientMattersBindings {
  static DynamicLibrary? _lib;
  static bool _initialized = false;

  // 函数指针定义
  static late final int Function(
          Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>, int)
      _salientMattersInit;

  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersOptions>,
      Pointer<Utf8>,
      int) _salientMattersInteractive;

  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersOptions>,
      Pointer<Utf8>,
      int) _salientMattersMatting;

  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersOptions>,
      Pointer<Utf8>,
      int) _salientMattersTracking;

  static late final void Function() _salientMattersFree;
  static late final void Function(Pointer<SalientMattersOptions>)
      _salientMattersGetDefaultOptions;
  static late final int Function() _salientMattersIsInitialized;

  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<Uint8>,
      Pointer<Uint8>,
      int,
      Pointer<Pointer<Uint8>>,
      Pointer<Pointer<Uint8>>,
      Pointer<Utf8>,
      int) _salientMattersProcessInteractiveMasks;

  // 新增：区域抠图函数指针
  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersRect>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersOptions>,
      Pointer<Utf8>,
      int) _salientMattersRegionalMatting;

  // 新增：区域交互式抠图函数指针
  static late final int Function(
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersRect>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersImage>,
      Pointer<SalientMattersOptions>,
      Pointer<Utf8>,
      int) _salientMattersRegionalInteractive;

  /// 加载动态库
  static bool _loadLibrary() {
    if (_lib != null) {
      return true;
    }

    try {
      _lib = UniversalPlatformLoader.loadSugoiNativeLibrary();

      // 绑定函数
      _bindFunctions();

      return true;
    } catch (e) {
      PGLog.e('加载SalientMatters动态库失败: $e');
      _lib = null;
      return false;
    }
  }

  /// 绑定所有函数
  static void _bindFunctions() {
    _salientMattersInit = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>,
                    Pointer<Utf8>, Int32)>>('SalientMatters_Init')
        .asFunction();

    _salientMattersInteractive = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersOptions>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_Interactive')
        .asFunction();

    _salientMattersMatting = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersOptions>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_Matting')
        .asFunction();

    _salientMattersTracking = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersOptions>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_Tracking')
        .asFunction();

    _salientMattersFree = _lib!
        .lookup<NativeFunction<Void Function()>>('SalientMatters_Free')
        .asFunction();

    _salientMattersGetDefaultOptions = _lib!
        .lookup<NativeFunction<Void Function(Pointer<SalientMattersOptions>)>>(
            'SalientMatters_GetDefaultOptions')
        .asFunction();

    _salientMattersIsInitialized = _lib!
        .lookup<NativeFunction<Int32 Function()>>(
            'SalientMatters_IsInitialized')
        .asFunction();

    _salientMattersProcessInteractiveMasks = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<Uint8>,
                    Pointer<Uint8>,
                    Int32,
                    Pointer<Pointer<Uint8>>,
                    Pointer<Pointer<Uint8>>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_ProcessInteractiveMasks')
        .asFunction();

    // 新增：绑定区域抠图函数
    _salientMattersRegionalMatting = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersRect>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersOptions>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_RegionalMatting')
        .asFunction();

    // 新增：绑定区域交互式抠图函数
    _salientMattersRegionalInteractive = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersRect>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersImage>,
                    Pointer<SalientMattersOptions>,
                    Pointer<Utf8>,
                    Int32)>>('SalientMatters_RegionalInteractive')
        .asFunction();
  }

  /// Flutter实现的图像内存分配函数
  static int allocateImage(
      Pointer<SalientMattersImage> image, int width, int height, int channels) {
    try {
      final dataSize = width * height * channels;
      final step = width * channels;

      // 分配内存
      final dataPtr = malloc<Uint8>(dataSize);

      // 设置图像结构体
      image.ref.data = dataPtr;
      image.ref.width = width;
      image.ref.height = height;
      image.ref.channels = channels;
      image.ref.step = step;

      // 初始化为0
      dataPtr.asTypedList(dataSize).fillRange(0, dataSize, 0);

      return 0; // 成功
    } catch (e) {
      PGLog.e('分配图像内存失败: $e');
      return -1; // 失败
    }
  }

  /// Flutter实现的图像内存释放函数
  static void freeImage(Pointer<SalientMattersImage> image) {
    try {
      if (image.ref.data != nullptr) {
        malloc.free(image.ref.data);
        image.ref.data = nullptr;
        image.ref.width = 0;
        image.ref.height = 0;
        image.ref.channels = 0;
        image.ref.step = 0;
      }
    } catch (e) {
      PGLog.e('释放图像内存失败: $e');
    }
  }

  /// 设置图像数据
  static void _setImageData(
      Pointer<SalientMattersImage> image, ImageData imageData) {
    final dataPtr = malloc<Uint8>(imageData.data.length);
    dataPtr.asTypedList(imageData.data.length).setAll(0, imageData.data);

    image.ref.data = dataPtr;
    image.ref.width = imageData.width;
    image.ref.height = imageData.height;
    image.ref.channels = imageData.channels;
    image.ref.step = imageData.step;
  }

  /// 设置区域数据
  static void _setRectData(
      Pointer<SalientMattersRect> rect, RectData rectData) {
    rect.ref.x = rectData.x;
    rect.ref.y = rectData.y;
    rect.ref.width = rectData.width;
    rect.ref.height = rectData.height;
  }

  /// 初始化SalientMatters
  static SalientMattersResult initialize({
    required String key,
    required String userCode,
    required String prodCode,
  }) {
    if (!_loadLibrary()) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.initFailed,
        errorMessage: '动态库加载失败',
      );
    }

    Pointer<Utf8>? keyPtr;
    Pointer<Utf8>? userCodePtr;
    Pointer<Utf8>? prodCodePtr;
    Pointer<Utf8>? errorMessagePtr;

    try {
      keyPtr = key.toNativeUtf8();
      userCodePtr = userCode.toNativeUtf8();
      prodCodePtr = prodCode.toNativeUtf8();
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      final result = _salientMattersInit(
          keyPtr, userCodePtr, prodCodePtr, errorMessagePtr, 512);

      final errorCode = SalientMattersErrorCode.fromValue(result);
      _initialized = errorCode == SalientMattersErrorCode.success;

      String? errorMessage;
      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      }

      return SalientMattersResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
      );
    } catch (e) {
      PGLog.e('初始化SalientMatters失败: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.initFailed,
        errorMessage: e.toString(),
      );
    } finally {
      if (keyPtr != null) {
        malloc.free(keyPtr);
      }
      if (userCodePtr != null) {
        malloc.free(userCodePtr);
      }
      if (prodCodePtr != null) {
        malloc.free(prodCodePtr);
      }
      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 主体抠图
  static SalientMattersResult matting(
    ImageData image, {
    SalientMattersConfig? config,
  }) {
    if (!_initialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final options = malloc<SalientMattersOptions>();
    final inputImage = malloc<SalientMattersImage>();
    final resultImage = malloc<SalientMattersImage>();
    Pointer<Utf8>? errorMessagePtr;
    Pointer<Uint8>? imageDataPtr;

    try {
      // 设置配置选项
      final cfg = config ?? SalientMattersConfig.defaultConfig;
      options.ref.netSize = cfg.netSize;
      options.ref.type = cfg.type.value;
      options.ref.key = cfg.key.toNativeUtf8();
      options.ref.userCode = cfg.userCode.toNativeUtf8();
      options.ref.prodCode = cfg.prodCode.toNativeUtf8();

      // 设置输出颜色
      for (int i = 0; i < 4 && i < cfg.outputColor.length; i++) {
        options.ref.outputColor[i] = cfg.outputColor[i];
      }

      // 设置输入图像
      imageDataPtr = malloc<Uint8>(image.data.length);
      imageDataPtr.asTypedList(image.data.length).setAll(0, image.data);

      inputImage.ref.data = imageDataPtr;
      inputImage.ref.width = image.width;
      inputImage.ref.height = image.height;
      inputImage.ref.channels = image.channels;
      inputImage.ref.step = image.step;

      // 分配结果图像内存 (RGBA格式，4通道)
      final allocResult =
          allocateImage(resultImage, image.width, image.height, 4);
      if (allocResult != 0) {
        return SalientMattersResult(
          errorCode: SalientMattersErrorCode.fromValue(allocResult),
          errorMessage: '分配结果图像内存失败',
        );
      }

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 执行主体抠图
      final result = _salientMattersMatting(
          inputImage, resultImage, options, errorMessagePtr, 512);

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? resultData;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 获取结果数据 (RGBA格式，4通道)
        final dataSize = resultImage.ref.width * resultImage.ref.height * 4;
        final tempList = resultImage.ref.data.asTypedList(dataSize);
        resultData = Uint8List.fromList(tempList); // 创建数据拷贝
      }

      return SalientMattersResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        imageData: resultData,
      );
    } catch (e) {
      PGLog.e('主体抠图失败: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (options.ref.key != nullptr) {
        malloc.free(options.ref.key);
      }
      if (options.ref.userCode != nullptr) {
        malloc.free(options.ref.userCode);
      }
      if (options.ref.prodCode != nullptr) {
        malloc.free(options.ref.prodCode);
      }
      malloc.free(options);

      if (imageDataPtr != null) {
        malloc.free(imageDataPtr);
      }
      malloc.free(inputImage);

      freeImage(resultImage);
      malloc.free(resultImage);

      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 交互式抠图
  static SalientMattersResult interactive(
    ImageData image,
    ImageData? latestMask,
    ImageData? foreStrokes,
    ImageData? backStrokes, {
    SalientMattersConfig? config,
  }) {
    if (!_initialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final options = malloc<SalientMattersOptions>();
    final inputImage = malloc<SalientMattersImage>();
    final maskImage = malloc<SalientMattersImage>();
    final foreImage = malloc<SalientMattersImage>();
    final backImage = malloc<SalientMattersImage>();
    final resultImage = malloc<SalientMattersImage>();
    Pointer<Utf8>? errorMessagePtr;
    Pointer<Uint8>? imageDataPtr;
    Pointer<Uint8>? maskDataPtr;
    Pointer<Uint8>? foreDataPtr;
    Pointer<Uint8>? backDataPtr;

    try {
      // 设置配置选项
      final cfg = config ?? SalientMattersConfig.defaultConfig;
      options.ref.netSize = cfg.netSize;
      options.ref.type = SalientMattersType.interactive.value;
      options.ref.key = cfg.key.toNativeUtf8();
      options.ref.userCode = cfg.userCode.toNativeUtf8();
      options.ref.prodCode = cfg.prodCode.toNativeUtf8();

      // 设置输出颜色
      for (int i = 0; i < 4 && i < cfg.outputColor.length; i++) {
        options.ref.outputColor[i] = cfg.outputColor[i];
      }

      // 设置输入图像
      imageDataPtr = malloc<Uint8>(image.data.length);
      imageDataPtr.asTypedList(image.data.length).setAll(0, image.data);
      inputImage.ref.data = imageDataPtr;
      inputImage.ref.width = image.width;
      inputImage.ref.height = image.height;
      inputImage.ref.channels = image.channels;
      inputImage.ref.step = image.step;

      // 设置最新蒙版
      if (latestMask != null) {
        maskDataPtr = malloc<Uint8>(latestMask.data.length);
        maskDataPtr
            .asTypedList(latestMask.data.length)
            .setAll(0, latestMask.data);
        maskImage.ref.data = maskDataPtr;
        maskImage.ref.width = latestMask.width;
        maskImage.ref.height = latestMask.height;
        maskImage.ref.channels = latestMask.channels;
        maskImage.ref.step = latestMask.step;
      } else {
        maskImage.ref.data = nullptr;
      }

      // 设置前景笔触
      if (foreStrokes != null) {
        foreDataPtr = malloc<Uint8>(foreStrokes.data.length);
        foreDataPtr
            .asTypedList(foreStrokes.data.length)
            .setAll(0, foreStrokes.data);
        foreImage.ref.data = foreDataPtr;
        foreImage.ref.width = foreStrokes.width;
        foreImage.ref.height = foreStrokes.height;
        foreImage.ref.channels = foreStrokes.channels;
        foreImage.ref.step = foreStrokes.step;
      } else {
        foreImage.ref.data = nullptr;
      }

      // 设置背景笔触
      if (backStrokes != null) {
        backDataPtr = malloc<Uint8>(backStrokes.data.length);
        backDataPtr
            .asTypedList(backStrokes.data.length)
            .setAll(0, backStrokes.data);
        backImage.ref.data = backDataPtr;
        backImage.ref.width = backStrokes.width;
        backImage.ref.height = backStrokes.height;
        backImage.ref.channels = backStrokes.channels;
        backImage.ref.step = backStrokes.step;
      } else {
        backImage.ref.data = nullptr;
      }

      // 分配结果图像内存 (RGBA格式，4通道)
      final allocResult =
          allocateImage(resultImage, image.width, image.height, 4);
      if (allocResult != 0) {
        return SalientMattersResult(
          errorCode: SalientMattersErrorCode.fromValue(allocResult),
          errorMessage: '分配结果图像内存失败',
        );
      }

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 执行交互式抠图
      final result = _salientMattersInteractive(
        inputImage,
        latestMask != null ? maskImage : nullptr,
        foreStrokes != null ? foreImage : nullptr,
        backStrokes != null ? backImage : nullptr,
        resultImage,
        options,
        errorMessagePtr,
        512,
      );

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? resultData;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 获取结果数据 (RGBA格式，4通道)
        final dataSize = resultImage.ref.width * resultImage.ref.height * 4;
        final tempList = resultImage.ref.data.asTypedList(dataSize);
        resultData = Uint8List.fromList(tempList); // 创建数据拷贝
      }

      return SalientMattersResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        imageData: resultData,
      );
    } catch (e) {
      PGLog.e('交互式抠图失败: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (options.ref.key != nullptr) {
        malloc.free(options.ref.key);
      }
      if (options.ref.userCode != nullptr) {
        malloc.free(options.ref.userCode);
      }
      if (options.ref.prodCode != nullptr) {
        malloc.free(options.ref.prodCode);
      }
      malloc.free(options);

      if (imageDataPtr != null) {
        malloc.free(imageDataPtr);
      }
      if (maskDataPtr != null) {
        malloc.free(maskDataPtr);
      }
      if (foreDataPtr != null) {
        malloc.free(foreDataPtr);
      }
      if (backDataPtr != null) {
        malloc.free(backDataPtr);
      }

      malloc.free(inputImage);
      malloc.free(maskImage);
      malloc.free(foreImage);
      malloc.free(backImage);

      freeImage(resultImage);
      malloc.free(resultImage);

      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 蒙版跟踪
  static SalientMattersResult tracking(
    ImageData currentImage,
    ImageData previousImage,
    ImageData previousMask, {
    SalientMattersConfig? config,
  }) {
    if (!_initialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final options = malloc<SalientMattersOptions>();
    final currImage = malloc<SalientMattersImage>();
    final prevImage = malloc<SalientMattersImage>();
    final prevMaskImage = malloc<SalientMattersImage>();
    final resultImage = malloc<SalientMattersImage>();
    Pointer<Utf8>? errorMessagePtr;
    Pointer<Uint8>? currDataPtr;
    Pointer<Uint8>? prevDataPtr;
    Pointer<Uint8>? maskDataPtr;

    try {
      // 设置配置选项
      final cfg = config ?? SalientMattersConfig.defaultConfig;
      options.ref.netSize = cfg.netSize;
      options.ref.type = SalientMattersType.tracking.value;
      options.ref.key = cfg.key.toNativeUtf8();
      options.ref.userCode = cfg.userCode.toNativeUtf8();
      options.ref.prodCode = cfg.prodCode.toNativeUtf8();

      // 设置输出颜色
      for (int i = 0; i < 4 && i < cfg.outputColor.length; i++) {
        options.ref.outputColor[i] = cfg.outputColor[i];
      }

      // 设置当前图像
      currDataPtr = malloc<Uint8>(currentImage.data.length);
      currDataPtr
          .asTypedList(currentImage.data.length)
          .setAll(0, currentImage.data);
      currImage.ref.data = currDataPtr;
      currImage.ref.width = currentImage.width;
      currImage.ref.height = currentImage.height;
      currImage.ref.channels = currentImage.channels;
      currImage.ref.step = currentImage.step;

      // 设置前一帧图像
      prevDataPtr = malloc<Uint8>(previousImage.data.length);
      prevDataPtr
          .asTypedList(previousImage.data.length)
          .setAll(0, previousImage.data);
      prevImage.ref.data = prevDataPtr;
      prevImage.ref.width = previousImage.width;
      prevImage.ref.height = previousImage.height;
      prevImage.ref.channels = previousImage.channels;
      prevImage.ref.step = previousImage.step;

      // 设置前一帧蒙版
      maskDataPtr = malloc<Uint8>(previousMask.data.length);
      maskDataPtr
          .asTypedList(previousMask.data.length)
          .setAll(0, previousMask.data);
      prevMaskImage.ref.data = maskDataPtr;
      prevMaskImage.ref.width = previousMask.width;
      prevMaskImage.ref.height = previousMask.height;
      prevMaskImage.ref.channels = previousMask.channels;
      prevMaskImage.ref.step = previousMask.step;

      // 分配结果图像内存
      final allocResult = allocateImage(
          resultImage, currentImage.width, currentImage.height, 1);
      if (allocResult != 0) {
        return SalientMattersResult(
          errorCode: SalientMattersErrorCode.fromValue(allocResult),
          errorMessage: '分配结果图像内存失败',
        );
      }

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 执行蒙版跟踪
      final result = _salientMattersTracking(
        currImage,
        prevImage,
        prevMaskImage,
        resultImage,
        options,
        errorMessagePtr,
        512,
      );

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? resultData;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 获取结果数据
        final dataSize = resultImage.ref.width * resultImage.ref.height;
        final tempList = resultImage.ref.data.asTypedList(dataSize);
        resultData = Uint8List.fromList(tempList); // 创建数据拷贝
      }

      return SalientMattersResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        imageData: resultData,
      );
    } catch (e) {
      PGLog.e('蒙版跟踪失败: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (options.ref.key != nullptr) {
        malloc.free(options.ref.key);
      }
      if (options.ref.userCode != nullptr) {
        malloc.free(options.ref.userCode);
      }
      if (options.ref.prodCode != nullptr) {
        malloc.free(options.ref.prodCode);
      }
      malloc.free(options);

      if (currDataPtr != null) {
        malloc.free(currDataPtr);
      }
      if (prevDataPtr != null) {
        malloc.free(prevDataPtr);
      }
      if (maskDataPtr != null) {
        malloc.free(maskDataPtr);
      }

      malloc.free(currImage);
      malloc.free(prevImage);
      malloc.free(prevMaskImage);

      freeImage(resultImage);
      malloc.free(resultImage);

      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 处理前景和后景的交互蒙版数据
  static ProcessInteractiveMaskResult processInteractiveMask(
    ImageData strokeMask,
    Uint8List? previousForegroundMask,
    Uint8List? previousBackgroundMask,
    bool isPainting,
  ) {
    if (!_initialized) {
      return const ProcessInteractiveMaskResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final strokeMaskImage = malloc<SalientMattersImage>();
    Pointer<Uint8>? strokeMaskDataPtr;
    Pointer<Uint8>? prevForegroundMaskPtr;
    Pointer<Uint8>? prevBackgroundMaskPtr;
    final newForegroundMaskPtr = malloc<Pointer<Uint8>>();
    final newBackgroundMaskPtr = malloc<Pointer<Uint8>>();
    Pointer<Utf8>? errorMessagePtr;

    try {
      // 设置涂抹路径蒙版
      strokeMaskDataPtr = malloc<Uint8>(strokeMask.data.length);
      strokeMaskDataPtr
          .asTypedList(strokeMask.data.length)
          .setAll(0, strokeMask.data);
      strokeMaskImage.ref.data = strokeMaskDataPtr;
      strokeMaskImage.ref.width = strokeMask.width;
      strokeMaskImage.ref.height = strokeMask.height;
      strokeMaskImage.ref.channels = strokeMask.channels;
      strokeMaskImage.ref.step = strokeMask.step;

      // 设置上一次的前景蒙版（如果提供）
      if (previousForegroundMask != null) {
        prevForegroundMaskPtr = malloc<Uint8>(previousForegroundMask.length);
        prevForegroundMaskPtr
            .asTypedList(previousForegroundMask.length)
            .setAll(0, previousForegroundMask);
      } else {
        prevForegroundMaskPtr = nullptr;
      }

      // 设置上一次的背景蒙版（如果提供）
      if (previousBackgroundMask != null) {
        prevBackgroundMaskPtr = malloc<Uint8>(previousBackgroundMask.length);
        prevBackgroundMaskPtr
            .asTypedList(previousBackgroundMask.length)
            .setAll(0, previousBackgroundMask);
      } else {
        prevBackgroundMaskPtr = nullptr;
      }

      // 初始化输出指针
      newForegroundMaskPtr.value = nullptr;
      newBackgroundMaskPtr.value = nullptr;

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 调用底层函数
      final result = _salientMattersProcessInteractiveMasks(
        strokeMaskImage,
        prevForegroundMaskPtr,
        prevBackgroundMaskPtr,
        isPainting ? 1 : 0,
        newForegroundMaskPtr,
        newBackgroundMaskPtr,
        errorMessagePtr,
        512,
      );

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? foregroundMask;
      Uint8List? backgroundMask;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 计算单通道蒙版的数据大小
        final maskDataSize = strokeMask.width * strokeMask.height;

        // 复制前景蒙版数据
        if (newForegroundMaskPtr.value != nullptr) {
          final tempForegroundList =
              newForegroundMaskPtr.value.asTypedList(maskDataSize);
          foregroundMask = Uint8List.fromList(tempForegroundList);

          // 释放底层分配的内存
          malloc.free(newForegroundMaskPtr.value);
        }

        // 复制背景蒙版数据（如果有）
        if (newBackgroundMaskPtr.value != nullptr) {
          final tempBackgroundList =
              newBackgroundMaskPtr.value.asTypedList(maskDataSize);
          backgroundMask = Uint8List.fromList(tempBackgroundList);

          // 释放底层分配的内存
          malloc.free(newBackgroundMaskPtr.value);
        }
      }

      return ProcessInteractiveMaskResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        foregroundMask: foregroundMask,
        backgroundMask: backgroundMask,
      );
    } catch (e) {
      PGLog.e('处理交互式蒙版失败: $e');
      return ProcessInteractiveMaskResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (strokeMaskDataPtr != null) {
        malloc.free(strokeMaskDataPtr);
      }
      if (prevForegroundMaskPtr != null && prevForegroundMaskPtr != nullptr) {
        malloc.free(prevForegroundMaskPtr);
      }
      if (prevBackgroundMaskPtr != null && prevBackgroundMaskPtr != nullptr) {
        malloc.free(prevBackgroundMaskPtr);
      }
      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
      malloc.free(strokeMaskImage);
      malloc.free(newForegroundMaskPtr);
      malloc.free(newBackgroundMaskPtr);
    }
  }

  /// 区域主体抠图
  static RegionalMattingResult regionalMatting(
    ImageData image,
    RectData region, {
    ImageData? croppedImage,
    ImageData? previousFullResult,
    SalientMattersConfig? config,
    bool returnCroppedResult = false,
    bool returnCroppedMattingResult = false,
  }) {
    if (!_initialized) {
      return const RegionalMattingResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final options = malloc<SalientMattersOptions>();
    final inputImage = malloc<SalientMattersImage>();
    final croppedImagePtr = malloc<SalientMattersImage>();
    final previousFullResultPtr = malloc<SalientMattersImage>();
    final regionPtr = malloc<SalientMattersRect>();
    final resultImage = malloc<SalientMattersImage>();
    final croppedResultPtr = malloc<SalientMattersImage>();
    final croppedMattingResultPtr = malloc<SalientMattersImage>();

    Pointer<Utf8>? errorMessagePtr;
    final allocatedDataPtrs = <Pointer<Uint8>>[];

    try {
      // 设置配置选项
      final cfg = config ?? SalientMattersConfig.defaultConfig;
      options.ref.netSize = cfg.netSize;
      options.ref.type = cfg.type.value;
      options.ref.key = cfg.key.toNativeUtf8();
      options.ref.userCode = cfg.userCode.toNativeUtf8();
      options.ref.prodCode = cfg.prodCode.toNativeUtf8();

      // 设置输出颜色
      for (int i = 0; i < 4 && i < cfg.outputColor.length; i++) {
        options.ref.outputColor[i] = cfg.outputColor[i];
      }

      // 设置输入图像
      final imageDataPtr = malloc<Uint8>(image.data.length);
      allocatedDataPtrs.add(imageDataPtr);
      _setImageData(inputImage, image);
      inputImage.ref.data = imageDataPtr;
      imageDataPtr.asTypedList(image.data.length).setAll(0, image.data);

      // 设置裁剪图像（可选）
      if (croppedImage != null) {
        final croppedDataPtr = malloc<Uint8>(croppedImage.data.length);
        allocatedDataPtrs.add(croppedDataPtr);
        _setImageData(croppedImagePtr, croppedImage);
        croppedImagePtr.ref.data = croppedDataPtr;
        croppedDataPtr
            .asTypedList(croppedImage.data.length)
            .setAll(0, croppedImage.data);
      } else {
        croppedImagePtr.ref.data = nullptr;
      }

      // 设置上次的完整图像抠图结果（可选）
      if (previousFullResult != null) {
        final prevDataPtr = malloc<Uint8>(previousFullResult.data.length);
        allocatedDataPtrs.add(prevDataPtr);
        _setImageData(previousFullResultPtr, previousFullResult);
        previousFullResultPtr.ref.data = prevDataPtr;
        prevDataPtr
            .asTypedList(previousFullResult.data.length)
            .setAll(0, previousFullResult.data);
      } else {
        previousFullResultPtr.ref.data = nullptr;
      }

      // 设置区域信息
      _setRectData(regionPtr, region);

      // 分配结果图像内存
      final allocResult =
          allocateImage(resultImage, image.width, image.height, 4);
      if (allocResult != 0) {
        return RegionalMattingResult(
          errorCode: SalientMattersErrorCode.fromValue(allocResult),
          errorMessage: '分配结果图像内存失败',
        );
      }

      // 分配可选的输出图像内存
      if (returnCroppedResult) {
        allocateImage(
            croppedResultPtr, region.width, region.height, image.channels);
      } else {
        croppedResultPtr.ref.data = nullptr;
      }

      if (returnCroppedMattingResult) {
        allocateImage(croppedMattingResultPtr, region.width, region.height, 4);
      } else {
        croppedMattingResultPtr.ref.data = nullptr;
      }

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 执行区域主体抠图
      final result = _salientMattersRegionalMatting(
        inputImage,
        croppedImage != null ? croppedImagePtr : nullptr,
        previousFullResult != null ? previousFullResultPtr : nullptr,
        regionPtr,
        resultImage,
        returnCroppedResult ? croppedResultPtr : nullptr,
        returnCroppedMattingResult ? croppedMattingResultPtr : nullptr,
        options,
        errorMessagePtr,
        512,
      );

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? fullResult;
      Uint8List? croppedResult;
      Uint8List? croppedMattingResult;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 获取完整图像结果数据
        final fullDataSize = resultImage.ref.width * resultImage.ref.height * 4;
        final fullTempList = resultImage.ref.data.asTypedList(fullDataSize);
        fullResult = Uint8List.fromList(fullTempList);

        // 获取可选的裁剪结果数据
        if (returnCroppedResult && croppedResultPtr.ref.data != nullptr) {
          final croppedDataSize = croppedResultPtr.ref.width *
              croppedResultPtr.ref.height *
              croppedResultPtr.ref.channels;
          final croppedTempList =
              croppedResultPtr.ref.data.asTypedList(croppedDataSize);
          croppedResult = Uint8List.fromList(croppedTempList);
        }

        // 获取可选的裁剪抠图结果数据
        if (returnCroppedMattingResult &&
            croppedMattingResultPtr.ref.data != nullptr) {
          final croppedMattingDataSize = croppedMattingResultPtr.ref.width *
              croppedMattingResultPtr.ref.height *
              4;
          final croppedMattingTempList = croppedMattingResultPtr.ref.data
              .asTypedList(croppedMattingDataSize);
          croppedMattingResult = Uint8List.fromList(croppedMattingTempList);
        }
      }

      return RegionalMattingResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        fullResult: fullResult,
        croppedResult: croppedResult,
        croppedMattingResult: croppedMattingResult,
      );
    } catch (e) {
      PGLog.e('区域主体抠图失败: $e');
      return RegionalMattingResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (options.ref.key != nullptr) {
        malloc.free(options.ref.key);
      }
      if (options.ref.userCode != nullptr) {
        malloc.free(options.ref.userCode);
      }
      if (options.ref.prodCode != nullptr) {
        malloc.free(options.ref.prodCode);
      }
      malloc.free(options);

      // 清理数据指针
      for (final ptr in allocatedDataPtrs) {
        malloc.free(ptr);
      }

      malloc.free(inputImage);
      malloc.free(croppedImagePtr);
      malloc.free(previousFullResultPtr);
      malloc.free(regionPtr);

      freeImage(resultImage);
      malloc.free(resultImage);

      if (returnCroppedResult) {
        freeImage(croppedResultPtr);
      }
      malloc.free(croppedResultPtr);

      if (returnCroppedMattingResult) {
        freeImage(croppedMattingResultPtr);
      }
      malloc.free(croppedMattingResultPtr);

      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 区域交互式抠图
  static RegionalInteractiveResult regionalInteractive(
    ImageData croppedImage,
    RectData region, {
    ImageData? latestMask,
    ImageData? foreStrokes,
    ImageData? backStrokes,
    ImageData? latestFullMask,
    SalientMattersConfig? config,
    bool returnRegionalResult = false,
  }) {
    if (!_initialized) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '库未初始化',
      );
    }

    final options = malloc<SalientMattersOptions>();
    final croppedImagePtr = malloc<SalientMattersImage>();
    final latestMaskPtr = malloc<SalientMattersImage>();
    final foreStrokesPtr = malloc<SalientMattersImage>();
    final backStrokesPtr = malloc<SalientMattersImage>();
    final latestFullMaskPtr = malloc<SalientMattersImage>();
    final regionPtr = malloc<SalientMattersRect>();
    final resultImage = malloc<SalientMattersImage>();
    final regionalResultPtr = malloc<SalientMattersImage>();

    Pointer<Utf8>? errorMessagePtr;
    final allocatedDataPtrs = <Pointer<Uint8>>[];

    try {
      // 设置配置选项
      final cfg = config ?? SalientMattersConfig.defaultConfig;
      options.ref.netSize = cfg.netSize;
      options.ref.type = SalientMattersType.interactive.value;
      options.ref.key = cfg.key.toNativeUtf8();
      options.ref.userCode = cfg.userCode.toNativeUtf8();
      options.ref.prodCode = cfg.prodCode.toNativeUtf8();

      // 设置输出颜色
      for (int i = 0; i < 4 && i < cfg.outputColor.length; i++) {
        options.ref.outputColor[i] = cfg.outputColor[i];
      }

      // 设置裁剪区域图像
      final croppedDataPtr = malloc<Uint8>(croppedImage.data.length);
      allocatedDataPtrs.add(croppedDataPtr);
      _setImageData(croppedImagePtr, croppedImage);
      croppedImagePtr.ref.data = croppedDataPtr;
      croppedDataPtr
          .asTypedList(croppedImage.data.length)
          .setAll(0, croppedImage.data);

      // 设置上次的区域抠图结果（可选）
      if (latestMask != null) {
        final maskDataPtr = malloc<Uint8>(latestMask.data.length);
        allocatedDataPtrs.add(maskDataPtr);
        _setImageData(latestMaskPtr, latestMask);
        latestMaskPtr.ref.data = maskDataPtr;
        maskDataPtr
            .asTypedList(latestMask.data.length)
            .setAll(0, latestMask.data);
      } else {
        latestMaskPtr.ref.data = nullptr;
      }

      // 设置前景笔触（可选）
      if (foreStrokes != null) {
        final foreDataPtr = malloc<Uint8>(foreStrokes.data.length);
        allocatedDataPtrs.add(foreDataPtr);
        _setImageData(foreStrokesPtr, foreStrokes);
        foreStrokesPtr.ref.data = foreDataPtr;
        foreDataPtr
            .asTypedList(foreStrokes.data.length)
            .setAll(0, foreStrokes.data);
      } else {
        foreStrokesPtr.ref.data = nullptr;
      }

      // 设置背景笔触（可选）
      if (backStrokes != null) {
        final backDataPtr = malloc<Uint8>(backStrokes.data.length);
        allocatedDataPtrs.add(backDataPtr);
        _setImageData(backStrokesPtr, backStrokes);
        backStrokesPtr.ref.data = backDataPtr;
        backDataPtr
            .asTypedList(backStrokes.data.length)
            .setAll(0, backStrokes.data);
      } else {
        backStrokesPtr.ref.data = nullptr;
      }

      // 设置上次的完整图像抠图结果（可选）
      if (latestFullMask != null) {
        final fullMaskDataPtr = malloc<Uint8>(latestFullMask.data.length);
        allocatedDataPtrs.add(fullMaskDataPtr);
        _setImageData(latestFullMaskPtr, latestFullMask);
        latestFullMaskPtr.ref.data = fullMaskDataPtr;
        fullMaskDataPtr
            .asTypedList(latestFullMask.data.length)
            .setAll(0, latestFullMask.data);
      } else {
        latestFullMaskPtr.ref.data = nullptr;
      }

      // 设置区域信息
      _setRectData(regionPtr, region);

      // 计算完整图像尺寸（从区域推断）
      final fullWidth = region.x + region.width;
      final fullHeight = region.y + region.height;

      // 分配结果图像内存（完整图像尺寸）
      final allocResult = allocateImage(resultImage, fullWidth, fullHeight, 4);
      if (allocResult != 0) {
        return RegionalInteractiveResult(
          errorCode: SalientMattersErrorCode.fromValue(allocResult),
          errorMessage: '分配结果图像内存失败',
        );
      }

      // 分配区域结果图像内存（可选）
      if (returnRegionalResult) {
        allocateImage(regionalResultPtr, region.width, region.height, 4);
      } else {
        regionalResultPtr.ref.data = nullptr;
      }

      // 准备错误信息缓冲区
      errorMessagePtr = malloc<Uint8>(512).cast<Utf8>();

      // 执行区域交互式抠图
      final result = _salientMattersRegionalInteractive(
        croppedImagePtr,
        latestMask != null ? latestMaskPtr : nullptr,
        foreStrokes != null ? foreStrokesPtr : nullptr,
        backStrokes != null ? backStrokesPtr : nullptr,
        latestFullMask != null ? latestFullMaskPtr : nullptr,
        regionPtr,
        resultImage,
        returnRegionalResult ? regionalResultPtr : nullptr,
        options,
        errorMessagePtr,
        512,
      );

      final errorCode = SalientMattersErrorCode.fromValue(result);
      String? errorMessage;
      Uint8List? fullResult;
      Uint8List? regionalResult;

      if (errorCode != SalientMattersErrorCode.success) {
        errorMessage = errorMessagePtr.toDartString();
      } else {
        // 获取完整图像结果数据
        final fullDataSize = resultImage.ref.width * resultImage.ref.height * 4;
        final fullTempList = resultImage.ref.data.asTypedList(fullDataSize);
        fullResult = Uint8List.fromList(fullTempList);

        // 获取区域结果数据（可选）
        if (returnRegionalResult && regionalResultPtr.ref.data != nullptr) {
          final regionalDataSize =
              regionalResultPtr.ref.width * regionalResultPtr.ref.height * 4;
          final regionalTempList =
              regionalResultPtr.ref.data.asTypedList(regionalDataSize);
          regionalResult = Uint8List.fromList(regionalTempList);
        }
      }

      return RegionalInteractiveResult(
        errorCode: errorCode,
        errorMessage: errorMessage,
        fullResult: fullResult,
        regionalResult: regionalResult,
      );
    } catch (e) {
      PGLog.e('区域交互式抠图失败: $e');
      return RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    } finally {
      // 清理内存
      if (options.ref.key != nullptr) {
        malloc.free(options.ref.key);
      }
      if (options.ref.userCode != nullptr) {
        malloc.free(options.ref.userCode);
      }
      if (options.ref.prodCode != nullptr) {
        malloc.free(options.ref.prodCode);
      }
      malloc.free(options);

      // 清理数据指针
      for (final ptr in allocatedDataPtrs) {
        malloc.free(ptr);
      }

      malloc.free(croppedImagePtr);
      malloc.free(latestMaskPtr);
      malloc.free(foreStrokesPtr);
      malloc.free(backStrokesPtr);
      malloc.free(latestFullMaskPtr);
      malloc.free(regionPtr);

      freeImage(resultImage);
      malloc.free(resultImage);

      if (returnRegionalResult) {
        freeImage(regionalResultPtr);
      }
      malloc.free(regionalResultPtr);

      if (errorMessagePtr != null) {
        malloc.free(errorMessagePtr);
      }
    }
  }

  /// 释放资源
  static void dispose() {
    if (_initialized) {
      try {
        _salientMattersFree();
        _initialized = false;
      } catch (e) {
        PGLog.e('释放SalientMatters资源失败: $e');
      }
    }
  }

  /// 检查是否已初始化
  static bool get isInitialized {
    if (!_loadLibrary()) {
      return false;
    }

    try {
      return _salientMattersIsInitialized() == 1;
    } catch (e) {
      PGLog.e('检查初始化状态失败: $e');
      return false;
    }
  }

  /// 获取默认配置
  static SalientMattersConfig getDefaultConfig() {
    if (!_loadLibrary()) {
      return SalientMattersConfig.defaultConfig;
    }

    final options = malloc<SalientMattersOptions>();
    try {
      _salientMattersGetDefaultOptions(options);

      // 获取输出颜色
      final outputColor = <int>[];
      for (int i = 0; i < 4; i++) {
        outputColor.add(options.ref.outputColor[i]);
      }

      return SalientMattersConfig(
        netSize: options.ref.netSize,
        type: SalientMattersType.values.firstWhere(
          (e) => e.value == options.ref.type,
          orElse: () => SalientMattersType.matting,
        ),
        outputColor: outputColor,
      );
    } catch (e) {
      PGLog.e('获取默认配置失败: $e');
      return SalientMattersConfig.defaultConfig;
    } finally {
      malloc.free(options);
    }
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo() {
    return UniversalPlatformLoader.getLibraryInfo(
      'PGSalientMatters',
      subDirectory: 'salient_matters',
    );
  }
}
