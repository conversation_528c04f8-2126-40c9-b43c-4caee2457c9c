import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/collect/customaction/popup_action.dart'
    as collect;
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class NewUserBenefitDialog extends StatefulWidget {
  const NewUserBenefitDialog({super.key, required this.count, this.onClose});

  final int count;
  final VoidCallback? onClose;

  static void show(BuildContext context, int count, {VoidCallback? onClose}) {
    PGDialog.showCustomDialog(
      width: 700,
      height: 500,
      tag: DialogTags.newUserBenefit,
      child: NewUserBenefitDialog(count: count, onClose: onClose),
    );
  }

  @override
  State<NewUserBenefitDialog> createState() => _NewUserBenefitDialogState();
}

class _NewUserBenefitDialogState extends State<NewUserBenefitDialog> {
  int _countdown = 5; // 倒计时秒数
  Timer? _timer;
  bool _isHovered = false;
  bool _isClaimed = false;

  // 用于埋点记录用户id
  String? _userId;

  @override
  void initState() {
    super.initState();
    // 启动倒计时
    _startCountdown();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_userId == null) {
      _userId = context.read<CurrentUserRepository>().user?.effectiveId;
      _recordEvent(collect.Action.show);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();

    // 调用关闭回调
    if (widget.onClose != null) {
      widget.onClose!();
    }

    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 1) {
          _countdown--;
        } else {
          _timer?.cancel();
          // 倒计时结束后自动领取
          _claimBenefit();
        }
      });
    });
  }

  void _claimBenefit() {
    setState(() {
      _isClaimed = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 56,
            child: Align(
              alignment: Alignment.topRight,
              child: IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Color(0xFFAAAAAA),
                  size: 24,
                ),
                onPressed: () {
                  dismiss();
                },
              ),
            ),
          ),
          SizedBox(
            height: 16,
            child: Text(
              '— 图灵精修 新人礼包 —',
              style: TextStyle(
                color: Colors.white.withAlpha(100),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                height: 1,
              ),
            ),
          ),
          const SizedBox(height: 7),
          SizedBox(
              height: 32,
              child: Text(
                _isClaimed ? '恭喜你 领取成功' : '送你${widget.count}张精修免费导出次数',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  height: 1,
                ),
              )),
          const SizedBox(height: 44),
          // 礼物图像
          _buildGiftCard(count: widget.count, isClaimed: _isClaimed),
          const SizedBox(height: 24),
          SizedBox(
            width: 128,
            height: 44,
            child: Center(
              child: GestureDetector(
                onTap: _isClaimed
                    ? () => dismiss
                    : () {
                        _recordEvent(collect.Action.click);
                        _claimBenefit();
                      },
                child: PlatformMouseRegion(
                  cursor: _isClaimed
                      ? SystemMouseCursors.basic
                      : SystemMouseCursors.click,
                  onEnter: (_) => setState(() => _isHovered = true),
                  onExit: (_) => setState(() => _isHovered = false),
                  child: Container(
                    width: 128,
                    height: 44,
                    decoration: BoxDecoration(
                      color: _isClaimed
                          ? const Color(0xFFFFFFFF).withOpacity(0.05)
                          : _isHovered
                              ? const Color(0xFFF73069)
                              : const Color(0xFFF72561),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        _isClaimed ? '知道了' : '立即领取',
                        style: TextStyle(
                          fontSize: 12,
                          color: _isClaimed
                              ? const Color(0xFFEBEDF5).withOpacity(0.4)
                              : Colors.white,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.semiBold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          // 倒计时
          SizedBox(
            height: 10,
            child: Text(
              _isClaimed ? '精修免费导出次数*${widget.count}' : '${_countdown}s后自动领取',
              style: TextStyle(
                color: const Color(0xFFFF3B6B),
                fontSize: 10,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                height: 1,
              ),
            ),
          ),
          const SizedBox(height: 23),
          SizedBox(
            height: 20,
            child: Text(
              '温馨提示：编辑预览照片的次数不受任何限制，你可以随心体验。',
              style: TextStyle(
                color: const Color(0xFFFFFFFF).withOpacity(0.3),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                height: 1,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // 中间图片和张数区域
  Widget _buildGiftCard({required int count, bool isClaimed = false}) {
    if (isClaimed) {
      return SizedBox(
        width: 180,
        height: 180,
        child: Image.asset("assets/icons/new_user_card_success.png"),
      );
    }

    return SizedBox(
      width: 180,
      height: 180,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/icons/new_user_card_bottom.png"),
          Positioned(
            top: 30,
            left: 60,
            right: 60,
            child: Container(
              width: 52,
              height: 42,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    count.toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.semiBold,
                      height: 1,
                    ),
                  ),
                  const SizedBox(width: 1),
                  Transform.translate(
                    offset: const Offset(0, -2),
                    child: Text(
                      '张',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.semiBold,
                        height: 1,
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void dismiss() {
    _recordEvent(collect.Action.close);
    PGDialog.dismiss(tag: DialogTags.newUserBenefit);
  }

  void _recordEvent(collect.Action action) {
    collect.recordPopupAction(
        subElementId: collect.SubElementId.welcome_gift,
        userId: _userId ?? '',
        action: action);
  }
}
