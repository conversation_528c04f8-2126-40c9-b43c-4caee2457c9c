import 'dart:convert';
import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/utils/pg_log.dart';

part 'external_message.freezed.dart';
part 'external_message.g.dart';

/// 外部消息类型枚举
enum ExternalMessageType {
  @JsonValue('import_project')
  importProject,
  @JsonValue('smart')
  smart,
  @JsonValue('open_project')
  openProject,
  @JsonValue('export_project')
  exportProject,
  @JsonValue('unknown')
  unknown,
}

/// 外部消息基类
@freezed
class ExternalMessage with _$ExternalMessage {
  const factory ExternalMessage({
    required ExternalMessageType type,
    required Map<String, dynamic> data,
    String? requestId,
    @Default(0) int timestamp,
  }) = _ExternalMessage;

  const ExternalMessage._();

  factory ExternalMessage.fromJson(Map<String, dynamic> json) =>
      _$ExternalMessageFromJson(json);

  /// 从字符串解析外部消息
  static ExternalMessage? fromString(String message) {
    try {
      final Map<String, dynamic> json =
          message.startsWith('{') && message.endsWith('}')
              ? Map<String, dynamic>.from(jsonDecode(message))
              : {
                  'type': 'unknown',
                  'data': {'raw': message}
                };

      PGLog.d('解析外部消息JSON: $json');

      final result = ExternalMessage.fromJson(json);
      PGLog.d('外部消息解析成功，类型: ${result.type}');
      return result;
    } catch (e, stackTrace) {
      // 如果解析失败，返回unknown类型的消息
      PGLog.e('外部消息解析失败: $e');
      PGLog.e('原始消息: $message');
      PGLog.e('堆栈跟踪: $stackTrace');

      return ExternalMessage(
        type: ExternalMessageType.unknown,
        data: {'raw': message, 'error': e.toString()},
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );
    }
  }
}

/// 文件项模型
@freezed
class FileItem with _$FileItem {
  const factory FileItem({
    required String originalPath,
    String? historyId,
    @Default(false) bool isSelected,
  }) = _FileItem;

  const FileItem._();

  factory FileItem.fromJson(Map<String, dynamic> json) =>
      _$FileItemFromJson(json);

  /// 从文件路径创建 FileItem
  static FileItem fromPath(String path) {
    return FileItem(
      originalPath: path,
      historyId: null,
      isSelected: false,
    );
  }

  /// 从字符串解析FileItem (统一解析方法)
  /// 支持格式：
  /// - "path" (0个分割符)
  /// - "path|historyId" (1个分割符)
  /// - "path|historyId|isSelected" (2个分割符)
  static FileItem? fromString(String value) {
    try {
      if (value.contains('|')) {
        final parts = value.split('|');
        if (parts.isNotEmpty) {
          final path = parts[0];
          final historyId =
              parts.length > 1 && parts[1].isNotEmpty ? parts[1] : null;
          final isSelected =
              parts.length > 2 ? parts[2].toLowerCase() == 'true' : false;

          return FileItem(
            originalPath: path,
            historyId: historyId,
            isSelected: isSelected,
          );
        }
      } else {
        // 简单文件路径
        return FileItem.fromPath(value);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 从通用数据Map创建
  static FileItem? fromMap(Map<String, dynamic> data) {
    try {
      return FileItem.fromJson(data);
    } catch (e) {
      return null;
    }
  }
}

/// 导入项目消息数据
@freezed
class ImportProjectData with _$ImportProjectData {
  const factory ImportProjectData({
    String? projectId,
    String? projectName,
    // 文件列表字段
    required List<FileItem> selectedFileList, // 选中的文件列表
    required List<FileItem> allFileList, // 所有文件列表（包括未选中的）
    @Default(false) bool autoNavigate,
    @Default(false) bool newProject, // 是否创建新项目，忽略现有projectId
  }) = _ImportProjectData;

  const ImportProjectData._();

  factory ImportProjectData.fromJson(Map<String, dynamic> json) =>
      _$ImportProjectDataFromJson(json);

  /// 从通用数据Map创建
  static ImportProjectData? fromMap(Map<String, dynamic> data) {
    try {
      // 支持旧的 fileList 字段名和新的 selectedFileList 字段名
      final selectedFileList = _parseFileList(data['selectedFileList']) ??
          _parseFileList(data['fileList']);
      final allFileList = _parseFileList(data['allFileList']);

      if (selectedFileList == null || allFileList == null) {
        // 如果必需字段解析失败，尝试使用标准的fromJson方法
        return ImportProjectData.fromJson(data);
      }

      return ImportProjectData(
        projectId: data['projectId'] as String?,
        projectName: data['projectName'] as String?,
        selectedFileList: selectedFileList,
        allFileList: allFileList,
        autoNavigate: data['autoNavigate'] as bool? ?? false,
        newProject: data['newProject'] as bool? ?? false,
      );
    } catch (e) {
      return null;
    }
  }

  /// 统一解析fileList的方法
  /// 支持多种数据类型：
  /// - List<FileItem>：直接使用
  /// - List<String>：使用FileItem.fromString解析
  /// - List<Map>：使用FileItem.fromJson解析
  static List<FileItem>? _parseFileList(dynamic fileListData) {
    if (fileListData is! List || fileListData.isEmpty) {
      return null;
    }

    final List<FileItem> fileItems = [];

    for (final item in fileListData) {
      FileItem? fileItem;

      if (item is FileItem) {
        // 已经是FileItem对象，直接使用
        fileItem = item;
      } else if (item is String) {
        // 字符串格式，使用统一的fromString方法
        fileItem = FileItem.fromString(item);
      } else if (item is Map<String, dynamic>) {
        // Map格式，使用fromJson方法
        fileItem = FileItem.fromMap(item);
      }

      if (fileItem != null) {
        fileItems.add(fileItem);
      }
    }

    return fileItems.isNotEmpty ? fileItems : null;
  }

  /// 获取所有文件路径
  List<String> getAllFilePaths() {
    return allFileList.map((item) => item.originalPath).toList();
  }

  List<File> getAllFiles() {
    return allFileList.map((item) => File(item.originalPath)).toList();
  }

  /// 获取所有文件项
  List<FileItem> getAllFileItems() {
    return allFileList;
  }

  /// 获取选中的文件路径
  List<String> getSelectedFilePaths() {
    return selectedFileList.map((item) => item.originalPath).toList();
  }

  /// 获取选中的文件项
  List<FileItem> getSelectedFileItems() {
    return selectedFileList;
  }

  /// 从文件路径创建 ImportProjectData
  static ImportProjectData fromFilePaths({
    String? projectId,
    String? projectName,
    required List<String> selectedFilePaths,
    required List<String> allFilePaths,
    bool autoNavigate = false,
    bool newProject = false,
  }) {
    return ImportProjectData(
      projectId: projectId,
      projectName: projectName,
      selectedFileList:
          selectedFilePaths.map((path) => FileItem.fromPath(path)).toList(),
      allFileList: allFilePaths.map((path) => FileItem.fromPath(path)).toList(),
      autoNavigate: autoNavigate,
      newProject: newProject,
    );
  }

  /// 从文件列表创建 ImportProjectData
  static ImportProjectData fromFileList({
    String? projectId,
    String? projectName,
    required List<FileItem> selectedFileList,
    required List<FileItem> allFileList,
    bool autoNavigate = false,
    bool newProject = false,
  }) {
    return ImportProjectData(
      projectId: projectId,
      projectName: projectName,
      selectedFileList: selectedFileList,
      allFileList: allFileList,
      autoNavigate: autoNavigate,
      newProject: newProject,
    );
  }
}

/// 智能导入消息数据
@freezed
class SmartData with _$SmartData {
  const factory SmartData({
    required List<String> imagePaths,
    String? projectName,
    @Default(false) bool autoNavigate,
    @Default(false) bool newProject,
  }) = _SmartData;

  const SmartData._();

  factory SmartData.fromJson(Map<String, dynamic> json) =>
      _$SmartDataFromJson(json);

  /// 从通用数据Map创建
  static SmartData? fromMap(Map<String, dynamic> data) {
    try {
      final imagePaths = data['imagePaths'] as List<dynamic>?;
      if (imagePaths == null) {
        return null;
      }

      return SmartData(
        imagePaths: imagePaths.map((path) => path.toString()).toList(),
        projectName: data['projectName'] as String?,
        autoNavigate: data['autoNavigate'] as bool? ?? false,
        newProject: data['newProject'] as bool? ?? false,
      );
    } catch (e) {
      return null;
    }
  }

  /// 从字符串路径列表创建
  static SmartData fromStringPaths({
    required List<String> imagePaths,
    String? projectName,
    bool autoNavigate = false,
    bool newProject = false,
  }) {
    return SmartData(
      imagePaths: imagePaths,
      projectName: projectName,
      autoNavigate: autoNavigate,
      newProject: newProject,
    );
  }
}

/// 打开项目消息数据
@freezed
class OpenProjectData with _$OpenProjectData {
  const factory OpenProjectData({
    required String projectId,
  }) = _OpenProjectData;

  const OpenProjectData._();

  factory OpenProjectData.fromJson(Map<String, dynamic> json) =>
      _$OpenProjectDataFromJson(json);

  /// 从通用数据Map创建
  static OpenProjectData? fromMap(Map<String, dynamic> data) {
    try {
      return OpenProjectData.fromJson(data);
    } catch (e) {
      return null;
    }
  }
}
