import 'dart:async';

import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';

import '../domain/models/project_info/project_info.dart';
import '../domain/models/workspace/workspace.dart';
import 'update_event.dart';

abstract class ProjectRepository {
  /// 异步获得所有的工程
  Future<List<ProjectInfo>> getAllProjects();

  /// 异步获得该用户的所有的工程
  Future<List<ProjectInfo>> getUserAllProjects(String userId);

  /// 异步获得该用户的所有的工程
  Future<List<ProjectInfo>> getUserAllProjectsByType(
    String userId,
    ProjectType projectType,
  );

  /// 获得所有被标记删除的工程
  Future<List<ProjectInfo>> getDeletedProjects();

  /// 异步获得某目标工程
  Future<ProjectInfo?> getProjectById(String projectId);

  /// 异步添加一个项目
  Future<void> addProject(ProjectInfo info);

  /// 异步更新一个项目
  Future<void> updateProject(ProjectInfo info);

  /// 异步更新一个项目的工作区
  Future<void> updateWorkspace(Workspace workspace);

  /// 异步更新一个项目的工作区，忽略文件相关内容变更
  Future<void> updateWorkspaceWithoutFiles(Workspace workspace);

  /// 异步删除一个项目
  Future<void> deleteProject(String projectId);

  /// 异步删除一组项目
  Future<void> deleteProjects(List<String> projectIds);

  /// 异步标记一个项目为已删除
  Future<void> markProjectAsDeleted(String projectId);

  /// 异步标记一组项目为已删除
  Future<void> markProjectsAsDeleted(List<String> projectIds);

  /// 异步获得某目标工程的工作区
  Future<Workspace?> getWorkspaceByProjectId(String projectId);

  /// 异步获得某目标工程数组的工作区s
  Future<List<Workspace>> getWorkspacesByProjectIds(List<String> projectIds);

  /// 数据更新通知流
  /// 当项目发生增删改操作时，通过该流推送变更事件
  Stream<UpdateEvent<String>> get dataUpdates;

  /// 同步工作区
  Future<ProjectInfo?> syncProject(String projectId);

  /// 工程改名
  Future<void> renameProject(String projectId, String newName);

  /// 获取项目文件列表
  Future<List<WorkspaceFile>> getProjectFiles(String projectId);

  /// 登出时，清理所有缓存假设存在缓存
  void clearCache();
}
