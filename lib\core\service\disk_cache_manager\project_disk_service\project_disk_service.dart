import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_result/cache_cleanup_result.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_server/cache_service.dart';
import 'package:turing_art/datalayer/service/database/dao/sort_option.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/project_db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/workspace_db_operater.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

class _ProjectCacheFile {
  final String filePath;
  final int cacheSize;
  final int lastEditTime;

  _ProjectCacheFile(this.filePath, this.cacheSize, this.lastEditTime);
}

/// 删除结果
class _DeleteResult {
  final int deletedSize;
  final int deletedCount;

  _DeleteResult(this.deletedSize, this.deletedCount);
}

/// 项目磁盘缓存服务
/// 负责管理项目相关的磁盘缓存，包括项目文件、临时文件、导出文件等
/// 基于 FileManager 实现，所有操作均在后台线程执行以避免UI卡顿
class ProjectDiskService extends CacheService {
  final rootDirPath = FileManager().rootDir.path;
  final DbOperater _dbOperater;
  final ProjectCacheRuleProvider _projectCacheRuleProvider;
  List<ProjectCacheRule> get _ruleList => _projectCacheRuleProvider.cacheRule;

  ProjectDiskService({
    required DbOperater dbOperater,
    required ProjectCacheRuleProvider projectCacheRuleProvider,
  })  : _dbOperater = dbOperater,
        _projectCacheRuleProvider = projectCacheRuleProvider;

  @override
  String get serviceName => 'projectDisk';

  @override
  CacheServiceLevel get level => CacheServiceLevel.medium;

  @override
  Future<ServiceCleanupResult> clearCache({int? targetSize}) async {
    try {
      PGLog.d('开始清理项目磁盘缓存, 目标大小: ${targetSize ?? '全部'}');
      final startTime = DateTime.now();
      int totalDeletedSize = 0;
      int totalDeletedCount = 0;

      // 获取所有缓存文件
      final cacheFiles = await _fetchAllCacheFiles();
      if (cacheFiles.isEmpty) {
        PGLog.d('没有找到缓存文件，无需清理');
        return ServiceCleanupResult(
          serviceName: serviceName,
          deletedSize: 0,
          timestamp: DateTime.now(),
        );
      }

      // 如果targetSize为空，删除所有缓存
      if (targetSize == null) {
        PGLog.d('目标大小未指定，删除所有缓存文件: ${cacheFiles.length}个');
        final results = await _deleteCacheFilesInParallel(cacheFiles);
        totalDeletedSize =
            results.fold<int>(0, (sum, r) => sum + r.deletedSize);
        totalDeletedCount =
            results.fold<int>(0, (sum, r) => sum + r.deletedCount);
      }
      // 如果指定了目标大小，则按时间顺序删除直到达到目标
      else if (targetSize > 0) {
        PGLog.d('按目标大小删除缓存文件: $targetSize 字节');
        int accumulatedSize = 0;
        final filesToDelete = <_ProjectCacheFile>[];

        // 按时间顺序累加文件，直到达到目标大小
        for (final file in cacheFiles) {
          if (accumulatedSize >= targetSize) break;
          filesToDelete.add(file);
          accumulatedSize += file.cacheSize;
        }

        if (filesToDelete.isEmpty) {
          PGLog.d('没有需要删除的缓存文件');
          return ServiceCleanupResult(
            serviceName: serviceName,
            deletedSize: 0,
            timestamp: DateTime.now(),
          );
        }

        PGLog.d('计划删除${filesToDelete.length}个缓存文件以达到目标大小 $targetSize 字节');
        final results = await _deleteCacheFilesInParallel(filesToDelete,
            targetSize: targetSize);
        totalDeletedSize =
            results.fold<int>(0, (sum, r) => sum + r.deletedSize);
        totalDeletedCount =
            results.fold<int>(0, (sum, r) => sum + r.deletedCount);
      }
      // 如果targetSize <= 0，则不删除任何文件
      else {
        PGLog.w('目标大小无效: $targetSize，不执行删除操作');
        return ServiceCleanupResult(
          serviceName: serviceName,
          deletedSize: 0,
          timestamp: DateTime.now(),
        );
      }

      final duration = DateTime.now().difference(startTime).inMilliseconds;
      PGLog.d(
          '项目磁盘缓存清理完成，耗时: ${duration}ms, 删除了$totalDeletedCount个文件, 共$totalDeletedSize字节');
      return ServiceCleanupResult(
        serviceName: serviceName,
        deletedSize: totalDeletedSize,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      PGLog.e('清理项目磁盘缓存时发生错误: $e');
      return ServiceCleanupResult(
        serviceName: serviceName,
        deletedSize: 0,
        timestamp: DateTime.now(),
      );
    }
  }

  /// 并行删除缓存文件
  /// [cacheFiles] 要删除的缓存文件列表
  /// [targetSize] 目标删除大小，如果指定则在达到目标大小时停止删除
  /// 返回删除结果列表
  Future<List<_DeleteResult>> _deleteCacheFilesInParallel(
      List<_ProjectCacheFile> cacheFiles,
      {int? targetSize}) async {
    // 将文件列表分片以实现并行处理
    const chunkSize = 5; // 每次并行处理的文件数量
    final results = <_DeleteResult>[];
    int accumulatedDeletedSize = 0;

    for (var i = 0; i < cacheFiles.length; i += chunkSize) {
      // 如果指定了目标大小且已达到目标，则停止删除
      if (targetSize != null && accumulatedDeletedSize >= targetSize) {
        PGLog.d('已达到目标删除大小 $accumulatedDeletedSize/$targetSize 字节，停止删除');
        break;
      }

      // 获取当前分片
      final endIndex =
          i + chunkSize < cacheFiles.length ? i + chunkSize : cacheFiles.length;
      final chunk = cacheFiles.sublist(i, endIndex);

      // 并行处理当前分片中的每个文件
      final chunkResults = await Future.wait(chunk.map((file) async {
        try {
          final deletedCount =
              await _deleteFilesWithRule(_ruleList, file.filePath);
          return _DeleteResult(file.cacheSize, deletedCount);
        } catch (e) {
          PGLog.e('删除缓存文件失败 ${file.filePath}: $e');
          return _DeleteResult(0, 0);
        }
      }));

      // 累加结果
      results.addAll(chunkResults);
      accumulatedDeletedSize =
          results.fold<int>(0, (sum, r) => sum + r.deletedSize);
    }

    return results;
  }

  @override
  Future<int> getCacheSize() async {
    try {
      PGLog.d('开始计算项目磁盘缓存大小...');

      // 获取所有缓存文件信息
      final files = await _fetchAllCacheFiles();
      if (files.isEmpty) {
        PGLog.d('没有找到缓存文件');
        return 0;
      }

      PGLog.d('找到 ${files.length} 个文件需要计算缓存大小');

      int totalCacheSize = 0;
      int processedCount = 0;
      const progressInterval = 50; // 每处理50个文件输出一次进度

      // 分批处理文件，避免长时间阻塞
      const batchSize = 20; // 每批处理20个文件

      for (var i = 0; i < files.length; i += batchSize) {
        final endIndex =
            i + batchSize < files.length ? i + batchSize : files.length;
        final batch = files.sublist(i, endIndex);

        // 处理当前批次
        for (final file in batch) {
          try {
            // 根据规则list，计算每个文件夹内的缓存资源文件大小
            final fileCacheSize =
                await _getFilesTempFileSizeWithRule(_ruleList, file.filePath);
            totalCacheSize += fileCacheSize;
            processedCount++;

            // 输出进度
            if (processedCount % progressInterval == 0) {
              final progress =
                  (processedCount / files.length * 100).toStringAsFixed(1);
              PGLog.d('缓存大小计算进度: $progress% ($processedCount/${files.length})');
            }
          } catch (e) {
            PGLog.e('计算文件缓存大小失败: ${file.filePath}, 错误: $e');
            // 继续处理下一个文件，不中断整个过程
          }
        }

        // 每处理完一批后，给其他任务执行的机会
        if (i + batchSize < files.length) {
          await Future.delayed(const Duration(milliseconds: 1));
        }
      }
      return totalCacheSize;
    } catch (e) {
      PGLog.e('计算项目磁盘缓存大小时发生严重错误: $e');
      return 0;
    }
  }

  /// 根据规则计算当前文件资源下缓存文件的大小
  /// [ruleList] 缓存规则列表
  /// [filePath] 文件路径
  /// 返回字节数
  Future<int> _getFilesTempFileSizeWithRule(
    List<ProjectCacheRule> ruleList,
    String filePath,
  ) async {
    // 安全检查1：路径验证
    if (filePath.isEmpty) {
      PGLog.e('文件路径不能为空');
      return 0;
    }

    // 安全检查2：路径存在性检查
    final directory = Directory(filePath);
    if (!directory.existsSync()) {
      PGLog.d('文件路径不存在: $filePath');
      return 0;
    }

    try {
      int totalSize = 0;

      // 遍历每个缓存规则，直接在主线程中处理，避免 isolate 开销
      for (final rule in ruleList) {
        // 构建子文件夹路径
        final subDirPath = path.join(filePath, rule.module);
        final subDir = Directory(subDirPath);

        // 检查子文件夹是否存在
        if (!subDir.existsSync()) {
          continue;
        }

        try {
          // 获取子文件夹下的所有文件（非递归，只处理直接子文件）
          final allFiles = subDir.listSync(recursive: false);

          // 遍历子文件夹中的文件，只匹配 list 中指定的文件名
          for (final entity in allFiles) {
            if (entity is File) {
              try {
                // 安全检查：文件存在性检查
                if (!entity.existsSync()) {
                  continue;
                }

                // 安全检查：文件路径合法性检查
                if (entity.path.length < 3) {
                  continue;
                }

                final fileName = path.basename(entity.path);

                // 与规则中的每个字段值进行模糊匹配（保持原有逻辑）
                bool shouldCount = false;
                for (final fieldValue in rule.list) {
                  if (fileName
                      .toLowerCase()
                      .contains(fieldValue.toLowerCase())) {
                    shouldCount = true;
                    break;
                  }
                }

                // 如果匹配到任何规则，则计算文件大小
                if (shouldCount) {
                  try {
                    final fileSize = entity.lengthSync();
                    totalSize += fileSize;
                  } catch (e) {
                    // 文件大小获取失败，跳过
                    continue;
                  }
                }
              } catch (e) {
                // 文件处理失败，跳过
                continue;
              }
            }
          }
        } catch (e) {
          // 子文件夹访问失败，跳过
          continue;
        }
      }

      return totalSize;
    } catch (e) {
      PGLog.e('计算缓存文件大小时发生严重错误: $e');
      return 0;
    }
  }

  /// 根据规则删除缓存文件
  /// [ruleList] 缓存规则列表
  /// [filePath] 文件路径
  /// 返回删除的文件数量
  Future<int> _deleteFilesWithRule(
    List<ProjectCacheRule> ruleList,
    String filePath,
  ) async {
    // 安全检查1：路径验证
    if (filePath.isEmpty) {
      PGLog.e('文件路径不能为空');
      return 0;
    }

    // 安全检查2：路径存在性检查
    final directory = Directory(filePath);
    if (!directory.existsSync()) {
      PGLog.e('文件路径不存在: $filePath');
      return 0;
    }

    try {
      int deletedCount = 0;
      final deletedFiles = <String>[];
      final failedFiles = <String>[];

      // 遍历每个缓存规则
      for (final rule in ruleList) {
        // 构建子文件夹路径
        final subDirPath = path.join(filePath, rule.module);
        final subDir = Directory(subDirPath);

        // 检查子文件夹是否存在
        if (!subDir.existsSync()) {
          continue;
        }

        try {
          // 获取子文件夹下的所有文件
          final allFiles = await subDir.list(recursive: false).toList();

          // 遍历子文件夹中的文件，只匹配 list 中指定的文件名
          for (final entity in allFiles) {
            if (entity is File) {
              try {
                // 安全检查3：文件存在性检查
                if (!entity.existsSync()) {
                  continue;
                }

                // 安全检查4：文件路径合法性检查
                if (entity.path.length < 3) {
                  continue;
                }

                final fileName = path.basename(entity.path);

                // 与规则中的每个字段值进行模糊匹配（保持原有逻辑）
                bool shouldDelete = false;
                String matchedField = "";
                for (final fieldValue in rule.list) {
                  if (fileName
                      .toLowerCase()
                      .contains(fieldValue.toLowerCase())) {
                    shouldDelete = true;
                    matchedField = fieldValue;
                    break;
                  }
                }

                // 如果匹配到任何规则，则删除文件
                if (shouldDelete) {
                  // 安全检查5：删除前再次确认文件存在
                  if (entity.existsSync()) {
                    try {
                      await entity.delete();
                      deletedCount++;
                      deletedFiles.add(entity.path);
                      PGLog.d(
                          '已删除缓存文件: $fileName (匹配规则: ${rule.module}/$matchedField)');
                    } catch (e) {
                      PGLog.e('删除文件失败: ${entity.path}, 错误: $e');
                      failedFiles.add(entity.path);
                    }
                  }
                }
              } catch (e) {
                PGLog.e('处理文件时发生错误: ${entity.path}, 错误: $e');
                failedFiles.add(entity.path);
              }
            }
          }
        } catch (e) {
          PGLog.e('访问子文件夹失败: $subDirPath, 错误: $e');
          continue;
        }
      }

      if (deletedFiles.isNotEmpty) {
        for (final deletedFile in deletedFiles) {
          final fileName = path.basename(deletedFile);
          final module = path.basename(path.dirname(deletedFile));
          PGLog.d('  - $deletedFile (匹配规则: $module/$fileName)');
        }
      }

      if (failedFiles.isNotEmpty) {
        PGLog.w('删除失败的文件:');
        for (final failedFile in failedFiles) {
          PGLog.w('  - $failedFile');
        }
      }

      return deletedCount;
    } catch (e) {
      PGLog.e('模糊匹配删除过程中发生严重错误: $e');
      return 0;
    }
  }

  Future<List<_ProjectCacheFile>> _fetchAllCacheFiles() async {
    try {
      PGLog.d('开始获取所有缓存文件信息...');
      final startTime = DateTime.now();

      // 1.获取所有的工程，最老的在最前，最新的在最后
      final workspaces = await _dbOperater.loadAllWorkspaces(
        sortField: SortField.lastEditTime.value,
        sortOrder: SortOrder.ascending.value,
      );

      if (workspaces.isEmpty) {
        PGLog.d('没有找到工作空间');
        return [];
      }

      PGLog.d('找到 ${workspaces.length} 个工作空间');
      List<_ProjectCacheFile> allFileTemp = [];

      // 分批处理工作空间，避免长时间阻塞
      const workspaceBatchSize = 5; // 每批处理5个工作空间

      for (var i = 0; i < workspaces.length; i += workspaceBatchSize) {
        final endIndex = i + workspaceBatchSize < workspaces.length
            ? i + workspaceBatchSize
            : workspaces.length;
        final workspaceBatch = workspaces.sublist(i, endIndex);

        // 并行处理当前批次的工作空间
        final batchResults =
            await Future.wait(workspaceBatch.map((workspace) async {
          try {
            // 找到该工程中所有的资源
            final files = await _dbOperater.getFilesByWorkspaceId(
              workspace.projectId,
              sortField: SortField.lastEditTime.value,
              sortOrder: SortOrder.ascending.value,
            );

            if (files.isEmpty) {
              return <_ProjectCacheFile>[];
            }

            // 找到该工程对应的用户，基于该用户id获取用户文件夹
            final user =
                await _dbOperater.getProjectAuthorById(workspace.projectId);
            if (user == null) {
              return <_ProjectCacheFile>[];
            }

            final workspaceDir = FileManager().getProjectDirWithUser(
              user,
              workspace.projectId,
            );

            if (workspaceDir == null) {
              return <_ProjectCacheFile>[];
            }

            // 计算该工作空间内所有文件的缓存大小
            final workspaceTemp = await _calculateWorkspaceFilesCacheSize(
              files,
              workspaceDir.path,
              workspace.lastEditTime,
            );

            return workspaceTemp;
          } catch (e) {
            PGLog.e('处理工作空间失败: ${workspace.projectId}, 错误: $e');
            return <_ProjectCacheFile>[];
          }
        }));

        // 合并批次结果
        for (final batchResult in batchResults) {
          allFileTemp.addAll(batchResult);
        }

        // 输出进度
        final progress = ((i + workspaceBatchSize) / workspaces.length * 100)
            .toStringAsFixed(1);
        PGLog.d(
            '工作空间处理进度: $progress% (${(i + workspaceBatchSize).clamp(0, workspaces.length)}/${workspaces.length})');

        // 每处理完一批后，给其他任务执行的机会
        if (i + workspaceBatchSize < workspaces.length) {
          await Future.delayed(const Duration(milliseconds: 1));
        }
      }

      final duration = DateTime.now().difference(startTime).inMilliseconds;
      PGLog.d('缓存文件信息获取完成，共 ${allFileTemp.length} 个文件，耗时: ${duration}ms');
      return allFileTemp;
    } catch (e) {
      PGLog.e('获取缓存文件信息时发生严重错误: $e');
      return [];
    }
  }

  /// 并行计算工作空间内所有文件的缓存大小
  /// [files] 工作空间内的文件列表
  /// [workspacePath] 工作空间路径
  /// [lastEditTime] 工作空间最后编辑时间
  /// 返回缓存文件信息列表
  Future<List<_ProjectCacheFile>> _calculateWorkspaceFilesCacheSize(
    List<dynamic> files,
    String workspacePath,
    int lastEditTime,
  ) async {
    if (files.isEmpty) {
      return [];
    }

    // 将文件列表分片以实现并行处理，避免同时启动过多任务
    const chunkSize = 5; // 减少分片大小，避免过多并发
    final results = <_ProjectCacheFile>[];

    for (var i = 0; i < files.length; i += chunkSize) {
      // 获取当前分片
      final endIndex =
          i + chunkSize < files.length ? i + chunkSize : files.length;
      final chunk = files.sublist(i, endIndex);

      // 并行处理当前分片中的每个文件
      final chunkResults = await Future.wait(chunk.map((file) async {
        try {
          final filePath = '$workspacePath/${file.fileId}';
          // 根据规则list，计算每个文件夹内的缓存资源文件大小
          final fileCacheSize =
              await _getFilesTempFileSizeWithRule(_ruleList, filePath);
          return _ProjectCacheFile(
            filePath,
            fileCacheSize,
            lastEditTime,
          );
        } catch (e) {
          PGLog.e('计算文件缓存大小失败: ${file.fileId}, 错误: $e');
          // 返回一个默认的缓存文件对象，避免中断整个流程
          return _ProjectCacheFile(
            '$workspacePath/${file.fileId}',
            0,
            lastEditTime,
          );
        }
      }));

      results.addAll(chunkResults);

      // 每处理完一个分片后，给其他任务执行的机会
      if (i + chunkSize < files.length) {
        await Future.delayed(const Duration(milliseconds: 1));
      }
    }

    return results;
  }
}
