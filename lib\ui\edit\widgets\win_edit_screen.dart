import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/collect/clickaction/about_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/export_list_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/info_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/logout_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/renew_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/settings_btn.dart';
import 'package:pg_turing_collect_event/collect/clickaction/topbar_info_reward.dart';
import 'package:pg_turing_collect_event/collect/clickaction/topbar_my.dart';
import 'package:pg_turing_collect_event/collect/clickaction/topbar_purchase.dart';
import 'package:pg_turing_collect_event/collect/pay_action_log.dart';
import 'package:pg_turing_collect_event/model.dart' show ProjectAction;
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/domain/events/order_event.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/ui/h5_webview.dart';
import 'package:turing_art/ui/edit/services/edit_action_service.dart';
import 'package:turing_art/ui/edit/view_model/edit_view_model.dart';
import 'package:turing_art/ui/edit/widgets/edit_pc_profile_dialog.dart';
import 'package:turing_art/ui/export_history/widgets/export_history_dialog.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/login/widgets/privacy/privacy_const.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/project_home_title_bar_option.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_dialog.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_qr_code_dialog.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_success_dialog.dart';
import 'package:turing_art/ui/setting/widgets/general_setting_dialog.dart';
import 'package:turing_art/ui/ui_status/edit_process_files_ui_status.dart';
import 'package:turing_art/ui/unity/constant/turing_unity_constant.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/unity/widgets/turing_unity_widget.dart';
import 'package:turing_art/ui/version_update/widgets/version_update_dialog.dart';
import 'package:turing_art/ui/wechat_gift/widgets/wechat_gift_dialog.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/mixins/page_duration_tracking_mixin.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

class WinEditScreen extends StatefulWidget {
  final String? projectId;

  const WinEditScreen({super.key, this.projectId});

  @override
  State<WinEditScreen> createState() => _WinEditScreenState();
}

class _WinEditScreenState extends State<WinEditScreen>
    with SimplePageDurationTrackingMixin
    implements AccountChangeListener {
  ProjectStateProvider? _projectStateProvider;
  late final EditActionService _editActionService;
  bool _isActionServiceInitialized = false;

  final _titleBarOptionKey = GlobalKey();

  // 缓存ProfileDialogViewModel，避免在监听器中使用Provider
  ProfileDialogViewModel? _cachedProfileViewModel;

  // 订阅订单状态变更事件
  late StreamSubscription<OrderEvent> _orderEventSubscription;

  // 订阅异常事件
  StreamSubscription<EditProcessFilesUiStatus>? _exceptionEventSubscription;

  @override
  String get pageName => AnalyticPageNames.edit;

  @override
  void dispose() {
    _clearProjectState();
    _orderEventSubscription.cancel();
    _exceptionEventSubscription?.cancel();
    // 设置WinEditScreen为非活动状态
    GlobalVars.isWinEditScreenActive = false;
    super.dispose();
  }

  // 实现AccountChangeListener接口
  @override
  void onAccountChange(ProfileDialogViewModel viewModel) {
    if (viewModel.getNeedShowWechatGiftDialog()) {
      // 检查弹窗状态
      _checkIsFirstNeedPopDialogStatus(viewModel);
      // 检查福利添加状态
      _checkAddWechatStatus(viewModel);
    } else {
      PGLog.d('WinEditScreen - onAccountChange 不需要展示微信礼包弹窗');
    }
  }

  @override
  void initState() {
    super.initState();
    // 设置WinEditScreen为活动状态
    GlobalVars.isWinEditScreenActive = true;
    // 订阅订单状态变更事件
    _orderEventSubscription = context
        .read<PurchaseStateProvider>()
        .orderStatusEvents
        .listen(_handleOrderStatusChangeEvent);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _projectStateProvider = context.read<ProjectStateProvider>();
    if (!_isActionServiceInitialized) {
      _isActionServiceInitialized = true;
      _editActionService = EditActionService(
        context.read<ProjectRepository>(),
        context.read<CurrentUserRepository>(),
        _projectStateProvider!,
        GoRouterNavigatorService(context),
      );

      _reportProjectAction(ProjectAction.open);
    }
  }

  // 处理异常事件
  void _handleExceptionEvent(EditProcessFilesUiStatus status) {
    if (!mounted) {
      return;
    }

    if (status is ErrorStatus) {
      switch (status.errorType) {
        case EditProcessFilesErrorType.diskSpace:
          PGDialog.showToastOnUnity(status.message);
          break;
        default:
          break;
      }
    }
  }

  // 处理订单状态变更事件(只是用于UI展示toast)
  void _handleOrderStatusChangeEvent(OrderEvent event) async {
    if (!mounted) {
      return;
    }
    PGLog.d('WinEditScreen - _handleOrderStatusChangeEvent ${event.status}');

    // 处理订单完成事件
    if (event.status == OrderStatus.completed) {
      // 显示购买成功弹窗
      await PGDialog.dismiss(tag: DialogTags.purchaseQRCode); // 确保关闭支付二维码弹窗
      // 第一次购买成功已经弹过加微信，就在此处弹窗
      final isFirstBuySuccess =
          _cachedProfileViewModel?.isFirstBuySuccess ?? false;
      PGLog.d(
          'WinEditScreen - _handleOrderStatusChangeEvent isFirstBuySuccess $isFirstBuySuccess');
      if (!isFirstBuySuccess) {
        // 如果购买成功弹窗没有显示，则显示（第一次购买成功有流程，不能强制关闭已有的）
        if (!PGDialog.isDialogVisible(DialogTags.purchaseSuccess)) {
          PurchaseSuccessDialog.showOnUnity(null);
        }
      }
    }
    // 处理订单取消、退款事件
    else if (event.status == OrderStatus.canceled ||
        event.status == OrderStatus.refunded) {
      // 显示提示
      PGDialog.showToastOnUnity(event.message);
    }
  }

  void _clearProjectState() {
    _editActionService.clearProjectState();
  }

  @override
  Widget build(BuildContext context) {
    String id = widget.projectId ?? '';
    if (id.isEmpty) {
      return const Center(child: Text('项目ID不能为空'));
    }
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => ProfileDialogViewModel(
            context.read<CurrentUserRepository>(),
            context.read<AccountRepository>(),
            context.read<WechatGiftRepository>(),
            context.read<OpsCustomTableRepository>(),
            context.read<AccountRightsStateProvider>(),
            context.read<RewardRepository>(),
            context.read<NewUserRepository>(),
            context.read<AuthUseCaseProvider>(),
          ),
        ),
        ChangeNotifierProvider(
          create: (context) {
            final viewModel = EditViewModel(
              id,
              context.read<ProjectRepository>(),
              context.read<UnityController>(),
              context.read<UnityUseCaseProvider>(),
              context.read<ExportUseCaseProvider>(),
              context.read<ExportProjectProvider>(),
              context.read<ExportTaskStateProvider>(),
              context.read<NoviceGuideManager>(),
              _editActionService,
            );
            _exceptionEventSubscription?.cancel();
            // 订阅异常事件
            _exceptionEventSubscription =
                viewModel.exceptionEvents.listen(_handleExceptionEvent);
            return viewModel;
          },
        ),
      ],
      child: _buildBody2(),
    );
  }

  Widget _buildBody2() {
    return Consumer<EditViewModel>(
      builder: (context, viewModel, child) {
        return FutureBuilder<ProjectInfo?>(
          future: viewModel.getProjectInfo(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('加载错误: ${snapshot.error}'));
            }

            final project = snapshot.data;
            if (project == null) {
              return const Center(child: Text('项目不存在'));
            }

            return Stack(
              children: [
                Platform.isMacOS
                    ? Container() // mac端编辑页面的标题栏为原生实现，不需要flutter的ui
                    : TitleBarWidget(
                        funcWidget: Consumer<ProfileDialogViewModel>(
                          builder: (context, profileViewModel, child) {
                            if (_cachedProfileViewModel != profileViewModel) {
                              // 缓存新的profileViewModel并设置监听器
                              _cachedProfileViewModel = profileViewModel;
                              profileViewModel.setAccountChangeListener(this);

                              // 是否需要展示微信礼包弹窗
                              _checkIsFirstNeedPopDialogStatus(
                                  profileViewModel);
                              // 检查福利添加状态
                              _checkAddWechatStatus(profileViewModel);
                            }

                            // 只有这个组件会因为ProfileDialogViewModel的变化而重建是否显示完善信息按钮
                            // 初始化未完成，准备数据未完成，默认不展示按钮
                            final showReward = profileViewModel.isFinishInit &&
                                profileViewModel.getNeedShowWechatGiftDialog();

                            return ProjectHomeTitleBarOption(
                              key: _titleBarOptionKey,
                              showProgress: false,
                              showLogo: true,
                              showProfile: true,
                              showReward: showReward,
                              // 根据ProfileDialogViewModel的状态动态决定是否显示
                              showPurchase: !profileViewModel.isSubAccount,
                              // 子账号不显示购买按钮
                              profileClick: () {
                                _showProfileDialog(context, _titleBarOptionKey);
                                recordTopbarMy(
                                    userId: profileViewModel.effectiveId ?? '',
                                    clickTime:
                                        DateTimeUtil.getCurrentTimestampMs()
                                            .toString());
                              },
                              rewardCardClick: () {
                                _showWechatGiftDialog(profileViewModel);
                                recordTopbarInfoReward(
                                    userId: profileViewModel.effectiveId ?? '',
                                    clickTime:
                                        DateTimeUtil.getCurrentTimestampMs()
                                            .toString());
                              },
                              purchaseCardClick: () {
                                // 然后显示购买对话框
                                PurchaseDialog.showOnUnity(context,
                                    (payChannelList, orderId, collectInfo) {
                                  // 订单创建成功后，显示支付二维码，传递可支付渠道马上创建UI
                                  PurchaseQRCodeDialog.showOnUnity(
                                      payChannelList, orderId, collectInfo);
                                }, SourceType.edit_top_button);
                                recordTopbarPurchase(
                                    userId: profileViewModel.effectiveId ?? '',
                                    clickTime:
                                        DateTimeUtil.getCurrentTimestampMs()
                                            .toString());
                              },
                            );
                          },
                        ),
                        child: Scaffold(
                          backgroundColor: Colors.black,
                          body: Container(),
                        ),
                      ),
                TuringUnityWidget(
                  onActionCallback: (action, message) {
                    switch (action) {
                      case TuringActions.home:
                        throw Exception('fuck');
                        _reportProjectAction(ProjectAction.save);
                        _clearProjectState();
                        Navigator.of(context).pop();
                        break;
                      case TuringActions.export:
                      // TODO: Handle this case.
                    }
                  },
                  onUnityMessageCallback: (message) {
                    viewModel.onUnityMessage(message);
                  },
                  onDebugMessageCallback: (message) {
                    viewModel.onDebugMessageReceived(message);
                    PGLog.d('WinEditScreen - onDebugMessageCallback $message');
                  },
                  onEngineErrorCallback: (error) {
                    PGLog.e('WinEditScreen - Unity 引擎错误: $error');
                    // 模拟 Unity 引擎错误的处理
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 上报Project操作埋点
  Future<void> _reportProjectAction(ProjectAction action) async {
    final projectId = widget.projectId;
    if (projectId == null) {
      return;
    }
    await _editActionService.reportProjectAction(projectId, action);
  }
}

// 显示编辑个人中心弹窗
void _showProfileDialog(BuildContext context, GlobalKey titleBarKey) async {
  final profileViewModel = context.read<ProfileDialogViewModel>();

  // await获取isAigcUser状态
  PGDialog.showLoading();
  final aigcEntranceManager = context.read<AigcEntranceManager>();
  final isAigcUser = await aigcEntranceManager.isAigcUser();
  // 马上需要show dialog，所以需要await先dismiss
  await PGDialog.dismiss();

  // 获取 ProjectHomeTitleBarOption 实例
  final titleBar = titleBarKey.currentWidget as ProjectHomeTitleBarOption?;
  if (titleBar == null) {
    return;
  }

  // 获取 头像 的 RenderBox
  final RenderBox? renderBox = titleBar.profileButtonKey.currentContext
      ?.findRenderObject() as RenderBox?;
  if (renderBox == null) {
    return;
  }

  // 获取头像在屏幕上的位置
  final buttonPosition = renderBox.localToGlobal(Offset.zero);
  final buttonSize = renderBox.size;

  // 计算对话框位置
  final dialogLeft = buttonPosition.dx + buttonSize.width / 2 - 88;
  final dialogTop = buttonPosition.dy + buttonSize.height + 8;

  // 如果对话框已经存在，则不显示(防止快速多次点击)
  if (PGDialog.isDialogVisible(DialogTags.winEditProfile)) {
    PGLog.d(
        'WinEditScreen showProfileDialog, but dialog already exist, return');
    return;
  }
  final optionsCount = profileViewModel.role == UserRole.employee
      ? 3 // 子账号3个选项
      : 5; // 主账号5个选项

  // 根据isAigcUser状态计算对话框高度
  final dialogHeight = isAigcUser
      ? optionsCount * 36.0 + 170.0 + 16.0
      : optionsCount * 36.0 + 126.0 + 16.0; // 非AIGC用户减去44px积分卡片高度

  PGDialog.showCustomDialogOnUnity(
    tag: DialogTags.winEditProfile,
    width: 176,
    height: dialogHeight,
    dialogPosition: Offset(dialogLeft, dialogTop),
    centerInWindow: false,
    roundRadius: 12,
    // 使用 ChangeNotifierProvider.value 包装对话框，这样对话框内部就可以监听 EditViewModel 的变化
    child: ChangeNotifierProvider<ProfileDialogViewModel>.value(
      value: profileViewModel,
      child: ProjectEditPcProfileDialog(
        onCheckUpdate: () => _tryUpdateDownload(context),
        onLogout: () async {
          await profileViewModel.logout();
          if (!context.mounted) {
            return;
          }
          GoRouterNavigatorService(context).navigateToLogin();
          recordLogoutBtn(
              userId: profileViewModel.effectiveId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onSettings: () {
          GeneralSettingDialog.showOnUnity();
          recordSettingsBtn(
              userId: profileViewModel.userId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onPurchase: (defaultTab) {
          // 根据传入的类型显示相应的购买弹窗
          PurchaseDialog.showOnUnity(context,
              (payChannelList, orderId, collectInfo) {
            // 订单创建成功后，显示支付二维码，传递可支付渠道马上创建UI
            PurchaseQRCodeDialog.showOnUnity(
                payChannelList, orderId, collectInfo);
          }, SourceType.self_edit_page, defaultTab: defaultTab);
          recordRenewBtn(
              userId: profileViewModel.effectiveId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onExport: () {
          ExportHistoryDialog.showOnUnity();
          recordExportListBtn(
              userId: profileViewModel.effectiveId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onAboutUs: () {
          if (Platform.isWindows) {
            UrlLauncherUtil.openInSystemBrowser(PrivacyConst.aboutUrl);
          } else {
            // 如果对话框已经存在，则不显示(防止快速多次点击)
            if (PGDialog.isDialogVisible(DialogTags.aboutUs)) {
              PGLog.d(
                  'WinEditScreen showAboutUs, but dialog already exist, return');
              return;
            }
            PGDialog.showCustomDialog(
              width: PrivacyConst.privacyPolicyWidth,
              height: PrivacyConst.privacyPolicyHeight,
              tag: DialogTags.aboutUs,
              needBlur: true,
              child: H5WebView(
                url: PrivacyConst.aboutUrl,
                title: '关于我们',
                onClose: () => PGDialog.dismiss(tag: DialogTags.aboutUs),
                forDialog: true,
              ),
            );
          }
          recordAboutBtn(
              userId: profileViewModel.effectiveId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        onCompleteAccountInfo: () async {
          _showWechatGiftDialog(profileViewModel);

          recordInfoBtn(
              userId: profileViewModel.effectiveId ?? '',
              clickTime: DateTimeUtil.getCurrentTimestampMs().toString());
        },
        isAigcUser: isAigcUser,
      ),
    ),
  );
}

/// 检查弹窗状态并显示相应弹窗
void _checkIsFirstNeedPopDialogStatus(ProfileDialogViewModel profileViewModel) {
  // 如果已经显示过微信礼包弹窗，则不再显示
  if (UserPreferencesService.getHasShownWechatGiftDialog()) {
    return;
  }
  PGLog.d('WinEditScreen - _checkIsFirstNeedPopDialogStatus 没有显示过微信礼包弹窗');
  // 第一次导出成功，则调起加微信弹窗
  if (profileViewModel.isFirstExportSuccess) {
    _showWechatGiftDialog(profileViewModel);
    // 记录已经显示过微信礼包弹窗
    UserPreferencesService.setHasShownWechatGiftDialog(hasShown: true);
    return;
  }

  // 如果第一次购买成功，则显示购买成功弹窗,再显示微信礼包弹窗
  if (profileViewModel.isFirstBuySuccess) {
    PurchaseSuccessDialog.showOnUnity(() async {
      // 购买成功弹窗关闭后，再显示微信礼包弹窗
      _showWechatGiftDialog(profileViewModel);
      // 记录已经显示过微信礼包弹窗
      UserPreferencesService.setHasShownWechatGiftDialog(hasShown: true);
    });
  }
}

void _showWechatGiftDialog(ProfileDialogViewModel profileViewModel) async {
  // 调起加微信弹窗
  // PGDialog.showLoading();
  final wechatGiftList = await profileViewModel.getWechatGiftBenefits();
  // PGDialog.dismiss();
  if (wechatGiftList != null && wechatGiftList.isNotEmpty) {
    WechatGiftDialog.showOnUnity(wechatGiftList);
    // 弹起微信弹窗后，开始轮询账户权益状态
    profileViewModel.checkAndStartRightsPollingIfNeeded();
  } else {
    PGDialog.showToastOnUnity('获取微信礼包权益失败,请稍后重试');
  }
}

// account发生变化，检查福利添加状态
void _checkAddWechatStatus(ProfileDialogViewModel profileViewModel) {
  if (profileViewModel.isWechatAddFirstTime()) {
    // 检测到已经添加微信，显示 toast并关闭弹窗
    PGDialog.showToast('成功添加微信');
    PGDialog.dismiss(tag: DialogTags.wechatGift);
  }
}

// 检查更新
Future<void> _tryUpdateDownload(BuildContext context) async {
  // 需要自定义UI
  _checkUpdateDownload(context);
}

/// 检查更新
void _checkUpdateDownload(BuildContext context) async {
  await UpdateManager().checkUpdateVersion(isLaunch: false).then((result) {
    if (result.message != null && result.message != '') {
      PGDialog.showToast(result.message!);
    }
    if (result.isUpdateVersion != null && result.isUpdateVersion == true) {
      VersionUpdateDialog.show(context);
    }
  });
}
