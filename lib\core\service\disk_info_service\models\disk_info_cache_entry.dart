// 需要导入的类型
import 'package:turing_art/ffi/ffi.dart' show DiskType;

/// 磁盘信息缓存条目
class DiskInfoCacheEntry {
  final String driveRoot; // 磁盘根路径，如 "C:\" 或 "/"
  final DiskType type;
  final DateTime cachedAt;

  const DiskInfoCacheEntry({
    required this.driveRoot,
    required this.type,
    required this.cachedAt,
  });

  /// 检查缓存是否过期
  bool isExpired({Duration maxAge = const Duration(hours: 24)}) {
    return DateTime.now().difference(cachedAt) > maxAge;
  }

  /// 创建缓存条目的副本
  DiskInfoCacheEntry copyWith({
    String? driveRoot,
    DiskType? type,
    DateTime? cachedAt,
  }) {
    return DiskInfoCacheEntry(
      driveRoot: driveRoot ?? this.driveRoot,
      type: type ?? this.type,
      cachedAt: cachedAt ?? this.cachedAt,
    );
  }

  @override
  String toString() =>
      'DiskInfoCacheEntry(driveRoot: $driveRoot, type: $type, cachedAt: $cachedAt)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DiskInfoCacheEntry &&
        other.driveRoot == driveRoot &&
        other.type == type &&
        other.cachedAt == cachedAt;
  }

  @override
  int get hashCode => Object.hash(driveRoot, type, cachedAt);
}
