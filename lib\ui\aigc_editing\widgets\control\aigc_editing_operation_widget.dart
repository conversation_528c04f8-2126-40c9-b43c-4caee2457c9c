import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/services/aigc_editing_service_coordinator.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_operation_view_model.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_scene_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_sample_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/model/aigc_batch_sample_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/widgets/aigc_batch_sample_dialog.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_canvas_mask_operation_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_theme_list_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart' as core_fonts;
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC编辑操作组件
///
/// 遵循工厂模式（Factory Pattern）和状态模式（State Pattern）
class AigcEditingOperationWidget extends StatefulWidget {
  /// 编辑操作ViewModel
  final AigcEditingOperationViewModel viewModel;

  /// 批量打样回调函数
  final Future<List<(bool, String?)>> Function({
    required List<AigcBatchSampleModel> sampleModels,
    Function(int current, int total, {required bool success, String? error})?
        onProgress,
  })? onBatchSample;

  const AigcEditingOperationWidget({
    super.key,
    required this.viewModel,
    this.onBatchSample,
  });

  @override
  State<AigcEditingOperationWidget> createState() =>
      _AigcEditingOperationWidgetState();
}

class _AigcEditingOperationWidgetState extends State<AigcEditingOperationWidget>
    with SingleTickerProviderStateMixin {
  // 加载动画控制器
  late AnimationController _loadingController;

  // Widget的GlobalKey，用于Toast定位
  final GlobalKey _widgetKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();
  }

  @override
  void dispose() {
    _loadingController.dispose();
    // 取消正在进行的操作，避免在页面销毁后显示提示
    widget.viewModel.cancelCurrentOperation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.viewModel,
      child: Consumer<AigcEditingOperationViewModel>(
        builder: (context, viewModel, child) {
          return PlatformMouseRegion(
            // 当鼠标离开整个操作区域时，确保清除所有悬停状态
            onExit: (_) {
              // 触发创意离开回调
            },
            child: Container(
              key: _widgetKey,
              decoration: const BoxDecoration(
                color: Color(0xFF1F1F1F),
              ),
              child: Row(
                children: [
                  Container(
                    width: 1,
                    height: double.infinity,
                    color: Colors.white.withOpacity(0.1),
                  ),
                  Expanded(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // 计算可用高度
                        final totalHeight = constraints.maxHeight;

                        // 固定组件高度：
                        // - 顶部间距: 4px
                        // - 标题栏: 40px
                        // - 底部间距: 1px
                        // - 分隔线: 1px
                        // - AigcCanvasMaskOperationWidget: 大约250px
                        // - 底部按钮区域: 77px
                        // - 底部预留空间: 80px
                        const fixedHeight = 4 + 40 + 1 + 1 + 250 + 77 + 80;

                        // 计算AigcThemeListWidget的可用高度
                        final availableHeight = totalHeight - fixedHeight;

                        // 限制在150-398之间
                        final themeListHeight =
                            availableHeight.clamp(150.0, 398.0);

                        return Stack(
                          children: [
                            // 主内容区域
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                const SizedBox(height: 4),
                                _buildHeader(),
                                Container(
                                  padding: const EdgeInsets.only(bottom: 1),
                                  height: themeListHeight,
                                  child: AigcThemeListWidget(
                                    backgroundColor: const Color(0xFF1F1F1F),
                                    width: double.infinity,
                                    height: themeListHeight,
                                    title: "主题预设",
                                    titleColor: Colors.white,
                                    showAddButton: true,
                                    viewModel: viewModel.themeListViewModel,
                                    emptyIconAsset:
                                        'assets/icons/aigc_theme_empty.png',
                                  ),
                                ),
                                const SizedBox(height: 1),
                                Column(
                                  children: [
                                    // 添加顶部分隔线
                                    Container(
                                      height: 1,
                                      width: double.infinity,
                                      color: const Color(0xFFFFFFFF)
                                          .withOpacity(0.1),
                                    ),
                                    const AigcCanvasMaskOperationWidget(),
                                  ],
                                ),
                                // 为底部按钮留出空间
                                const SizedBox(height: 80),
                              ],
                            ),

                            // 底部固定按钮区域
                            Positioned(
                              left: 0,
                              right: 0,
                              bottom: 0,
                              child: Container(
                                height: 77,
                                decoration: const BoxDecoration(
                                  color: Color(0xFF1F1F1F),
                                ),
                                child: Center(
                                  child: _buildOperationButton(viewModel),
                                ),
                              ),
                            ),

                            // 半透明遮罩层 - 根据控制区域启用状态显示/隐藏
                            ChangeNotifierProvider.value(
                              value: context.read<AigcEditingControlProvider>(),
                              child: Consumer<AigcEditingControlProvider>(
                                builder: (context, controlProvider, child) {
                                  return Visibility(
                                    visible:
                                        !controlProvider.isEnabledControlArea,
                                    child: Positioned.fill(
                                      child: Container(
                                        color: const Color(0xFF000000)
                                            .withOpacity(0.5),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建标题区域
  Widget _buildHeader() {
    return Container(
      height: 40,
      color: const Color(0xFF1F1F1F),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Image.asset(
            'assets/icons/aigc_ai.png',
            width: 24,
            height: 24,
          ),
          Text(
            "AI场景增强",
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              height: 18 / 14,
              fontFamily: core_fonts.Fonts.defaultFontFamily,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildOperationButton(AigcEditingOperationViewModel viewModel) {
    return Consumer3<AigcEditingThemeListProvider, AigcEditingImageProvider,
        AigcEditingServiceCoordinator>(
      builder: (context, themeListProvider, imageProvider, serviceCoordinator,
          child) {
        final selectedImage = imageProvider.selectedImage;
        if (selectedImage == null) {
          return _buildDisabledButton(viewModel);
        }

        // 检查多选模式下的批量抠图状态
        final multiSelectProvider =
            context.read<AigcEditingMultiSelectProvider>();
        if (multiSelectProvider.isMultiSelectMode) {
          if (multiSelectProvider.selectedImages.isNotEmpty) {
            if (serviceCoordinator.isBatchMaskProcessing) {
              return _buildBatchMaskProcessingButton(serviceCoordinator);
            } else {
              final imageState =
                  themeListProvider.getImageState(selectedImage.fileId);
              return _buildButtonFromState(imageState, viewModel);
            }
          } else {
            return _buildDisabledButton(viewModel);
          }
        }

        final imageState =
            themeListProvider.getImageState(selectedImage.fileId);
        return _buildButtonFromState(imageState, viewModel);
      },
    );
  }

  Widget _buildButtonFromState(
      ProcessState state, AigcEditingOperationViewModel viewModel) {
    switch (state) {
      case ProcessState.initial:
        return _buildStartButton(viewModel);
      case ProcessState.processed:
        return _buildRedoButton(viewModel);
      case ProcessState.loading:
        return _buildLoadingButton();
      case ProcessState.disabled:
        return _buildDisabledButton(viewModel);
    }
  }

  /// 构建批量抠图处理中按钮
  Widget _buildBatchMaskProcessingButton(dynamic coordinator) {
    final pendingCount = coordinator.pendingMaskTaskCount ?? 0;

    return Container(
      width: 253,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFF404040),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF666666),
          width: 1,
        ),
      ),
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.white.withOpacity(0.6),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '主体区域获取中 ($pendingCount)',
              style: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 14,
                fontWeight: FontWeight.w500,
                height: 18 / 14,
                fontFamily: core_fonts.Fonts.defaultFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建开始打样按钮
  Widget _buildStartButton(AigcEditingOperationViewModel viewModel) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => viewModel.isButtonHovered = true,
      onExit: (_) => viewModel.isButtonHovered = false,
      child: Container(
        width: 253,
        height: 40,
        decoration: BoxDecoration(
          // 描边渐变
          gradient: viewModel.isButtonHovered
              ? const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0x4DFFFFFF), // FFFFFF 30%
                    Color(0x00FFFFFF), // FFFFFF 0%
                  ],
                )
              : const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0x1AFFFFFF), // FFFFFF 10%
                    Color(0x00FFFFFF), // FFFFFF 0%
                  ],
                ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          margin: const EdgeInsets.all(1), // 描边宽度
          decoration: BoxDecoration(
            // 填充渐变
            gradient: viewModel.isButtonHovered
                ? const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFF94B7D), // 悬停时：F94B7D
                      Color(0xFFF72561), // 悬停时：F72561
                    ],
                  )
                : const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF742E42), // 常规时：742E42
                      Color(0xFFF72561), // 常规时：F72561
                    ],
                  ),
            borderRadius: BorderRadius.circular(7),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFF72561).withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // 按钮内容
              Center(
                child: _createSampleButton(viewModel),
              ),

              // 可点击区域
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: GestureDetector(
                    onTap: () async {
                      _handleCreateButtonClicked(viewModel);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建再次打样按钮
  Widget _buildRedoButton(AigcEditingOperationViewModel viewModel) {
    return Container(
      width: 253,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFFF9EB9).withOpacity(0.4),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFF72561).withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 按钮内容
          Center(
            child: _createSampleButton(viewModel),
          ),
          // 可点击区域
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () async {
                  _handleCreateButtonClicked(viewModel);
                },
                borderRadius: BorderRadius.circular(8),
                splashColor: Colors.white.withOpacity(0.1),
                highlightColor: Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _createSampleButton(AigcEditingOperationViewModel viewModel,
      {bool isRedo = false}) {
    final themeColor =
        isRedo ? const Color(0xFFFF9EB9) : const Color(0xFFFFFFFF);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _getButtonText(),
          style: TextStyle(
            color: themeColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 18 / 14,
            fontFamily: core_fonts.Fonts.defaultFontFamily,
          ),
        ),
        const SizedBox(width: 5),
        Image.asset(
          'assets/icons/coin_score_style.png',
          width: 20,
          height: 20,
          color: themeColor,
        ),
        Text(
          viewModel.getCostValue().toString(),
          style: TextStyle(
            color: themeColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 18 / 14,
            fontFamily: core_fonts.Fonts.defaultFontFamily,
          ),
        ),
      ],
    );
  }

  String _getButtonText() {
    final themeListProvider = context.read<AigcEditingThemeListProvider>();
    final imageProvider = context.read<AigcEditingImageProvider>();

    // 检查 selectedImage 是否为空，避免空检查操作符异常
    final selectedImage = imageProvider.selectedImage;
    if (selectedImage == null) {
      return "开始打样";
    }

    // 根据图片的打样状态显示按钮文本
    final imageState = themeListProvider.getImageState(selectedImage.fileId);
    return imageState == ProcessState.processed ? "再次打样" : "开始打样";
  }

  void _handleCreateButtonClicked(
      AigcEditingOperationViewModel viewModel) async {
    final networkProvider = context.read<NetworkProvider>();
    if (!await networkProvider.isConnected()) {
      PGDialog.showToast('网络错误，请检查网络连接');
      return;
    }
    if (!mounted) {
      return;
    }
    final imageProvider = context.read<AigcEditingImageProvider>();
    final currentEditingProjectRepository =
        context.read<CurrentEditingProjectRepository>();
    final themeListProvider = context.read<AigcEditingThemeListProvider>();
    final batchThemeListProvider = AigcEditingThemeListProvider();
    batchThemeListProvider.setSelectedPreset(themeListProvider.selectedPreset);
    batchThemeListProvider.setSelectedEffect(themeListProvider.selectedEffect);
    final multiSelectProvider = context.read<AigcEditingMultiSelectProvider>();
    if (multiSelectProvider.isMultiSelectMode &&
        multiSelectProvider.selectedImages.isNotEmpty) {
      final sceneViewModel = context.read<AigcEditingSceneViewModel>();
      AigcBatchSampleDialog.show(
        context,
        imageProvider,
        multiSelectProvider,
        batchThemeListProvider,
        currentEditingProjectRepository,
        sceneViewModel.perSampleEffectCostValue,
        widget.onBatchSample != null
            ? ({required sampleModels, onProgress}) async {
                final results = await widget.onBatchSample!(
                  sampleModels: sampleModels,
                  onProgress: onProgress,
                );

                // 批量打样完成后显示提示
                if (mounted) {
                  final successCount =
                      results.where((result) => result.$1).length;
                  final totalCount = results.length;

                  if (successCount > 0) {
                    // 更新打样记录 - 批量设置成功的图片状态
                    final successStates = <String, ProcessState>{};
                    for (int i = 0; i < sampleModels.length; i++) {
                      if (i < results.length && results[i].$1) {
                        successStates[sampleModels[i].selectedImage.fileId] =
                            ProcessState.processed;
                      }
                    }
                    themeListProvider.setBatchImageStates(successStates);
                    // 刷新账户信息
                    viewModel.sampleSuccessRefreshAccount();
                    // 显示成功提示
                    String message;
                    if (successCount == totalCount) {
                      message = '已添加 $successCount 张到打样队列';
                    } else {
                      final failCount = totalCount - successCount;
                      message = '已添加 $successCount 张到打样队列，$failCount 张失败';
                    }

                    AigcEditSampleToast.show(
                      context,
                      message,
                      iconPath:
                          'assets/icons/aigc_editing_sample_toast_icon.png',
                      isAutoDismiss: true,
                      parentWidget: _widgetKey,
                    );
                  }
                }

                return results;
              }
            : ({required sampleModels, onProgress}) async {
                PGDialog.showToast('批量打样功能未配置');
                return [];
              },
      );
      return;
    }

    try {
      final result = await viewModel.handleOperationButtonClicked();

      // 检查组件是否仍然mounted，避免在页面销毁后显示Toast
      if (!mounted) {
        return;
      }

      if (result.$1) {
        final themeListProvider = context.read<AigcEditingThemeListProvider>();
        final imageProvider = context.read<AigcEditingImageProvider>();
        // 检查 selectedImage 是否为空，避免空检查操作符异常
        final selectedImage = imageProvider.selectedImage;
        if (selectedImage != null) {
          themeListProvider.addSampleRecord(selectedImage.fileId);
          AigcEditSampleToast.show(
            context,
            '已添加到打样队列',
            iconPath: 'assets/icons/aigc_editing_sample_toast_icon.png',
            isAutoDismiss: true,
            parentWidget: _widgetKey,
          );
        } else {
          PGDialog.showToast('选择的图片信息有误，请重新选择');
        }
      } else {
        PGDialog.showToast(result.$2 ?? '创建打样失败，请稍后重试');
      }
    } catch (e) {
      // 如果操作被取消或组件已销毁，不显示错误提示
      PGLog.d('取消创建打样: $e');
    }
  }

  /// 构建加载中按钮
  Widget _buildLoadingButton() {
    return Container(
      width: 253,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFF333333),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: RotationTransition(
          turns: _loadingController,
          child: Image.asset(
            'assets/icons/icon_loading.png',
            width: 24,
            height: 24,
          ),
        ),
      ),
    );
  }

  /// 构建禁用状态按钮
  Widget _buildDisabledButton(AigcEditingOperationViewModel viewModel) {
    return Container(
      width: 253,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFF404040),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          _getButtonText(),
          style: TextStyle(
            color: Colors.white.withOpacity(0.35),
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 18 / 14,
            fontFamily: core_fonts.Fonts.defaultFontFamily,
          ),
        ),
      ),
    );
  }
}
