import 'package:flutter/material.dart';
import 'package:turing_art/config/env_config.dart';
import 'package:turing_art/core/components/pc_hover_widget.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

import 'package:turing_art/utils/app_info.dart';

class ProjectHomePcVersionIntroView extends StatelessWidget {
  const ProjectHomePcVersionIntroView({
    super.key,
    this.onGuideClick,
    this.onShortKeyClick,
    this.onCustomerServiceClick,
    this.onCheckUpdateClick,
    this.onVersionIntroduceClick,
  });

  final VoidCallback? onGuideClick;
  final VoidCallback? onShortKeyClick;
  final VoidCallback? onCustomerServiceClick;
  final VoidCallback? onCheckUpdateClick;
  final VoidCallback? onVersionIntroduceClick;

  @override
  Widget build(BuildContext context) {
    final optionItems = [
      _OptionItem(
        icon: 'assets/icons/home_profile_guide.png',
        title: '操作指南',
        onTap: onGuideClick,
      ),
      _OptionItem(
        icon: 'assets/icons/home_profile_short_key.png',
        title: '快捷键',
        onTap: onShortKeyClick,
      ),
      _OptionItem(
        icon: 'assets/icons/home_profile_contact.png',
        title: '联系客服',
        onTap: onCustomerServiceClick,
      ),
      _OptionItem(
        icon: 'assets/icons/home_profile_update.png',
        title: '检查更新',
        onTap: onCheckUpdateClick,
      ),
      _OptionItem(
        icon: 'assets/icons/home_profile_version_intro.png',
        title: '版本介绍',
        onTap: onVersionIntroduceClick,
        showVersion: true,
      ),
    ];

    return SizedBox(
      width: 318,
      height: 30 +
          (optionItems.length * 48) +
          ((optionItems.length - 1) * 4) +
          12 +
          107,
      child: Column(
        children: [
          EnvConfig.isBetaVersion
              ? _buildExpirationCard()
              : const SizedBox(height: 107),
          const SizedBox(height: 12),
          ...List.generate(optionItems.length, (index) {
            final item = optionItems[index];
            return Column(
              children: [
                _buildOptionItem(
                  icon: item.icon,
                  title: item.title,
                  onTap: item.onTap,
                  showVersion: item.showVersion,
                ),
                if (index < optionItems.length - 1) const SizedBox(height: 4),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildExpirationCard() {
    return Container(
      width: 280,
      height: 107,
      margin: const EdgeInsets.only(left: 30),
      decoration: BoxDecoration(
        color: const Color(0xFF121315),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 16,
            top: 16,
            child: Image.asset(
              'assets/icons/home_profile_ experience.png',
              width: 24,
              height: 24,
            ),
          ),
          Positioned(
            left: 48,
            top: 16,
            child: Row(
              children: [
                Text(
                  'Beta V${AppInfo.version}',
                  style: TextStyle(
                    color: const Color(0xFFE1E2E5),
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 14,
                    height: 1.42, // 19.88/14
                  ),
                ),
                Text(
                  ' 内测体验中',
                  style: TextStyle(
                    color: const Color(0xFFE1E2E5),
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 14,
                    height: 1.4, // 19.6/14
                    letterSpacing: 0,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 48,
            top: 40,
            right: 24,
            child: RichText(
              textAlign: TextAlign.left,
              text: TextSpan(
                children: [
                  const TextSpan(
                    text: '该内测版本预计 ',
                    style: TextStyle(
                      color: Color(0x99EBEDF5),
                    ),
                  ),
                  TextSpan(
                    text: AppInfoExtension.getExpiredTimeForTipBar(),
                    style: const TextStyle(
                      color: Color(0xFFF72561),
                    ),
                  ),
                  const TextSpan(
                    text: ' 过期，届时请联系提供方或邮件咨询 ',
                    style: TextStyle(
                      color: Color(0x99EBEDF5),
                    ),
                  ),
                  const TextSpan(
                    text: '<EMAIL>',
                    style: TextStyle(
                      color: Color(0x99EBEDF5),
                    ),
                  ),
                  const TextSpan(
                    text: ' 以继续使用。',
                    style: TextStyle(
                      color: Color(0x99EBEDF5),
                    ),
                  ),
                ],
                style: const TextStyle(
                  fontSize: 12,
                  height: 1.4,
                  leadingDistribution: TextLeadingDistribution.even,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem({
    required String icon,
    required String title,
    VoidCallback? onTap,
    bool showVersion = false,
  }) {
    return PcHoverWidget(
      builder: (context, isHovered) => DebounceClickWidget(
        onTap: onTap,
        child: Container(
          width: 280,
          height: 48,
          margin: const EdgeInsets.only(left: 30),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color:
                isHovered ? Colors.white.withOpacity(0.05) : Colors.transparent,
          ),
          child: Stack(
            children: [
              Positioned(
                left: 12,
                top: 12,
                child: Image.asset(
                  icon,
                  width: 24,
                  height: 24,
                ),
              ),
              Positioned(
                left: 44,
                top: 14,
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    height: 1.4,
                    color: Colors.white,
                  ),
                ),
              ),
              if (showVersion)
                Positioned(
                  right: 12,
                  top: 14,
                  height: 20,
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 6, right: 6, top: 2, bottom: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        'v${AppInfo.version}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          height: 1.0,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _OptionItem {
  final String icon;
  final String title;
  final VoidCallback? onTap;
  final bool showVersion;

  _OptionItem({
    required this.icon,
    required this.title,
    this.onTap,
    this.showVersion = false,
  });
}
