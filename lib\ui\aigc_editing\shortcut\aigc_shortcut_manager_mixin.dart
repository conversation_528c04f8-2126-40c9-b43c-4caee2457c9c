import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_keyboard_event_manager.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_key.dart';
import 'package:turing_art/utils/pg_log.dart';

// Widget快捷键管理Mixin
mixin ShortcutManagerMixin<T extends StatefulWidget> on State<T> {
  late String _widgetId;

  String? _routeName;

  final KeyboardEventManager _keyboardManager = KeyboardEventManager.instance;

  @override
  void initState() {
    super.initState();
    _widgetId = '${widget.runtimeType}_$hashCode';
    _keyboardManager.startListening();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 获取当前路由名称，仅在 _routeName 为空时设置，此处使用了语法糖（Null Aware Assignment）
    _routeName ??= GoRouterState.of(context).matchedLocation;
  }

  @override
  void dispose() {
    _keyboardManager.unregisterWidgetShortcuts(_widgetId);
    super.dispose();
  }

  // 注册快捷键
  void registerShortcut({
    required String id,
    required ShortcutKey shortcut,
    required KeyboardEventHandler handler,
    required String description,
    int priority = 0,
    bool Function()? additionalCheck,
  }) {
    _keyboardManager.registerShortcut(
      id: '${_widgetId}_$id',
      widgetId: _widgetId,
      shortcut: shortcut,
      handler: handler,
      description: description,
      priority: priority,
      canExecute: () {
        if (!_isInForeground()) {
          return false;
        }

        // 额外检查条件
        if (additionalCheck != null && !additionalCheck()) {
          return false;
        }

        return true;
      },
    );
  }

  // 注册支持按键抬起的快捷键
  void registerShortcutWithKeyUp({
    required String id,
    required ShortcutKey shortcut,
    required KeyboardEventHandler handler,
    required String description,
    int priority = 0,
    bool Function()? additionalCheck,
    KeyboardEventHandler? onKeyUp,
  }) {
    _keyboardManager.registerShortcut(
      id: '${_widgetId}_$id',
      widgetId: _widgetId,
      shortcut: shortcut,
      handler: handler,
      description: description,
      priority: priority,
      onKeyUp: onKeyUp,
      canExecute: () {
        if (!_isInForeground()) {
          return false;
        }

        // 额外检查条件
        if (additionalCheck != null && !additionalCheck()) {
          return false;
        }

        return true;
      },
    );
  }

  // 注销快捷键
  void unregisterShortcut(String id) {
    _keyboardManager.unregisterShortcut('${_widgetId}_$id');
  }

  // 检查是否处于指定路由的前台
  bool _isInForeground() {
    if (!mounted) {
      return false;
    }

    try {
      final currentLocation = GoRouter.of(context)
          .routerDelegate
          .currentConfiguration
          .matches
          .last
          .matchedLocation;
      return currentLocation == _routeName;
    } catch (e) {
      PGLog.w('前台检测失败: $e');
      return false;
    }
  }

  // 获取Widget ID
  String get widgetId => _widgetId;
}
