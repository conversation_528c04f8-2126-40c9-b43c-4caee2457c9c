import 'dart:io';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/api/common_error_handler.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ui/aigc_presets/config/aigc_config.dart';
import 'package:turing_art/ui/core/base_view_model.dart';
import 'package:turing_art/utils/common_state.dart';
import 'package:turing_art/utils/error_handler.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 创建 AIGC 预设的 ViewModel
class CreatePresetViewModel extends BaseViewModel {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController promptController = TextEditingController();

  final AigcPresetsRepository _repository;

  // ignore: unused_field
  final MediaUploadRepository _uploadRepository;

  final OpsCustomTableRepository _customTableRepository;
  int _presetCost = 1;
  int get presetCost => _presetCost;

  final int _generateCount = AigcConfig.defaultGenerateCount;
  bool _isFormValid = false;

  /// 补充说明 section 是否展开，默认为false
  bool _isSupplementExpanded = false;

  /// 创建预设状态，用于控制页面刷新
  AsyncResult<void> _createStatus = const AsyncResult.idle();

  bool get isFormValid => _isFormValid;

  bool get isSupplementExpanded => _isSupplementExpanded;

  /// 是否正在加载
  bool get isLoading => _createStatus.isLoading;

  /// 创建状态
  AsyncResult<void> get createStatus => _createStatus;

  /// 计算需要消耗的 credit 数量
  int get requiredCredits => _presetCost * _generateCount;

  CreatePresetViewModel({
    required AigcPresetsRepository repository,
    required MediaUploadRepository uploadRepository,
    required OpsCustomTableRepository customTableRepository,
  })  : _repository = repository,
        _uploadRepository = uploadRepository,
        _customTableRepository = customTableRepository,
        super(ErrorHandler()) {
    nameController.addListener(_onFormChanged);
    promptController.addListener(_onFormChanged);
    _fetchAigcPointConfig();
  }

  Future<void> _fetchAigcPointConfig() async {
    try {
      final config = await _customTableRepository.getAigcPointConfig();
      _presetCost =
          config?.points?.firstWhere((e) => e.type == 'preset').value ?? 1;
      notifyListeners();
    } catch (e) {
      PGLog.e('Failed to fetch aigc point config: $e');
    }
  }

  void _onFormChanged() {
    final newFormValid = nameController.text.trim().isNotEmpty;
    if (_isFormValid != newFormValid) {
      _isFormValid = newFormValid;
      notifyListeners();
    }
  }

  /// 切换创意补充 section 的展开状态
  void toggleSupplementExpanded() {
    _isSupplementExpanded = !_isSupplementExpanded;

    // 如果是折叠状态（即从展开变为折叠），清空 promptController 的内容
    if (!_isSupplementExpanded) {
      promptController.clear();
    }

    notifyListeners();
  }

  /// 设置错误状态，包含网络检测
  void _setErrorStatus(String message, [Exception? exception]) {
    final finalMessage =
        exception != null ? handleException(exception, message) : message;
    _createStatus = AsyncResult.error(finalMessage, exception);
    notifyListeners();
  }

  /// 开始创建
  Future<void> startCreate(String imagePath, String maskPath) async {
    if (!_isFormValid || _createStatus.isLoading) {
      return;
    }

    // 设置加载状态
    _createStatus = const AsyncResult.loading();
    notifyListeners();

    try {
      PGLog.d('开始创建预设');
      PGLog.d('名称: ${nameController.text}');
      PGLog.d('创意补充: ${promptController.text}');
      PGLog.d('原图路径: $imagePath');
      PGLog.d('蒙版图路径: $maskPath');

      // 上传原图
      final imageFile = File(imagePath);
      final imageUploadResult =
          await _uploadRepository.uploadSingleFile(imageFile);

      final uploadImageUrl = imageUploadResult.publicUrl;

      if (!imageUploadResult.success || uploadImageUrl == null) {
        PGLog.e('上传原图失败: ${imageUploadResult.error}');
        _setErrorStatus('上传原图失败');
        return;
      }

      // 上传蒙版图
      final maskFile = File(maskPath);
      final maskUploadResult =
          await _uploadRepository.uploadSingleFile(maskFile);
      final uploadMaskUrl = maskUploadResult.publicUrl;

      if (!maskUploadResult.success || uploadMaskUrl == null) {
        PGLog.e('上传蒙版图失败: ${maskUploadResult.error}');
        _setErrorStatus('上传蒙版图失败');
        return;
      }

      // 调用接口创建预设
      await _repository.createAigcPresets(
        nameController.text.trim(),
        promptController.text.trim(),
        uploadImageUrl,
        uploadMaskUrl,
        _generateCount,
      );

      // 设置成功状态
      _createStatus = AsyncResult.success(null);
      notifyListeners();
    } catch (e) {
      PGLog.e('创建预设失败: $e');
      // 使用统一的错误处理器
      final (errorMessage, _) =
          CommonBusinessErrorHandler.handleError(e, '创建预设');
      _setErrorStatus(
          errorMessage, e is Exception ? e : Exception(e.toString()));
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    promptController.dispose();
    super.dispose();
  }
}
