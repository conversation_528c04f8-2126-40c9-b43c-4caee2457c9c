// 磁盘信息相关数据模型

import 'package:turing_art/ffi/ffi.dart' show DiskType;

export 'package:turing_art/ffi/ffi.dart' show DiskType;
export 'package:turing_art/ffi/native/disk_info_bindings.dart'
    show DiskSpaceInfo;

/// 磁盘信息查询结果
class DiskInfoResult {
  final String path;
  final String driveRoot;
  final DiskType type;
  final bool fromCache;
  final DateTime queriedAt;

  const DiskInfoResult({
    required this.path,
    required this.driveRoot,
    required this.type,
    required this.fromCache,
    required this.queriedAt,
  });

  @override
  String toString() =>
      'DiskInfoResult(path: $path, driveRoot: $driveRoot, type: $type, fromCache: $fromCache)';
}

/// 磁盘信息缓存统计
class DiskInfoCacheStats {
  final bool initialized;
  final bool initializing;
  final int cacheSize;
  final Map<DiskType, int> typeDistribution;
  final List<String> cachedDrives;
  final DateTime lastUpdated;

  const DiskInfoCacheStats({
    required this.initialized,
    required this.initializing,
    required this.cacheSize,
    required this.typeDistribution,
    required this.cachedDrives,
    required this.lastUpdated,
  });

  @override
  String toString() =>
      'DiskInfoCacheStats(initialized: $initialized, cacheSize: $cacheSize, types: $typeDistribution)';
}
