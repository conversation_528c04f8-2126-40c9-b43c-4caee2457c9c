import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/blur_container.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/screen_util.dart';

/// 导入项目选择对话框
/// 当项目已存在时显示，用户可以选择新建工程或覆盖工程
class ImportProjectChoiceDialog {
  static const _tag = "ImportProjectChoiceDialog";

  /// 展示弹窗
  /// [onCreateNew] 创建新工程回调
  /// [onOverwrite] 覆盖工程回调
  /// [onCancel] 取消回调
  static void show({
    VoidCallback? onCreateNew,
    VoidCallback? onOverwrite,
    VoidCallback? onCancel,
    required int fileCount,
    required int selectedCount,
  }) {
    SmartDialog.show(
      animationType: SmartAnimationType.fade,
      animationBuilder: (controller, child, param) =>
          AnimatedDialog(controller: controller, child: child),
      maskWidget: BlurContainer(
        blur: 12,
        child: SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
        ),
      ),
      builder: (context) => _ImportProjectChoiceDialogContent(
        onCreateNew: onCreateNew ?? hide,
        onOverwrite: onOverwrite ?? hide,
        onCancel: onCancel ?? hide,
        fileCount: fileCount,
        selectedCount: selectedCount,
      ),
      tag: _tag,
    );
  }

  /// 隐藏弹窗
  static void hide() {
    SmartDialog.dismiss(tag: _tag);
  }

  /// 展示协作请求弹窗（编辑页面状态下的特殊弹窗）
  /// [onCreateNew] 创建新工程回调
  /// [onCancel] 取消回调
  static void showCollaborationRequest({
    VoidCallback? onCreateNew,
    VoidCallback? onCancel,
    required int fileCount,
    required int selectedCount,
  }) {
    SmartDialog.show(
      animationType: SmartAnimationType.fade,
      animationBuilder: (controller, child, param) =>
          AnimatedDialog(controller: controller, child: child),
      maskWidget: BlurContainer(
        blur: 0,
        child: SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
        ),
      ),
      builder: (context) => _CollaborationRequestDialogContent(
        onCreateNew: onCreateNew ?? hide,
        onCancel: onCancel ?? hide,
        fileCount: fileCount,
        selectedCount: selectedCount,
      ),
      tag: _tag,
    );
  }

  /// 展示编辑页面协作请求弹窗
  /// [onProcessNow] 立即处理回调
  /// [onProcessLater] 稍后处理回调
  static void showEditPageCollaboration({
    VoidCallback? onProcessNow,
    VoidCallback? onProcessLater,
  }) {
    // 使用PGDialog.showCustomDialogOnUnity在编辑页面显示弹窗
    PGDialog.showCustomDialogOnUnity(
      width: 398,
      height: 280,
      centerInWindow: true,
      roundRadius: 12,
      tag: 'edit_page_collaboration_dialog',
      child: _EditPageCollaborationDialogContent(
        onProcessNow: onProcessNow ?? hide,
        onProcessLater: onProcessLater ?? hide,
      ),
    );
  }
}

class _ImportProjectChoiceDialogContent extends StatelessWidget {
  final VoidCallback onCreateNew;
  final VoidCallback onOverwrite;
  final VoidCallback onCancel;
  final int fileCount;
  final int selectedCount;
  const _ImportProjectChoiceDialogContent({
    required this.onCreateNew,
    required this.onOverwrite,
    required this.onCancel,
    required this.fileCount,
    required this.selectedCount,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {}, // 点击背景不关闭
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: GestureDetector(
            onTap: () {
              // 阻止事件冒泡
            },
            child: Container(
              width: 398,
              height: 320,
              decoration: BoxDecoration(
                color: const Color(0xFF1E1E1E),
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x33000000),
                    offset: Offset(0, 4),
                    blurRadius: 40,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // 关闭按钮
                  Positioned(
                    top: 12,
                    right: 12,
                    child: GestureDetector(
                      onTap: onCancel,
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Image.asset(
                          'assets/icons/dialog_close.png',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ),
                  ),

                  // 内容区域
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 36),

                        // 标题
                        Text(
                          "该项目已存在！共 $fileCount 张照片, 本次选择标识 $selectedCount 张。请选择合适的操作以继续",
                          style: TextStyle(
                            fontSize: 16,
                            color: const Color(0xFFFFFFFF),
                            fontWeight: Fonts.semiBold,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 32),

                        // 创建新项目按钮
                        _buildButton(
                          text: "创建新项目",
                          onTap: onCreateNew,
                          backgroundColor: const Color(0xFFF72561),
                          textColor: const Color(0xFFFFFFFF),
                        ),

                        const SizedBox(height: 12),

                        // 覆盖原来的项目按钮
                        _buildButton(
                          text: "导入「筛选标识」至原项目",
                          onTap: onOverwrite,
                          backgroundColor: const Color(0xFFF72561),
                          textColor: const Color(0xFFFFFFFF),
                        ),

                        const SizedBox(height: 24),

                        // 取消按钮
                        _buildButton(
                          text: "取消",
                          onTap: onCancel,
                          backgroundColor: const Color(0xFF2B2B2B),
                          textColor: const Color(0xFFFFFFFF),
                        ),

                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: Fonts.semiBold,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }
}

/// 协作请求对话框内容组件
class _CollaborationRequestDialogContent extends StatelessWidget {
  final VoidCallback onCreateNew;
  final VoidCallback onCancel;
  final int fileCount;
  final int selectedCount;
  const _CollaborationRequestDialogContent({
    required this.onCreateNew,
    required this.onCancel,
    required this.fileCount,
    required this.selectedCount,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {}, // 点击背景不关闭
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: GestureDetector(
            onTap: () {
              // 阻止事件冒泡
            },
            child: Container(
              width: 398,
              height: 270,
              decoration: BoxDecoration(
                color: const Color(0xFF1E1E1E),
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x33000000),
                    offset: Offset(0, 4),
                    blurRadius: 40,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // 关闭按钮
                  Positioned(
                    top: 12,
                    right: 12,
                    child: GestureDetector(
                      onTap: onCancel,
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Image.asset(
                          'assets/icons/dialog_close.png',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ),
                  ),

                  // 内容区域
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 36),

                        // 标题
                        Text(
                          "收到新的协作请求，共 $fileCount 张照片, 本次选择标识 $selectedCount 张。请立即处理",
                          style: TextStyle(
                            fontSize: 16,
                            color: const Color(0xFFFFFFFF),
                            fontWeight: Fonts.semiBold,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 32),

                        // 创建新项目按钮
                        _buildButton(
                          text: "创建新项目",
                          onTap: onCreateNew,
                          backgroundColor: const Color(0xFFF72561),
                          textColor: const Color(0xFFFFFFFF),
                        ),

                        const SizedBox(height: 24),

                        // 取消按钮
                        _buildButton(
                          text: "取消",
                          onTap: onCancel,
                          backgroundColor: const Color(0xFF2B2B2B),
                          textColor: const Color(0xFFFFFFFF),
                        ),

                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: Fonts.semiBold,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }
}

/// 编辑页面协作请求对话框内容组件
class _EditPageCollaborationDialogContent extends StatelessWidget {
  final VoidCallback onProcessNow;
  final VoidCallback onProcessLater;

  const _EditPageCollaborationDialogContent({
    required this.onProcessNow,
    required this.onProcessLater,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 398,
      height: 280,
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x33000000),
            offset: Offset(0, 4),
            blurRadius: 40,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 关闭按钮
          Positioned(
            top: 12,
            right: 12,
            child: GestureDetector(
              onTap: onProcessLater, // 关闭按钮等同于稍后处理
              child: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(
                  'assets/icons/dialog_close.png',
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ),

          // 内容区域
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 36),

                // 标题
                Text(
                  "收到新的协作请求，选择「稍后处理」在你回到首页时可处理协作任务。",
                  style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xFFFFFFFF),
                    fontWeight: Fonts.semiBold,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // 立即处理按钮
                _buildButton(
                  text: "立即处理",
                  onTap: onProcessNow,
                  backgroundColor: const Color(0xFFF72561),
                  textColor: const Color(0xFFFFFFFF),
                ),

                const SizedBox(height: 24),

                // 稍后处理按钮
                _buildButton(
                  text: "稍后处理",
                  onTap: onProcessLater,
                  backgroundColor: const Color(0xFF2B2B2B),
                  textColor: const Color(0xFFFFFFFF),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: Fonts.semiBold,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }
}
