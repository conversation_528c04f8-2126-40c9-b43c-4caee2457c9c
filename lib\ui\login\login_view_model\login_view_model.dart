import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/account/account.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/ui/core/message/message_state.dart';
import 'package:turing_art/ui/profile/use_case/login_use_case.dart';
import 'package:turing_art/utils/pg_log.dart';

class ValidationResult {
  final bool isValid;
  final ValidationError? error;

  ValidationResult({
    required this.isValid,
    this.error,
  });
}

enum ValidationError {
  emptyPhoneNumber,
  invalidPhoneNumber,
  emptyCode,
  invalidCode,
  privacyNotAgreed,
}

class PhoneValidationResult {
  final bool isValid;
  final String? errorMessage;

  PhoneValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}

class PhoneNumberInfo {
  final String phoneNumber;
  final String? countryCode;

  PhoneNumberInfo({
    required this.phoneNumber,
    this.countryCode,
  });
}

class LoginViewModel extends ChangeNotifier {
  final AuthRepository _authRepository;
  final LoginUseCase _loginUseCase;
  bool _isLoading = false;
  MessageState? _messageState;

  // Getters
  bool get isLoading => _isLoading;
  MessageState? get messageState => _messageState;

  LoginViewModel(this._authRepository, this._loginUseCase);

  // 清除消息
  void clearMessage() {
    _messageState = null;
    notifyListeners();
  }

  // 获取登录验证码
  Future<void> getLoginCode(String phoneNumber) async {
    final validationResult = _validatePhoneNumber(phoneNumber);
    if (!validationResult.isValid) {
      _messageState = MessageState(
        message: validationResult.errorMessage!,
        isError: true,
      );
      notifyListeners();
      throw Exception(validationResult.errorMessage!);
    }

    try {
      _setLoading(true);
      // 解析手机号码，分离区号和手机号
      final phoneInfo = _parsePhoneNumber(phoneNumber);
      final result = await _loginUseCase.sendVerificationCode(
          phoneInfo.phoneNumber, phoneInfo.countryCode);
      if (result) {
        _messageState = MessageState(message: '验证码发送成功');
        return; // 成功发送验证码，返回正常结果
      } else {
        _messageState = MessageState(
          message: '验证码发送失败',
          isError: true,
        );
        throw Exception('验证码发送失败');
      }
    } catch (e) {
      _messageState = MessageState(
        message: '验证码发送失败, 请稍后再试',
        isError: true,
      );
      PGLog.e('获取登录验证码失败: $e');
      rethrow; // 重新抛出异常
    } finally {
      _setLoading(false);
    }
  }

  // 登录
  Future<bool> login(String phoneNumber, String code, String storeId) async {
    _setLoading(true);

    // 解析手机号码，分离区号和手机号
    final phoneInfo = _parsePhoneNumber(phoneNumber);

    // 使用LoginUseCase执行登录
    final result = await _loginUseCase.invoke(
      phoneNumber: phoneInfo.phoneNumber,
      cc: phoneInfo.countryCode,
      code: code,
      storeId: storeId,
    );

    // 将LoginResult转换为MessageState
    _messageState = MessageState(
      message: result.message,
      isError: !result.isSuccess,
    );

    _setLoading(false);
    return result.isSuccess;
  }

  ValidationResult validateInputs({
    required String phoneNumber,
    required String code,
    required bool isPrivacyChecked,
  }) {
    final phoneValidation = _validatePhoneNumber(phoneNumber);
    if (!phoneValidation.isValid) {
      return ValidationResult(
        isValid: false,
        error: ValidationError.invalidPhoneNumber,
      );
    }

    if (code.isEmpty) {
      return ValidationResult(
        isValid: false,
        error: ValidationError.emptyCode,
      );
    }

    if (code.length != 4) {
      return ValidationResult(
        isValid: false,
        error: ValidationError.invalidCode,
      );
    }

    if (!isPrivacyChecked) {
      return ValidationResult(
        isValid: false,
        error: ValidationError.privacyNotAgreed,
      );
    }

    return ValidationResult(isValid: true);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 检查账户列表
  /// 如果只有一个账户，则返回账户ID和账户列表
  /// 如果有多个账户，则返回空字符串和账户列表
  Future<(String, List<Account>?)> checkAccountList(
      String phoneNumber, String code) async {
    final phoneValidation = _validatePhoneNumber(phoneNumber);
    if (!phoneValidation.isValid) {
      _messageState = MessageState(
        message: phoneValidation.errorMessage!,
        isError: true,
      );
      notifyListeners();
      return ('', null);
    }
    if (code.isEmpty) {
      _messageState = MessageState(
        message: '请输入验证码',
        isError: true,
      );
      notifyListeners();
      return ('', null);
    }
    try {
      _setLoading(true);
      final list = await _authRepository.getAccountList(phoneNumber);
      _setLoading(false);
      if (list.length == 1) {
        return (list.first.storeId, list);
      } else {
        return ('', list);
      }
    } catch (e) {
      PGLog.e('获取账户列表失败: $e');
      String errorMessage = '获取账户列表失败';
      _messageState = MessageState(
        message: errorMessage,
        isError: true,
      );
      PGLog.e('获取账户列表失败: $e');
      return ('', null);
    } finally {
      _setLoading(false);
    }
  }

  // 获取最后登录的门店ID
  Future<String> getLastLoginStoreIdByPhoneNumber(String phoneNumber) async {
    return await _authRepository.getLastLoginStoreIdByPhoneNumber(phoneNumber);
  }

  /// 验证手机号码格式
  /// 支持输入数字和一个唯一特殊字符"+"
  /// 输入中有特殊字符后取消输入位数限制，否则按原有逻辑有位数限制
  /// 输入字符开始和结尾都不能为"+"，否则输入无效
  PhoneValidationResult _validatePhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: '请输入手机号',
      );
    }

    // 检查开始和结尾是否为"+"
    if (phoneNumber.startsWith('+') || phoneNumber.endsWith('+')) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: '手机号格式不正确',
      );
    }

    // 检查是否只包含数字和最多一个"+"
    final plusCount = '+'.allMatches(phoneNumber).length;
    if (plusCount > 1) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: '手机号格式不正确',
      );
    }

    // 检查是否只包含数字和"+"
    final validChars = RegExp(r'^[0-9+]+$');
    if (!validChars.hasMatch(phoneNumber)) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: '手机号只能包含数字和"+"',
      );
    }

    // 如果包含"+"，则为海外格式，不限制位数
    if (phoneNumber.contains('+')) {
      // 海外格式：区号+手机号，比如"86+13678156435"
      final parts = phoneNumber.split('+');
      if (parts.length != 2 || parts[0].isEmpty || parts[1].isEmpty) {
        return PhoneValidationResult(
          isValid: false,
          errorMessage: '海外手机号格式应为：区号+手机号',
        );
      }
      return PhoneValidationResult(isValid: true);
    } else {
      // 大陆地区手机号，按原有逻辑，11位数字
      if (phoneNumber.length != 11) {
        return PhoneValidationResult(
          isValid: false,
          errorMessage: '请输入正确的手机号',
        );
      }
      return PhoneValidationResult(isValid: true);
    }
  }

  /// 解析手机号码，分离区号和手机号
  /// 按"+"进行拆分，字符串数组大于1则首部分为cc参数，后部分为手机号参数
  /// 否则原有phoneNumber则为手机号
  PhoneNumberInfo _parsePhoneNumber(String phoneNumber) {
    if (phoneNumber.contains('+')) {
      final parts = phoneNumber.split('+');
      if (parts.length == 2) {
        return PhoneNumberInfo(
          phoneNumber: parts[1],
          countryCode: parts[0],
        );
      }
    }

    // 默认为大陆地区，区号为86
    return PhoneNumberInfo(
      phoneNumber: phoneNumber,
      countryCode: null, // 让后端使用默认的86
    );
  }
}
