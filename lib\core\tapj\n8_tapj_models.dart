/// N8导出文件信息
class N8ExportFileInfo {
  String fileId;
  String fileName;
  String originalPath;
  String historyId;
  String exportPath;
  bool isSelected;

  N8ExportFileInfo({
    required this.fileId,
    required this.fileName,
    required this.originalPath,
    required this.historyId,
    required this.exportPath,
    required this.isSelected,
  });

  Map<String, dynamic> toJson() {
    return {
      'fileId': fileId,
      'fileName': fileName,
      'originalPath': originalPath,
      'historyId': historyId,
      'exportPath': exportPath,
      'isSelected': isSelected,
    };
  }

  factory N8ExportFileInfo.fromJson(Map<String, dynamic> json) {
    return N8ExportFileInfo(
      fileId: json['fileId'] ?? '',
      fileName: json['fileName'] ?? '',
      originalPath: json['originalPath'] ?? '',
      historyId: json['historyId'] ?? '',
      exportPath: json['exportPath'] ?? '',
      isSelected: json['isSelected'] ?? false,
    );
  }
}

/// N8导出工程数据
class N8ExportProjectData {
  String rawWorkspaceId;
  String exportTime;
  int createTime;
  String workspaceName;
  List<N8ExportFileInfo> fileList;

  N8ExportProjectData({
    required this.rawWorkspaceId,
    required this.exportTime,
    required this.createTime,
    required this.workspaceName,
    required this.fileList,
  });

  Map<String, dynamic> toJson() {
    return {
      'rawWorkspaceId': rawWorkspaceId,
      'exportTime': exportTime,
      'createTime': createTime,
      'workspaceName': workspaceName,
      'fileList': fileList.map((file) => file.toJson()).toList(),
    };
  }

  factory N8ExportProjectData.fromJson(Map<String, dynamic> json) {
    return N8ExportProjectData(
      rawWorkspaceId: json['rawWorkspaceId'] ?? '',
      exportTime: json['exportTime'] ?? '',
      createTime: json['createTime'] ?? '',
      workspaceName: json['workspaceName'] ?? '',
      fileList: (json['fileList'] as List<dynamic>?)
              ?.map((item) =>
                  N8ExportFileInfo.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

/// TAPJ读取结果
class N8TapjReadResult {
  N8ExportProjectData projectData;
  Map<String, List<int>> historyFiles;

  N8TapjReadResult({
    required this.projectData,
    required this.historyFiles,
  });
}
