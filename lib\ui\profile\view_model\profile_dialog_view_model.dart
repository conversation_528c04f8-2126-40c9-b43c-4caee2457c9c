import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/enums/user_info_change_event_type.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/domain/models/account/account_all.dart';
import 'package:turing_art/datalayer/domain/models/ops_operation/ops_operation.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 账户状态变更监听器抽象类
abstract class AccountChangeListener {
  /// 账户状态变更回调方法
  void onAccountChange(ProfileDialogViewModel viewModel);
}

/// 账户状态变更接口
mixin AccountChangeInterface {
  /// 设置账户状态变更监听器
  void setAccountChangeListener(AccountChangeListener? listener);
}

class ProfileDialogViewModel extends ChangeNotifier
    implements AccountChangeInterface {
  final CurrentUserRepository _currentUserRepository;
  final AccountRepository _accountRepository;
  final WechatGiftRepository _wechatGiftRepository;
  final OpsCustomTableRepository _opsCustomTableRepository;
  final AccountRightsStateProvider _accountRightsStateProvider;
  final RewardRepository _rewardRepository;
  final NewUserRepository _newUserRepository;
  final AuthUseCaseProvider _authUseCaseProvider;

  User? _user;
  AccountAll? _accountAll;
  List<OperationActivity>? _wechatGiftList;
  bool _isFirstExportSuccess = false;
  bool _isFirstBuySuccess = false;
  bool _hasCustomerSupport = false;
  List<String>? _noWechatGiftStoreIds; // 不显示微信礼包的店铺ID列表
  bool _showWechatGiftForAllStore = false; // 是否显示微信礼包入口
  bool _isFinishInit = false; // 是否完成初始化(防止闪现特定条件展示的控件从有到无)
  bool _disposed = false; // 跟踪是否已经dispose

  // 新增：获取新人权益
  OperationActivity? _newUserActivity;
  OperationActivity? get newUserActivity => _newUserActivity;

  String? get effectiveId => _user?.effectiveId;
  String? get userId => _user?.userId;
  String? get mobile => _user?.mobile;
  String? get nickname => _user?.nickname;
  UserRole? get role => _user?.role;
  // 主账号手机号
  String? get mainAccountMobile => _currentUserRepository.creator?.mobile;
  AccountAll? get accountAll => _accountAll;
  bool get isSubAccount => _user?.role == UserRole.employee;
  // 是否开启账号管理（片量共享入口）
  bool get isOpenAccountManagement =>
      _currentUserRepository.store?.volumeControl ?? false;
  bool get isFirstBuySuccess => _isFirstBuySuccess;
  bool get isFirstExportSuccess => _isFirstExportSuccess;
  bool get isFinishInit => _isFinishInit;

  // 获取精修导出剩余张数
  AccountInfo? get editExportInfo => _accountAll?.exportInfo;
  AccountInfo? get aigcInfo => _accountAll?.aigcInfo;
  // 获取积分数量
  int get integralCount => _accountAll?.aigcInfo?.available ?? 0;

  // 用户账户变更事件流
  late StreamSubscription<String> _userAccountChangeSubscription;

  // 用户store信息变更事件流
  late StreamSubscription<UserInfoChangeEventType>
      _currentUserStoreInfoChangeSubscription;

  // 账户变更监听器
  AccountChangeListener? _accountChangeListener;

  /// 安全的通知监听器方法，防止在dispose后调用
  void _safeNotifyListeners() {
    if (!_disposed) {
      notifyListeners();
    }
  }

  ProfileDialogViewModel(
    this._currentUserRepository,
    this._accountRepository,
    this._wechatGiftRepository,
    this._opsCustomTableRepository,
    this._accountRightsStateProvider,
    this._rewardRepository,
    this._newUserRepository,
    this._authUseCaseProvider,
  ) {
    _init();
  }

  @override
  void setAccountChangeListener(AccountChangeListener? listener) {
    _accountChangeListener = listener;
  }

  Future<void> _init() async {
    try {
      // 初始化订阅（先订阅，以免在await步骤多时间久的情况下收不到消息，导致无法刷新account）
      initialSubscription();
      // 初始化数据
      _loadUserProfile();
      // 获取账户信息
      _loadAccountInfo();
      // 获取是否需要展示企业微信礼包相关数据(必须await才能决定布局UI)，权限的增减可能比较频繁，需要冷启动就刷新
      await _currentUserRepository.refreshStore();
      // 初始化是否有销售，方便监听第一次添加顾问成功
      _hasCustomerSupport =
          _currentUserRepository.store?.customerSupport?.isNotEmpty == true;
      // 获取新人权益(满足条件才获取数据并上报)
      final needShowNewUserGiftDialog = getNeedShowNewUserGiftDialog();
      if (needShowNewUserGiftDialog) {
        PGLog.d('ProfileDialogViewModel - _init getNewUserBenefits');
        await getNewUserBenefits();
      }

      // 这里还需要判断是否加过销售微信，未加过才展示"完善信息"
      // 获取不需要展示微信礼包的店铺ID列表
      _noWechatGiftStoreIds =
          await _opsCustomTableRepository.getNoWechatGiftStoreIds();
      _showWechatGiftForAllStore =
          await _opsCustomTableRepository.getShowWechatGiftForAllStore();
      await getWechatGiftBenefits();

      final needShowWechatGiftDialog = getNeedShowWechatGiftDialog();
      if (needShowWechatGiftDialog) {
        PGLog.d('ProfileDialogViewModel - _init 开始上报微信礼包权益');
        await _reportWeChatGift();
      } else {
        PGLog.d('ProfileDialogViewModel - _init 不需要上报微信礼包权益');
      }
      // 开始检查权益状态(保障先获取权益、上报权益，最后检查状态)
      if (needShowNewUserGiftDialog || needShowWechatGiftDialog) {
        PGLog.d('ProfileDialogViewModel - _init 开始检查权益状态');
        checkAndStartRightsPollingIfNeeded();
      }
      _isFinishInit = true;
      // 数据准备完备刷新一次UI
      _safeNotifyListeners();
    } on Exception catch (e) {
      PGLog.e('初始化ProfileDialogViewModel失败: $e');
    }
  }

  void initialSubscription() {
    _userAccountChangeSubscription = _accountRepository.userAccountChange
        .listen(_handleUserAccountChangeEvent);
    // 监听用户store信息变更事件
    _currentUserStoreInfoChangeSubscription = _currentUserRepository
        .currentUserInfoChange
        .listen(_handleCurrentUserStoreInfoChangeEvent);
  }

  // 处理用户账户变更事件(主动调用+订单支付成功被动触发)
  void _handleUserAccountChangeEvent(String event) {
    PGLog.d('ProfileDialogViewModel - _handleUserAccountChangeEvent $event');
    // 从账户数量上判断是否是第一次购买成功
    final beforeCountTotal = editExportInfo?.total;
    final beforeCount = beforeCountTotal ?? 0;
    final int availableCount = editExportInfo?.available ?? 0;
    _accountAll = _accountRepository.accountAll;
    final int afterCount = _accountAll?.exportInfo.total ?? 0;
    final int afterAvailableCount = _accountAll?.exportInfo.available ?? 0;
    // 是否是第一次购买成功(新人购买福利到账接通后，此判断逻辑修改，产品定的100张作为判断，因为提需求给服务端周期会变长)
    const int judgmentCount = 100;
    _isFirstBuySuccess = beforeCountTotal != null && // 避免一开始没有请求到数据，导致判断错误
        beforeCount < judgmentCount &&
        afterCount != beforeCount &&
        afterCount > judgmentCount;
    PGLog.d(
        'ProfileDialogViewModel - _handleUserAccountChangeEvent beforeCount $beforeCount availableCount $availableCount afterCount $afterCount afterAvailableCount $afterAvailableCount');
    // 是否是第一次导出成功（刷新前总数和可用数一致且大于0，刷新后可用数不一致，说明消费了）
    if (!_isFirstExportSuccess) {
      PGLog.d('ProfileDialogViewModel - 不是第一次导出成功，可以重新计算');
      _isFirstExportSuccess = availableCount == beforeCount &&
          beforeCount > 0 &&
          afterAvailableCount != availableCount;
    } else {
      PGLog.d('ProfileDialogViewModel - 是第一次导出成功，不重新计算');
    }
    PGLog.d(
        'ProfileDialogViewModel - _handleUserAccountChangeEvent $beforeCount $afterCount $availableCount $afterAvailableCount');
    PGLog.d(
        'ProfileDialogViewModel - _handleUserAccountChangeEvent _isFirstBuySuccess $_isFirstBuySuccess _isFirstExportSuccess $_isFirstExportSuccess');

    // 先触发账户变更回调
    PGLog.d(
        'ProfileDialogViewModel - _accountChangeListener: $_accountChangeListener');
    if (_accountChangeListener != null) {
      PGLog.d(
          'ProfileDialogViewModel - _handleUserAccountChangeEvent 触发账户变更回调');
      _accountChangeListener!.onAccountChange(this);
    }

    // 刷新UI上的账户信息
    _safeNotifyListeners();
  }

  // 处理用户store信息变更事件(目前是被动触发)
  void _handleCurrentUserStoreInfoChangeEvent(UserInfoChangeEventType event) {
    PGLog.d(
        'ProfileDialogViewModel - _handleCurrentUserStoreInfoChangeEvent $event');
    switch (event) {
      case UserInfoChangeEventType.storeRefreshed:
        // 刷新UI上，是否需要展示"完善信息"按钮，布局发生变更
        _safeNotifyListeners();
        break;
      default:
        break;
    }
  }

  // 尝试更新用户数据
  Future<void> tryLoadUserProfile() async {
    if (_user == null) {
      await _loadUserProfile();
    }
  }

  /// 获取用户信息(此接口有些信息暂时不确定)
  Future<void> _loadUserProfile() async {
    // 在编辑页面，目前显示loading会有层级不正确的bug，25号版本暂时注释掉
    // PGDialog.showLoading();

    try {
      final user = _currentUserRepository.user;
      if (user != null) {
        _user = user;
        _safeNotifyListeners();
      } else {
        // 如果获取不到用户信息，检查登录状态
        final isLoggedIn = _currentUserRepository.isLoggedIn;
        if (!isLoggedIn) {
          // 如果未登录，可以在这里处理，比如关闭弹窗并跳转到登录页面
          PGLog.d('用户未登录');
        }
      }
    } catch (e) {
      PGLog.d('加载用户信息失败: $e');
    } finally {
      // PGDialog.dismiss();
    }
  }

  /// 获取账户信息(刚刚进入页面需要等待，设置_account，以免出现外部不同时机调用误判_isFirstBuySuccess)
  Future<void> _loadAccountInfo() async {
    // 获取完整账户信息（包含积分等信息）
    await _accountRepository.refreshAllAccount();
    _accountAll = _accountRepository.accountAll;
  }

  /// 刷新账户信息
  Future<void> refreshAccount() async {
    // 刷新完整账户信息（包含积分等信息）
    await _accountRepository.refreshAllAccount();
    _accountAll = _accountRepository.accountAll;
  }

  /// 刷新完整账户信息
  Future<void> refreshAllAccount() async {
    await _accountRepository.refreshAllAccount();
    _accountAll = _accountRepository.accountAll;
  }

  // 获取微信礼包权益(如果有完善信息按钮，并点击了，就需要等待回调)
  Future<List<OperationActivity>?> getWechatGiftBenefits() async {
    if (_wechatGiftList != null && _wechatGiftList!.isNotEmpty) {
      return _wechatGiftList;
    }

    _wechatGiftList = await _wechatGiftRepository.getWechatGiftInfo();
    return _wechatGiftList;
  }

  // ops获取成功就上报数量
  Future<void> _reportWeChatGift() async {
    // 增加部分保护逻辑，避免获取_wechatGiftList为空导致异常
    if (_wechatGiftList == null) {
      PGLog.d('ProfileDialogViewModel - _reportWeChatGift 没有拉取到微信礼包，不上报');
      return;
    }
    if (_wechatGiftList!.isEmpty) {
      PGLog.d('ProfileDialogViewModel - _reportWeChatGift 没有拉取到微信礼包，不上报');
      return;
    }
    if (_wechatGiftList!.length >= 2) {
      PGLog.d('ProfileDialogViewModel - _reportWeChatGift 至少有标题和免费导出礼包');

      final exportGift = _wechatGiftList![1];
      if (exportGift.id.isNotEmpty) {
        final isReportSuccess =
            await _rewardRepository.reportAddWechatSuccess(exportGift.id);
        if (isReportSuccess) {
          PGLog.d('ProfileDialogViewModel - _reportWeChatGift 上报添加微信好友成功');
        } else {
          PGLog.d('ProfileDialogViewModel - _reportWeChatGift 上报添加微信好友失败');
        }
      }
    }
  }

// 子账号过滤新人弹窗
  bool getNeedShowNewUserGiftDialog() {
    final isFirstLogin = _currentUserRepository.user?.firstLogin ?? false;
    final hasShow = UserPreferencesService.getHasShowNewUserGift();
    PGLog.d(
        'ProfileDialogViewModel - getNeedShowNewUserGiftDialog isFirstLogin $isFirstLogin hasShow $hasShow, isSubAccount $isSubAccount');
    // 如果是主账号，并且是第一次登录，并且没有展示过新人礼包，并且不是子账号，则展示新人礼包弹窗
    if (isFirstLogin && !hasShow && !isSubAccount) {
      return true;
    }
    return false;
  }

  // 主账号是否需要展示微信礼包权益弹窗(入口'完善信息'显示和购买成功、第一次导出显示)
  bool getNeedShowWechatGiftDialog() {
    // 【1】如果是子账号，则不展示微信礼包弹窗
    if (isSubAccount) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog 是子账号，没有权限展示微信礼包弹窗');
      return false;
    }
    // 【2】ops配置不展示微信礼包弹窗
    if (!_showWechatGiftForAllStore) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog ops配置不展示微信礼包弹窗');
      return false;
    }
    final store = _currentUserRepository.store;
    // 如果store为空异常则不需要展示微信礼包弹窗
    if (store == null) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog store为空，不展示微信礼包弹窗');
      return false;
    }
    // 【3】在黑名单则不展示微信礼包弹窗
    PGLog.d(
        'ProfileDialogViewModel - getNeedShowWechatGiftDialog noWechatGiftStoreIds: $_noWechatGiftStoreIds');
    if (_noWechatGiftStoreIds != null &&
        _noWechatGiftStoreIds!.isNotEmpty &&
        _noWechatGiftStoreIds!.contains(store.id)) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog storeId: ${store.id} 在noWechatGiftStoreIds列表中，不展示微信礼包弹窗');
      return false;
    }

    // 【4】已经添加了顾问，不需要再展示
    final hasCustomerSupport = store.customerSupport?.isNotEmpty ?? false;
    if (hasCustomerSupport) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog 已经添加了顾问，不展示微信礼包弹窗');
      return false;
    }

    // 是否正确配置了顾问，是否拉取到了，不然有入口也无法正常显示
    if (_wechatGiftList == null) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog 没有拉取到微信礼包，不展示微信礼包弹窗');
      return false;
    }
    if (_wechatGiftList!.isEmpty) {
      PGLog.d(
          'ProfileDialogViewModel - getNeedShowWechatGiftDialog 没有拉取到微信礼包，不展示微信礼包弹窗');
      return false;
    }
    PGLog.d(
        'ProfileDialogViewModel - getNeedShowWechatGiftDialog 需要展示微信礼包弹窗 store: $store');
    return true;
  }

  // 检查是否需要弹出微信礼包弹窗
  bool checkIsFirstNeedPopWechatGiftDialogStatus() {
    PGLog.d(
        'ProfileDialogViewModel - _checkIsFirstNeedPopWechatGiftDialogStatus 开始');
    // 如果已经显示过微信礼包弹窗，则不再显示
    if (UserPreferencesService.getHasShownWechatGiftDialog()) {
      PGLog.d(
          'ProfileDialogViewModel - _checkIsFirstNeedPopWechatGiftDialogStatus 缓存已经显示过微信礼包弹窗');
      return false;
    }
    PGLog.d(
        'ProfileDialogViewModel - _checkIsFirstNeedPopWechatGiftDialogStatus 没有显示过微信礼包弹窗 是否在编辑页面${GlobalVars.isWinEditScreenActive}');
    // 如果第一次导出成功，没有弹过，则调起加微信弹窗
    if (isFirstExportSuccess && !GlobalVars.isWinEditScreenActive) {
      return true;
    }

    // 如果第一次购买成功，则显示购买成功弹窗,没有弹过，再显示微信礼包弹窗
    if (isFirstBuySuccess && !GlobalVars.isWinEditScreenActive) {
      return true;
    }
    return false;
  }

  // 是第一次加顾问成功
  bool isWechatAddFirstTime() {
    final before = _hasCustomerSupport;
    final store = _currentUserRepository.store;
    // 没有加过销售微信，则需要展示
    final now = store != null && store.customerSupport?.isNotEmpty == true;
    // 更新_hasCustomerSupport
    _hasCustomerSupport = now;
    return now != before && before == false;
  }

  /// 退出登录
  Future<void> logout() async {
    PGDialog.showLoading();
    try {
      await _authUseCaseProvider.logoutUseCase.invoke();
    } catch (e) {
      PGLog.d('退出登录失败: $e');
    } finally {
      PGDialog.dismiss();
    }
  }

  // 新增：获取新人权益
  Future<OperationActivity?> getNewUserBenefits() async {
    if (_newUserActivity != null) {
      return _newUserActivity;
    }
    try {
      PGLog.d('ProfileDialogViewModel - getNewUserBenefits 开始获取新人权益');
      _newUserActivity = await _newUserRepository.getNewUserBenefits();
      PGLog.d(
          'ProfileDialogViewModel - getNewUserBenefits _newUserActivity: $_newUserActivity');
      // 如果获取成功，上报新人礼包领取
      if (_newUserActivity != null && _newUserActivity!.id.isNotEmpty) {
        await _reportNewUserGift();
      }
      return _newUserActivity;
    } catch (e) {
      PGLog.e('获取新人权益失败: $e');
      return null;
    }
  }

  // 新增：上报新人礼包领取
  Future<void> _reportNewUserGift() async {
    if (_newUserActivity == null || _newUserActivity!.id.isEmpty) {
      PGLog.e('新人礼包ID为空，无法上报');
      return;
    }
    try {
      PGLog.d(
          'ProfileDialogViewModel - _reportNewUserGift 上报新人礼包领取 id: ${_newUserActivity!.id}');
      final isReportSuccess =
          await _rewardRepository.reportNewUserGift(_newUserActivity!.id);
      if (isReportSuccess) {
        PGLog.d('ProfileDialogViewModel - _reportNewUserGift 上报新人礼包领取完成');
      } else {
        PGLog.d('ProfileDialogViewModel - _reportNewUserGift 上报新人礼包领取失败');
      }
    } catch (e) {
      PGLog.e('ProfileDialogViewModel - _reportNewUserGift 上报新人礼包领取失败: $e');
    }
  }

  // 新增：检查并开始权益轮询（有入口主动调用就开始检查，主要针对微信弹窗，如果权益都下发会触发相关刷新）
  void checkAndStartRightsPollingIfNeeded() {
    _accountRightsStateProvider.startAccountRightCheck();
  }

  @override
  void dispose() {
    _userAccountChangeSubscription.cancel();
    _currentUserStoreInfoChangeSubscription.cancel();
    _accountChangeListener = null;
    _disposed = true;
    super.dispose();
  }
}
