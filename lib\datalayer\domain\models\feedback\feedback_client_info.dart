import 'package:freezed_annotation/freezed_annotation.dart';

part 'feedback_client_info.freezed.dart';
part 'feedback_client_info.g.dart';

@freezed
class FeedbackClientInfo with _$FeedbackClientInfo {
  const factory FeedbackClientInfo({
    String? appVersion,
    String? systemVersion,
    String? cpuInfo,
    String? memoryInfo,
    String? diskInfo,
    String? gpuInfo,
    String? networkInfo,
    String? screenInfo,
    String? platform,
  }) = _FeedbackClientInfo;

  factory FeedbackClientInfo.fromJson(Map<String, dynamic> json) =>
      _$FeedbackClientInfoFromJson(json);
}
