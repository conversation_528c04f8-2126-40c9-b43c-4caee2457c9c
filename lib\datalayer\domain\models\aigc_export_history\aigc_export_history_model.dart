import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_export_history_model.freezed.dart';
part 'aigc_export_history_model.g.dart';

@freezed
class AIGCExportHistoryModel with _$AIGCExportHistoryModel {
  const factory AIGCExportHistoryModel({
    required List<AIGCExportHistoryItem> consumptions,
    required int total,
    required int page,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'page_size') required int pageSize,
  }) = _AIGCExportHistoryModel;

  factory AIGCExportHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$AIGCExportHistoryModelFromJson(json);
}

@freezed
class AIGCExportHistoryItem with _$AIGCExportHistoryItem {
  const factory AIGCExportHistoryItem({
    required String id,
    @J<PERSON><PERSON><PERSON>(name: 'account_role') required String accountRole,
    required String mobile,
    required String? nickname,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'photo_name') required String photoName,
    required String? hostname,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id') required String deviceId,
    @J<PERSON><PERSON><PERSON>(name: 'cost_type') required String costType,
    @J<PERSON><PERSON><PERSON>(name: 'cost_value') required int costValue,
    @Json<PERSON>ey(name: 'create_at') required int createAt,
  }) = _AIGCExportHistoryItem;

  factory AIGCExportHistoryItem.fromJson(Map<String, dynamic> json) =>
      _$AIGCExportHistoryItemFromJson(json);
}
