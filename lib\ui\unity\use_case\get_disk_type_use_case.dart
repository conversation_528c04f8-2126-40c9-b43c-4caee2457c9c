import 'dart:convert';

import 'package:turing_art/core/service/disk_info_service/disk_info_service.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 获取缓存磁盘空间用例
///
/// 该用例封装了获取缓存磁盘空间的过程
class GetDiskTypeUseCase {
  final UnityController _unityController;

  /// 创建 [GetDiskTypeUseCase] 实例
  GetDiskTypeUseCase(this._unityController);

  /// 获取当前设备磁盘类型
  ///
  /// 返回磁盘类型，失败返回 [ExportException]
  Future<void> invoke(MessageFromUnity message) async {
    if (message.args?.isEmpty == true) {
      return;
    }

    final taskId = message.taskId;
    if (taskId == null) {
      return;
    }

    try {
      // 解析 JSON 字符串获取 path
      final jsonStr = message.args?[0];
      if (jsonStr == null) {
        _unityController.sendTriggerCallback(
          taskId,
          '0',
        );
        PGLog.e('获取缓存磁盘空间失败: 参数错误 ${message.args}');
        return;
      }

      // 处理 JSON 字符串中的反斜杠
      final normalizedJsonStr = jsonStr.replaceAll('\\', '\\\\');

      final Map<String, dynamic> jsonMap = json.decode(normalizedJsonStr);
      final path = jsonMap['path'] as String?;

      if (path == null) {
        _unityController.sendTriggerCallback(
          taskId,
          '0',
        );
        PGLog.e('获取磁盘类型失败: 参数错误 ${message.args}');
        return;
      }

      final diskType = await DiskInfo.getDiskType(path);
      _unityController.sendTriggerCallback(
        taskId,
        diskType?.value.toString() ?? '0',
      );
      PGLog.i('获取磁盘类型成功: ${diskType?.value ?? 0}');
    } catch (e) {
      PGLog.e('获取磁盘类型失败: $e');
      _unityController.sendTriggerCallback(taskId, '0');
    }
  }
}
