import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_reconnect_file_dialog.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_image_selection_popup.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缩略图列表ViewModel
/// 负责缩略图列表的业务逻辑，包括选择、多选等
class AigcThumbnailListViewModel extends ChangeNotifier {
  final AigcEditingImageProvider _imageProvider;
  final AigcEditingMultiSelectProvider _multiSelectProvider;
  final MediaRepository _mediaRepository;
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final AigcPreviewImageItemAdapter _aigcPreviewImageItemAdapter;
  final AigcService _processingService;
  final FileManager _fileManager;
  late final ImageSelectionService _imageSelectionService;

  // 添加一个变量来存储订阅
  StreamSubscription<AigcTaskResultMessage>? _resultStreamSubscription;
  StreamSubscription<AigcTaskProgressMessage>? _progressStreamSubscription;

  /// 事件流订阅
  StreamSubscription<AigcEditingEventType>? _eventStreamSubscription;

  /// 多选事件流订阅
  StreamSubscription<AigcEditingMultiSelectEventType>?
      _multiSelectEventStreamSubscription;

  /// 蒙版任务监听订阅
  StreamSubscription<AigcTaskResultMessage>? _maskTaskSubscription;

  // 删除确认弹窗状态
  bool _showDeleteConfirmDialog = false;

  // 正在重连的文件ID集合，避免重复触发重连对话框
  String? _reconnectingFileId;

  /// 蒙版获取状态提供者
  AigcMaskAcquisitionProvider? _maskAcquisitionProvider;

  AigcThumbnailListViewModel._({
    required AigcEditingImageProvider imageProvider,
    required AigcEditingMultiSelectProvider multiSelectProvider,
    required MediaRepository mediaRepository,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required ProjectRepository projectRepository,
    required AigcService processingService,
    required FileManager fileManager,
  })  : _imageProvider = imageProvider,
        _multiSelectProvider = multiSelectProvider,
        _processingService = processingService,
        _mediaRepository = mediaRepository,
        _currentEditingProjectRepository = currentEditingProjectRepository,
        _fileManager = fileManager,
        _aigcPreviewImageItemAdapter =
            AigcPreviewImageItemAdapter(mediaRepository) {
    // 初始化 ImageSelectionService
    _imageSelectionService = ImageSelectionService.forPlatform();
    // 监听共享数据变化
    // _sharedDataProvider.addListener(_onSharedDataChanged);
    _setupRepositoryListener();
    _setupThumbnailListener();
    _setupMaskTaskListener();

    _imageProvider.addListener(_onImageSelectionChanged);
    _eventStreamSubscription = _imageProvider.eventStream.listen((event) {
      // 处理单选相关事件
      notifyListeners();
    });

    // 监听多选事件
    _multiSelectEventStreamSubscription =
        _multiSelectProvider.eventStream.listen((event) {
      if (event == AigcEditingMultiSelectEventType.multiSelectionChanged) {
        notifyListeners();
      }
    });
  }

  /// 工厂方法：通过context创建ViewModel
  /// UI组件调用此方法，不需要知道SharedDataProvider的存在
  static AigcThumbnailListViewModel create(BuildContext context) {
    final imageProvider = context.read<AigcEditingImageProvider>();
    final multiSelectProvider = context.read<AigcEditingMultiSelectProvider>();
    final processingService = context.read<AigcService>();
    final viewModel = AigcThumbnailListViewModel._(
      imageProvider: imageProvider,
      multiSelectProvider: multiSelectProvider,
      processingService: processingService,
      mediaRepository: context.read<MediaRepository>(),
      currentEditingProjectRepository:
          context.read<CurrentEditingProjectRepository>(),
      projectRepository: context.read<ProjectRepository>(),
      fileManager: FileManager(),
    );
    // 注入蒙版获取状态提供者
    viewModel._maskAcquisitionProvider =
        context.read<AigcMaskAcquisitionProvider>();
    return viewModel;
  }

  /// 获取图片列表
  List<AigcPreviewImageItem> get images => List.unmodifiable(_images);

  /// 获取选中索引
  int get selectedIndex => _selectedIndex;

  /// 获取多选ID集合
  Set<AigcPreviewImageItem> get selectedImages =>
      _multiSelectProvider.selectedImages;

  /// 是否为多选模式
  bool get isMultiSelectMode => _multiSelectProvider.isMultiSelectMode;

  /// 是否显示删除确认弹窗
  bool get showDeleteConfirmDialog => _showDeleteConfirmDialog;

  // 图片列表
  final List<AigcPreviewImageItem> _images = [];

  // 选中索引
  int _selectedIndex = -1;

  void _setupRepositoryListener() {
    _currentEditingProjectRepository.workspaceUpdated
        .where((workspace) => workspace != null) // 过滤掉 null 值
        .map((workspace) => workspace!) // 确保非空
        .listen((workspace) async {
      // 按创建时间排序
      final sortedFiles = List<WorkspaceFile>.from(workspace.files)
        ..sort((a, b) => a.createTime.compareTo(b.createTime));

      // 转换为预览图片项 - 使用批量转换方法，更高效
      _images.clear();
      _images.addAll(
        _aigcPreviewImageItemAdapter.fromWorkspaceFiles(sortedFiles),
      );

      // 默认选中第一个item
      if (_selectedIndex == -1) {
        selectImage(0);
      }

      // 同步更新后的数据到_imageProvider
      final selectedImage = _imageProvider.selectedImage;
      if (selectedImage != null) {
        final index =
            _images.indexWhere((img) => img.fileId == selectedImage.fileId);
        if (index >= 0 && index < _images.length) {
          _imageProvider.selectedImage = _images[index];
        }
      }

      notifyListeners();
    });
  }

  void _setupThumbnailListener() {
    _resultStreamSubscription =
        _processingService.resultStream.listen((message) {
      PGLog.d(
          'ThumbnailInterface: 收到结果消息 - taskType: ${message.processorKey}, inputPath: ${message.payload.inputPath}');
      _onMediaStateChanged(message);
    });

    // 监听进度流，过滤缩略图相关进度
    _progressStreamSubscription =
        _processingService.progressStream.listen((message) {
      if (AigcTaskType.values.byName(message.processorKey) ==
          AigcTaskType.imageAssets) {
        // TODO Update progress
      }
    });
  }

  Future<void> requestImageTasks(int index) async {
    final imageData = _images[index];
    PGLog.d(
        'ViewModel: 请求缩略图 - index: $index, path: ${imageData.originalPath}');

    final executeNow = index == selectedIndex;

    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId == null) {
      PGLog.d('ThumbnailListViewModel: 无法提交缩略图任务 - 当前工作空间为空');
      return;
    }

    if (!imageData.readyToDisplay) {
      final outputPath = _mediaRepository
          .getResourceFilePath(
            workspaceId,
            imageData.fileId,
            MediaResourceType.previewResource,
          )
          .path;

      // 提交预览图任务
      _processingService.submitTask(
        inputPath: imageData.originalPath,
        outputPath: outputPath,
        fileId: imageData.fileId,
        taskType: AigcTaskType.imageAssets,
        sortBy: index,
        // 直接传入图片索引
        executeNow: executeNow, // 选中的图片立即执行
      );
    }

    if (!imageData.isThumbnailReady) {
      // 同时提交封面任务
      final iconOutputPath = _mediaRepository
          .getResourceFilePath(
            workspaceId,
            imageData.fileId,
            MediaResourceType.minIcon,
          )
          .path;

      _processingService.submitTask(
        inputPath: imageData.originalPath,
        outputPath: iconOutputPath,
        fileId: imageData.fileId,
        taskType: AigcTaskType.thumbnail,
        sortBy: index,
        executeNow: executeNow,
      );
    }
  }

  void releaseImageTasks(int index) {
    // 💡 安全检查：如果已经disposed或索引无效，则不执行释放操作
    if (index < 0 || index >= _images.length) {
      PGLog.d('ViewModel: 跳过无效索引的图片任务释放 - index: $index');
      return;
    }

    final imageData = _images[index];

    // 释放缩略图任务
    _processingService.removeTask(
      inputPath: imageData.originalPath,
      taskType: AigcTaskType.imageAssets,
    );

    // 释放封面任务
    _processingService.removeTask(
      inputPath: imageData.originalPath,
      taskType: AigcTaskType.thumbnail,
    );

    PGLog.d('ViewModel: 释放图片任务 index=$index, path=${imageData.originalPath}');
  }

  /// 选择相邻图片
  /// [isNextImage] 是否选中下一张图片。这里只会存在选上一张或者下一张的情况
  Future<void> selectAdjacentImage({required bool isNextImage}) async {
    if (_images.isEmpty) {
      return;
    }
    final direction = isNextImage ? 1 : -1;
    int newIndex = _selectedIndex + direction;
    if (newIndex < 0 || newIndex >= _images.length) {
      return;
    }

    await selectImage(newIndex);
  }

  /// 选择图片
  Future<void> selectImage(int index, {bool isControlPressed = false}) async {
    if (index < 0 || index >= _images.length) {
      return;
    }

    // 当前提示绑定避免异常
    if (_reconnectingFileId != null) {
      return;
    }

    await _guardFile(index);

    // 处理多选逻辑
    if (isControlPressed) {
      // 按下Ctrl键，进入或保持多选模式
      if (!_multiSelectProvider.isMultiSelectMode) {
        _multiSelectProvider.setMultiSelectMode(
            enabled: true, withInitialImage: _imageProvider.selectedImage);
      } else {
        if (index == _selectedIndex) {
          // 需求要求当前选中的无法取消多选选中
          return;
        }
      }
      final file = _images[index];
      // 刷新一下数据，确保数据是最新的
      final workspaceId =
          _currentEditingProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId != null) {
        _images[index] = _aigcPreviewImageItemAdapter.refreshMaskPaths(
          file,
          workspaceId,
        );
      }
      // 如果当前图片没有准备好显示，就不能抠图
      if (!_images[index].readyToDisplay) {
        return;
      }
      _multiSelectProvider.toggleSelectedImage(_images[index]);
    } else {
      // 没有按下Ctrl键
      if (_multiSelectProvider.isMultiSelectMode) {
        // 退出多选模式，保持选中当前点击的item
        _multiSelectProvider.setMultiSelectMode(enabled: false);
      }

      _imageProvider.selectedImage = _images[index];
    }
  }

  Future<void> _guardFile(int index) async {
    final file = _images[index];
    // 刷新一下数据，确保数据是最新的
    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId != null) {
      _images[index] = _aigcPreviewImageItemAdapter.refreshMaskPaths(
        file,
        workspaceId,
      );
    }
    if (!file.readyToDisplay) {
      // 判断资源状态假设关键资源不存在则
      final originalFile = File(file.originalPath);
      if (!originalFile.existsSync()) {
        try {
          await _tryReconnectFile(file.fileId);
        } catch (e) {
          PGLog.e('ThumbnailListViewModel: 文件重新连接失败，取消选中操作 - $e');

          // 文件重连失败时，如果在多选模式下，从多选中移除该项
          if (_multiSelectProvider.isMultiSelectMode) {
            _multiSelectProvider.removeSelectedImage(file);
            // 如果没有选中项了，退出多选模式
            if (_multiSelectProvider.selectedImages.isEmpty) {
              _multiSelectProvider.setMultiSelectMode(enabled: false);
            }
          }
        }
      }
    }
  }

  void _onMediaStateChanged(AigcTaskResultMessage message) async {
    final taskType = AigcTaskType.values.byName(message.processorKey);
    if (taskType == AigcTaskType.imageAssets) {
      _onPreviewImagesUpdated(message);
    } else if (taskType == AigcTaskType.thumbnail) {
      _onThumbnailUpdated(message);
    } else {
      PGLog.d(
          'ThumbnailInterface: 忽略非处理消息 - taskType: ${message.processorKey}');
    }
  }

  // 当封面更新时调用，只刷新对应的缩略图组件而不是全量刷新
  Future<void> _onThumbnailUpdated(AigcTaskResultMessage message) async {
    final fileId = message.payload.fileId;
    final result = message.resultData;

    WorkspaceFile? file =
        await _currentEditingProjectRepository.getFile(fileId);
    if (file == null) {
      PGLog.w('_onCoverUpdated: 文件不存在 - fileId: $fileId');
      return;
    }

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.w('_onCoverUpdated: 工作空间为空');
      return;
    }

    // 处理封面生成失败的情况
    if (result == null) {
      PGLog.w('_onCoverUpdated: 封面生成失败 - fileId: $fileId');
      return;
    }

    // 处理封面文件保存
    final taskResult = message.resultData as AigcThumbnailTaskResult;
    final thumbnailPath = taskResult.thumbnailPath;

    if (thumbnailPath != null && thumbnailPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.minIcon,
        File(thumbnailPath),
      );

      PGLog.d(
          '_onCoverUpdated: 封面更新成功 - fileId: $fileId, path: $thumbnailPath');

      // 找到对应的图片索引并更新其数据
      final imageIndex = _images.indexWhere((img) => img.fileId == fileId);
      if (imageIndex >= 0) {
        // 刷新该图片的数据
        _images[imageIndex] = _aigcPreviewImageItemAdapter.refreshIconPath(
          _images[imageIndex],
          workspace.workspaceId,
        );

        // 只通知特定索引的缩略图组件更新，而不是全量刷新
        notifyListeners();
        PGLog.d('_onCoverUpdated: 已刷新索引 $imageIndex 的缩略图组件');
      }
    }
  }

  void _updateImageSelection(AigcPreviewImageItem selectedImage) {
    _selectedIndex =
        _images.indexWhere((img) => img.fileId == selectedImage.fileId);
    if (-1 == _selectedIndex) {
      return;
    }

    requestImageTasks(_selectedIndex);
    notifyListeners();
    return;
  }

  void _onImageSelectionChanged() {
    final selectedImage = _imageProvider.selectedImage;
    if (null == selectedImage) {
      return;
    }

    if (_selectedIndex < 0 || _selectedIndex >= _images.length) {
      _updateImageSelection(selectedImage);
      return;
    }

    final localSelectedImage = _images[_selectedIndex];
    if (selectedImage.fileId != localSelectedImage.fileId) {
      _updateImageSelection(selectedImage);
    } else {
      // 如果有更新，则同步更新_images列表中的对应项
      if (selectedImage != localSelectedImage) {
        _images[_selectedIndex] = selectedImage;
        notifyListeners();
        return;
      }
      if (!selectedImage.readyToDisplay && selectedImage.iconized == false) {
        requestImageTasks(_selectedIndex);
      }
    }
    PGLog.d('ThumbnailListViewModel: 选中图片未发生变化 - $selectedImage');
  }

  // 当缩略图等资源更新调用
  Future<void> _onPreviewImagesUpdated(AigcTaskResultMessage message) async {
    final fileId = message.payload.fileId;
    final result = message.resultData;

    WorkspaceFile? file =
        await _currentEditingProjectRepository.getFile(fileId);
    if (file == null) {
      return;
    }

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    // 处理缩略图生成失败的情况
    if (result == null) {
      // 标记为已处理（iconized = true），但由于没有生成预览图，readyToDisplay 将为 false
      // 这样就符合了图片损坏的判断条件：iconized == true && readyToDisplay == false
      file = file.copyWith(iconized: true);
      _currentEditingProjectRepository.updateFile(file);
      PGLog.w('ThumbnailListViewModel: 缩略图生成失败，标记为损坏 - fileId: $fileId');
      return;
    }

    // 更新资源存储
    final taskResult = message.resultData as AigcImageAssetsTaskResult;

    // 处理高清图
    final highQualityPath = taskResult.highQualityPath;
    if (highQualityPath != null && highQualityPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.largeResource,
        File(highQualityPath),
      );
    }

    // 处理预览图
    final previewPath = taskResult.previewPath;
    if (previewPath != null && previewPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.previewResource,
        File(previewPath),
      );

      // 同步数据库
      file = file.copyWith(iconized: true);
      _currentEditingProjectRepository.updateFile(file);
    }

    /**
     *  这里存在一定的隐患，只有当小icon生成成功后，才会更新_images列表，如果小icon生成失败，
     * 则_images列表不会更新，由于现在预览图和小图是一组任务，理论上不存在失败的情况，但
     * 假设后期做了拆分一定要注意这里的处理
     */
  }

  /// 处理选择文件
  Future<List<File>> handleFileSelection(ImageSelectionType type) async {
    try {
      DealImageFilesResult? result;
      switch (type) {
        case ImageSelectionType.files:
          result = await _imageSelectionService.pickImagesFromFiles();
          break;
        case ImageSelectionType.folder:
          result = await _imageSelectionService.pickImagesFromDirectory();
          break;
      }

      if (result != null && result.validFiles.isNotEmpty) {
        return await _addFilesToCurrentProject(result.validFiles);
      }
      return [];
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 选择文件时出错: $e');
      return [];
    }
  }

  /// 将文件添加到当前编辑项目
  Future<List<File>> _addFilesToCurrentProject(List<File> files) async {
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return [];
    }

    try {
      final workspaceFiles = await _mediaRepository.generateWorkspaceFiles(
          workspace.workspaceId, files);

      _currentEditingProjectRepository.batchAddFiles(workspaceFiles);
      return files;
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 添加文件到项目时出错: $e');
      return [];
    }
  }

  /// 删除当前选中的图片
  Future<void> deleteSelectedImage() async {
    if (_selectedIndex == -1 || _selectedIndex >= _images.length) {
      return;
    }

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    try {
      if (_multiSelectProvider.isMultiSelectMode &&
          _multiSelectProvider.selectedImages.isNotEmpty) {
        // 多选模式下删除所有选中的图片
        await _deleteMultipleImages();
      } else {
        // 单选模式下删除当前选中的图片
        await _deleteSingleImage();
      }

      notifyListeners();
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 删除图片时出错: $e');
    }
  }

  /// 删除单个图片
  Future<void> _deleteSingleImage() async {
    final selectedImage = _images[_selectedIndex];

    // 从repository中删除文件
    await _currentEditingProjectRepository.deleteFile(selectedImage.fileId);

    // 从本地列表中移除
    _images.removeAt(_selectedIndex);

    // 调整选中索引
    if (_images.isEmpty) {
      _selectedIndex = -1;
      _imageProvider.selectedImage = null;
    } else {
      // 如果删除的是最后一个，选中前一个；否则保持当前索引
      if (_selectedIndex >= _images.length) {
        _selectedIndex = _images.length - 1;
      }
      _imageProvider.selectedImage = _images[_selectedIndex];
    }
  }

  /// 删除多个图片
  Future<void> _deleteMultipleImages() async {
    final selectedIds =
        Set<AigcPreviewImageItem>.from(_multiSelectProvider.selectedImages)
            .map((e) => e.fileId)
            .toList();

    // 找出要删除的图片
    final imagesToDelete =
        _images.where((image) => selectedIds.contains(image.fileId)).toList();

    if (imagesToDelete.isEmpty) {
      return;
    }

    try {
      // 并行删除文件，提升性能
      final deleteResults = await Future.wait(
        imagesToDelete.map((image) => _currentEditingProjectRepository
                .deleteFile(image.fileId)
                .then((_) => image.fileId) // 返回成功删除的文件ID
                .catchError((error) {
              PGLog.e('删除文件失败: ${image.fileId} - $error');
              return ''; // 返回空字符串而不是null
            })),
        eagerError: false, // 不因单个失败而停止整个操作
      );

      // 统计删除结果
      final successfullyDeletedIds =
          deleteResults.where((id) => id.isNotEmpty).toSet();

      // 从本地列表中移除成功删除的图片
      _images.removeWhere(
          (image) => successfullyDeletedIds.contains(image.fileId));

      // 退出多选模式
      _multiSelectProvider.setMultiSelectMode(enabled: false);

      // 调整选中索引
      if (_images.isEmpty) {
        _selectedIndex = -1;
        _imageProvider.selectedImage = null;
      } else {
        // 选中第一个可用的图片
        _selectedIndex = 0;
        _imageProvider.selectedImage = _images[0];
      }
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 删除图片时出错: $e');
    }
  }

  /// 请求显示删除确认弹窗
  void requestDeleteConfirmation() {
    if (_showDeleteConfirmDialog) {
      // 避免重复弹出确认弹窗
      return;
    }
    // 检查是否有选中的图片
    if (_selectedIndex == -1 || _selectedIndex >= _images.length) {
      return;
    }

    _showDeleteConfirmDialog = true;
    notifyListeners();
  }

  /// 隐藏删除确认弹窗
  void hideDeleteConfirmation() {
    _showDeleteConfirmDialog = false;
    notifyListeners();
  }

  /// 确认删除操作
  Future<void> confirmDelete() async {
    // 先隐藏弹窗
    hideDeleteConfirmation();
    // 执行删除
    await deleteSelectedImage();
  }

  /// 进入多选模式（用于框选）
  void enterMultiSelectMode() {
    if (!_multiSelectProvider.isMultiSelectMode) {
      _multiSelectProvider.setMultiSelectMode(enabled: true);
    }
  }

  /// 选中当前所有照片进入多选模式
  void selectAllImages() {
    if (_images.isEmpty) {
      return;
    }
    if (!_multiSelectProvider.isMultiSelectMode) {
      _multiSelectProvider.setMultiSelectMode(enabled: true);
    }

    for (var index = 0; index < _images.length; index++) {
      final file = _images[index];
      // 刷新一下数据，确保数据是最新的
      final workspaceId =
          _currentEditingProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId != null) {
        _images[index] = _aigcPreviewImageItemAdapter.refreshMaskPaths(
          file,
          workspaceId,
        );
      }
    }

    // 获取_images中所有的没有准备好显示的图片
    final validImages =
        _images.where((element) => element.readyToDisplay).toList();
    _multiSelectProvider.batchSelectedImages(
        items: validImages, overrideMode: true);
  }

  /// 批量选择图片（用于框选）
  void batchSelectImages(List<int> indices) {
    // 先进入多选模式
    if (!_multiSelectProvider.isMultiSelectMode) {
      _multiSelectProvider.setMultiSelectMode(enabled: true);
    }

    final selectedImages = indices.map((index) => _images[index]).toList();

    for (var index = 0; index < selectedImages.length; index++) {
      final file = selectedImages[index];
      // 刷新一下数据，确保数据是最新的
      final workspaceId =
          _currentEditingProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId != null) {
        selectedImages[index] = _aigcPreviewImageItemAdapter.refreshMaskPaths(
          file,
          workspaceId,
        );
      }
    }
    // 过滤掉没有准备好显示的图片
    final validImages =
        selectedImages.where((element) => element.readyToDisplay).toList();
    _multiSelectProvider.batchSelectedImages(
        items: validImages, overrideMode: true);
  }

  /// 清空框选选择
  void clearBoxSelection() {
    if (_multiSelectProvider.isMultiSelectMode) {
      _multiSelectProvider.setMultiSelectMode(enabled: false);
    }
  }

  @override
  void dispose() {
    // 取消所有流订阅
    _resultStreamSubscription?.cancel();
    _progressStreamSubscription?.cancel();
    _eventStreamSubscription?.cancel();
    _multiSelectEventStreamSubscription?.cancel();
    _maskTaskSubscription?.cancel();
    _imageProvider.removeListener(_onImageSelectionChanged);

    // 最后释放图片相关资源，这样即使触发notifyListeners也不会影响已disposed的组件
    // 💡 关键修复：在dispose之前先获取需要释放的图片路径
    _processingService.removeTasksByType(AigcTaskType.imageAssets);
    _processingService.removeTasksByType(AigcTaskType.thumbnail);

    super.dispose();
  }

  // 尝试重新链接
  Future<WorkspaceFile?> _tryReconnectFile(String fileId) async {
    final file = await _currentEditingProjectRepository.getFile(fileId);
    if (file == null) {
      PGLog.d('ThumbnailListViewModel: 文件不存在 - fileId: $fileId');
      return null;
    }
    _reconnectingFileId = fileId;
    // 使用 Completer 来阻塞等待用户操作完成
    final completer = Completer<WorkspaceFile?>();

    AigcReconnectFileDialog.show(
      fileName: file.fileName,
      onConfirm: () async {
        await AigcReconnectFileDialog.hide();
        PGDialog.showLoading();
        try {
          final newFile = await _reconnectFile(fileId);
          completer.complete(newFile); // 重新连接成功
          PGDialog.dismiss();
        } catch (e) {
          PGDialog.dismiss();
          PGLog.e('ThumbnailListViewModel: 重新连接文件失败 - $e');
          completer.complete(null); // 重新连接失败
        }
      },
      onCancel: () {
        AigcReconnectFileDialog.hide();
        completer.complete(null); // 用户取消操作
      },
    );

    // 阻塞等待用户操作完成
    final result = await completer.future;
    _reconnectingFileId = null;
    if (result == null) {
      PGLog.d('ThumbnailListViewModel: 文件重新连接未完成或失败 - fileId: $fileId');
      // 可以选择抛出异常或返回特定状态
      throw Exception('文件重新连接失败或用户取消操作');
    }

    PGLog.d('ThumbnailListViewModel: 文件重新连接成功 - fileId: $fileId');
    return result;
  }

  // 链接文件
  Future<WorkspaceFile> _reconnectFile(String fileId) async {
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.d('ThumbnailListViewModel: 当前工作空间为空');
      throw Exception('当前工作空间为空');
    }

    final preFile = await _currentEditingProjectRepository.getFile(fileId);
    if (preFile == null) {
      PGLog.d('ThumbnailListViewModel: 文件不存在 - fileId: $fileId');
      throw Exception('文件不存在');
    }

    try {
      // 使用文件选择服务让用户选择新的文件
      final result = await _imageSelectionService.pickImagesFromFiles();

      if (result == null || result.validFiles.isEmpty) {
        throw Exception('用户未选择文件');
      }

      final temp = result.validFiles.first; // 只取第一个文件
      PGLog.d('ThumbnailListViewModel: 用户选择新文件 - path: ${temp.path}');

      // 检查文件是否已存在于当前工作区，目前不允许导入已存在的文件
      final fileExisted = await _currentEditingProjectRepository
          .isFileExistedInWorkspace(temp.path);
      if (fileExisted) {
        throw Exception('文件已存在于当前工作区');
      }

      final workspaceFiles = await _mediaRepository.generateWorkspaceFiles(
        workspace.workspaceId,
        [temp],
      );
// 由于在业务层以创建时间排序，为了达到替换的效果，则需要将新文件的创建时间设置为旧文件的创建时间
      final newFile = workspaceFiles.first.copyWith(
        createTime: preFile.createTime,
      );

      // 删除关联资源，重新生成
      final res = [
        MediaResourceType.mask,
        MediaResourceType.minIcon,
        MediaResourceType.previewResource,
        MediaResourceType.largeResource,
      ];

      for (final resourceType in res) {
        await _mediaRepository.deleteFileResource(
          workspace.workspaceId,
          fileId,
          resourceType,
        );
      }

      // 删除临时文件中所有的蒙版
      _fileManager
          .getTempInteractiveMaskDirectory(
            workspace.workspaceId,
            fileId,
          )
          .deleteSync(recursive: true);

      // 更新数据库
      await _currentEditingProjectRepository.replaceFile(
        fileId,
        newFile,
      );
      return newFile;
    } catch (e) {
      PGLog.e('ThumbnailListViewModel: 重新连接文件失败 - $e');
      rethrow; // 重新抛出异常，让调用者知道操作失败
    }
  }

  // ==================== 蒙版状态管理 ====================

  /// 设置蒙版任务监听器
  void _setupMaskTaskListener() {
    _maskTaskSubscription = _processingService.resultStream.listen((message) {
      // 只处理蒙版任务类型的消息
      final taskType = message.payload.taskType;
      if (taskType == AigcTaskType.mask) {
        _onMaskTaskCompleted(message);
      }
    });
  }

  /// 处理蒙版任务完成
  void _onMaskTaskCompleted(AigcTaskResultMessage message) {
    final fileId = message.payload.fileId;
    // 延迟1秒后执行
    Future.delayed(const Duration(seconds: 1), () {
      if (message.success) {
        // 蒙版获取成功，移除状态（蒙层消失）
        _maskAcquisitionProvider?.clearMaskAcquisitionStatus(fileId);
        PGLog.d('✅ 蒙版任务完成: $fileId');
      } else {
        // 蒙版获取失败，设置为失败状态（蒙层保留并显示失败文案）
        _maskAcquisitionProvider?.setMaskAcquisitionStatus(
            fileId, MaskAcquisitionStatus.failed);
        PGLog.e('❌ 蒙版任务失败: $fileId');
      }
    });
  }

  /// 获取指定图片的蒙版获取状态
  MaskAcquisitionStatus? getMaskAcquisitionStatus(String fileId) {
    return _maskAcquisitionProvider?.getMaskAcquisitionStatus(fileId);
  }

  /// 设置指定图片的蒙版获取状态
  void setMaskAcquisitionStatus(String fileId, MaskAcquisitionStatus status) {
    _maskAcquisitionProvider?.setMaskAcquisitionStatus(fileId, status);
  }

  /// 清除指定图片的蒙版获取状态
  void clearMaskAcquisitionStatus(String fileId) {
    _maskAcquisitionProvider?.clearMaskAcquisitionStatus(fileId);
  }

  /// 清除所有图片的蒙版获取状态
  void clearAllMaskAcquisitionStatus() {
    _maskAcquisitionProvider?.clearAllMaskAcquisitionStatus();
  }
}
