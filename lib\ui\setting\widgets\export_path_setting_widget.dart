import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/provider/export_path_setting_provider.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';
import 'package:turing_art/ui/setting/widgets/radio_setting_widget.dart';

class ExportPathSettingWidget extends StatefulWidget {
  const ExportPathSettingWidget({super.key});

  @override
  State<ExportPathSettingWidget> createState() =>
      _ExportPathSettingWidgetState();
}

class _ExportPathSettingWidgetState extends State<ExportPathSettingWidget> {
  late TextEditingController _suffixController;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _suffixController = TextEditingController();

    // 延迟初始化，等待 ViewModel 加载完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSuffixValue();
    });
  }

  @override
  void dispose() {
    _suffixController.dispose();
    super.dispose();
  }

  void _initializeSuffixValue() {
    final viewModel = context.read<SettingViewModel>();
    final suffixItem = _getSuffixSettingItem(viewModel);
    if (suffixItem != null) {
      _suffixController.text =
          suffixItem.value.isEmpty || suffixItem.value.trim().isEmpty
              ? "_TuringArt"
              : suffixItem.value;
      _updateSuffixValue(_suffixController.text, true);
    }
  }

  SettingItemModel? _getExportToOriginalFolderItem(SettingViewModel viewModel) {
    final items =
        viewModel.getItemsByCategory(SettingCategoryConstant.exportFile);
    return items
        .where((item) => item.key == SettingKeyConstant.exportToOriginalFolder)
        .firstOrNull;
  }

  SettingItemModel? _getSuffixSettingItem(SettingViewModel viewModel) {
    final items =
        viewModel.getItemsByCategory(SettingCategoryConstant.exportFile);
    return items
        .where((item) => item.key == SettingKeyConstant.exportFolderSuffix)
        .firstOrNull;
  }

  bool _isValidSuffix(String value) {
    // 检查是否包含特殊符号：/ \ : * " < > |
    final invalidChars = RegExp(r'[/\\:*?"<>|]');
    return !invalidChars.hasMatch(value);
  }

  void _onSuffixChanged(String value) {
    final isValid = _isValidSuffix(value);
    setState(() {
      _hasError = !isValid;
    });

    _updateSuffixValue(value, isValid);
  }

  void _updateSuffixValue(String value, bool isValid) {
    if (isValid) {
      // 更新设置值
      final viewModel = context.read<SettingViewModel>();
      viewModel.updateSetting(SettingKeyConstant.exportFolderSuffix, value);
    }
    // 同步到ExportPathSettingProvider
    final exportPathProvider = context.read<ExportPathSettingProvider>();
    exportPathProvider.setExportPath(path: value);
  }

  String _getStatusText(bool hasValidationError, bool isValidExportPath) {
    if (_hasError) {
      return '请不要输入特殊符号（如 / \\ : * ? " < > |）';
    } else if (!isValidExportPath) {
      return '请不要输入特殊符号（如 / \\ : * ? " < > |）';
    } else {
      return '*不会修改ERP导出时的后缀';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<SettingViewModel, ExportPathSettingProvider>(
      builder: (context, viewModel, exportPathProvider, child) {
        final exportToOriginalItem = _getExportToOriginalFolderItem(viewModel);
        final suffixItem = _getSuffixSettingItem(viewModel);

        if (exportToOriginalItem == null || suffixItem == null) {
          return const SizedBox.shrink();
        }

        // 合并本地验证错误和路径权限错误
        final hasValidationError =
            _hasError || !exportPathProvider.isValidExportPath;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 使用原有的RadioSettingWidget控件
            RadioSettingWidget(
              categoryKey: SettingCategoryConstant.exportFile,
              settingItem: exportToOriginalItem,
            ),

            // 提示文本
            Padding(
              padding: const EdgeInsets.only(left: 0, top: 8),
              child: Text(
                '开启后，导出时可选择导出位置选项。',
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.white.withOpacity(0.4),
                  height: 20 / 12,
                ),
              ),
            ),

            // 后缀名标题和输入框行
            Padding(
              padding: const EdgeInsets.only(left: 0, top: 40, right: 0),
              child: Row(
                children: [
                  // 后缀名标题文本
                  Text(
                    suffixItem.title,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.white.withOpacity(0.7),
                      height: 16 / 14,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 文本输入框
                  Expanded(
                    child: Container(
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2B2B2B),
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(
                          color: hasValidationError
                              ? const Color(0xFFF55C44)
                              : Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: _suffixController,
                        onChanged: _onSuffixChanged,
                        maxLength: 255,
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white.withOpacity(0.7),
                          height: 1.2, // 控制行高
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding:
                              EdgeInsets.fromLTRB(8, 10, 8, 8), // 精确控制内边距
                          counterText: '', // 隐藏字符计数器
                          isDense: true,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 状态文本
            Padding(
              padding: const EdgeInsets.only(right: 0, top: 8),
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  _getStatusText(
                      hasValidationError, exportPathProvider.isValidExportPath),
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: hasValidationError
                        ? const Color(0xFFF55C44)
                        : Colors.white.withOpacity(0.4),
                    height: 16 / 12,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
