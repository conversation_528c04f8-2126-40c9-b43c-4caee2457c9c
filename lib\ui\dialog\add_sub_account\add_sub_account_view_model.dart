import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/api_error/api_error.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

class AddSubAccountViewModel extends ChangeNotifier {
  final EmployeeRepository _employeeRepository;

  AddSubAccountViewModel({
    required EmployeeRepository employeeRepository,
  }) : _employeeRepository = employeeRepository;

  // 发送验证码
  void sendVerificationCode() {
    _employeeRepository.sendVerificationCode();
  }

  // 添加子账号
  Future<ApiError?> addSubAccount(
      String code, List<EmployeeInfo> employees) async {
    try {
      // 添加子账号 - 使用固定的验证码和示例员工信息
      final apiError = await _employeeRepository.addEmployees(
        code: code, // 固定验证码
        employees: employees,
      );

      if (apiError == null) {
        // 刷新数据
        _employeeRepository.getEmployeeList();
        _employeeRepository.getEmployeeSummary();
        return null;
      }
      return apiError;
    } catch (e) {
      PGLog.e('添加子账号失败: $e');
      return const ApiError(
        code: -1,
        message: '添加子账号失败',
      );
    }
  }

  // 检查手机号是否有效
  bool isValidPhoneNumber(String phone) {
    if (phone.isEmpty) {
      return false;
    }

    // 检查开始和结尾是否为"+"
    if (phone.startsWith('+') || phone.endsWith('+')) {
      return false;
    }

    // 检查是否只包含数字和最多一个"+"
    final plusCount = '+'.allMatches(phone).length;
    if (plusCount > 1) {
      return false;
    }

    // 检查是否只包含数字和"+"
    final validChars = RegExp(r'^[0-9+]+$');
    if (!validChars.hasMatch(phone)) {
      return false;
    }

    // 如果包含"+"，则为海外格式，不限制位数
    if (phone.contains('+')) {
      // 海外格式：区号+手机号，比如"86+13678156435"
      final parts = phone.split('+');
      if (parts.length != 2 || parts[0].isEmpty || parts[1].isEmpty) {
        return false;
      }
      return true;
    } else {
      // 大陆地区手机号，按原有逻辑，11位数字
      if (phone.length != 11) {
        return false;
      }
      return true;
    }
  }
}
