import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/store/rewards.dart';

part 'store.freezed.dart';
part 'store.g.dart';

@freezed
class Store with _$Store {
  const factory Store({
    required String id,
    required String tel,
    String? name,
    String? address,
    String? customerSupport,
    bool? volumeControl, // 是否开启账号管理（片量共享入口）
    // 奖励
    Rewards? rewards,
    // 能力列表
    @Default([]) List<String> capabilities,
  }) = _Store;
  const Store._();

  factory Store.fromJson(Map<String, dynamic> json) => _$StoreFromJson(json);

  // 判断店铺是否具有导出加密样片的权益
  bool couldExportSample() =>
      //  macos 系统暂时不支持导出加密样片功能
      Platform.isMacOS ? false : capabilities.contains('exportSample');
  // bool couldExportSample() => true;
}
