import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:pg_turing_collect_event/model.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/dialog/shortcut_keys_dialog.dart';
import 'package:turing_art/ui/export_history/viewModel/export_history_view_model.dart';
import 'package:turing_art/ui/project_home/services/profile_function_list_show_service.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_view_model.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_dialog.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_qr_code_dialog.dart';
import 'package:turing_art/ui/version_intro/widgets/version_intro_dialog.dart';
import 'package:turing_art/ui/version_update/widgets/version_update_dialog.dart';
import 'package:turing_art/ui/wechat_gift/widgets/wechat_gift_dialog.dart';
import 'package:turing_art/utils/custom_service_handler.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

/// 个人中心视图点击事件服务
///
/// 专门处理ProjectHomePcProfileView的所有点击事件，简化业务逻辑。
///
/// ## 设计原则
/// - 不依赖ViewModel，只依赖数据源Repository
/// - 使用静态方法，便于在多个页面复用
/// - 专门处理UI展示逻辑
/// ## 优势
/// 1. **代码复用**：避免在多个页面重复相同的业务逻辑
/// 2. **统一管理**：所有个人中心相关的点击事件集中管理
/// 3. **易于维护**：修改业务逻辑只需要在一个地方修改
/// 4. **架构清晰**：Service层专注于业务逻辑，不依赖UI状态
class ProfileViewClickService {
  /// 处理用户账号点击事件
  static void handleUserAccountClick({
    required BuildContext context,
    required GlobalKey? profileViewKey,
    required GlobalKey? userCardKey,
    ExportHistoryType defaultExportType = ExportHistoryType.export,
  }) {
    final user = context.read<CurrentUserRepository>().user;
    final store = context.read<CurrentUserRepository>().store;
    final userId = user?.effectiveId ?? '';

    ProfileFunctionListShowService.showProfileDialog(
      context: context,
      profileViewKey: profileViewKey ?? GlobalKey(),
      userCardKey: userCardKey ?? GlobalKey(),
      isSubAccount: user?.role == UserRole.employee,
      isOpenAccountManagement: store?.volumeControl ?? false,
      navigatorService: GoRouterNavigatorService(context),
      defaultExportType: defaultExportType,
      userId: userId,
    );

    recordHomeMy(
        userId: userId,
        clickTime: DateTimeUtil.getCurrentTimestampSec().toString());
  }

  /// 处理完善信息按钮点击事件
  static Future<void> handleShowWechatGiftDialog({
    required BuildContext context,
  }) async {
    bool isLoading = false;
    final accountRightsStateProvider =
        context.read<AccountRightsStateProvider>();
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordHomeInfoReward(userId: userId, clickTime: clickTime);

    try {
      // 显示加载状态
      if (PGDialog.isDialogVisible(DialogTags.loading)) {
        PGDialog.dismiss(tag: DialogTags.loading);
      }

      PGDialog.showLoading();
      isLoading = true;

      // 直接调用数据源获取微信礼包数据
      final wechatGiftList =
          await context.read<WechatGiftRepository>().getWechatGiftInfo();

      // 隐藏加载状态
      await PGDialog.dismiss(tag: DialogTags.loading);
      isLoading = false;

      if (wechatGiftList.isNotEmpty) {
        WechatGiftDialog.show(wechatGiftList);
        PGLog.d('ProfileViewClickService - 弹起微信弹窗后，开始轮询账户权益状态');

        // 弹起微信弹窗后，开始轮询账户权益状态
        accountRightsStateProvider.startAccountRightCheck();
      } else {
        PGDialog.showToast('获取微信礼包权益失败,请稍后重试');
      }
    } catch (e) {
      PGLog.e('获取微信礼包失败: $e');
      if (isLoading) {
        await PGDialog.dismiss(tag: DialogTags.loading);
      }
      PGDialog.showToast('获取微信礼包权益失败,请稍后重试');
    }
  }

  /// 处理购买套餐按钮点击事件
  static void handleBuyPackageClick(
    BuildContext context, {
    SourceType sourceType = SourceType.home_page,
    PurchaseTabType? defaultTab,
  }) {
    PurchaseDialog.show(
      context,
      (payChannelList, orderId, collectInfo) {
        // 订单创建成功后，显示支付二维码，传递可支付渠道马上创建UI
        PurchaseQRCodeDialog.show(payChannelList, orderId, collectInfo);
      },
      sourceType,
      defaultTab: defaultTab,
    );
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordHomePurchase(userId: userId, clickTime: clickTime);
  }

  /// 处理快捷指南点击事件
  static void handleGuideClick({
    required BuildContext context,
  }) {
    // 打开网页
    const url = 'https://help.turing.art/';
    UrlLauncherUtil.openInSystemBrowser(url);

    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordOpGuide(userId: userId, clickTime: clickTime);
  }

  /// 处理快捷键点击事件
  static void handleShortKeyClick({required BuildContext context}) {
    ShortcutKeysDialog.show();

    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordShortcutKeys(userId: userId, clickTime: clickTime);
  }

  /// 处理联系客服点击事件
  static void handleCustomerServiceClick({required BuildContext context}) {
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordContactAfterSales(userId: userId, clickTime: clickTime);

    CustomServiceHandler.showCustomerService('客服');
  }

  /// 处理检查更新点击事件
  static Future<void> handleCheckUpdateClick({
    required BuildContext context,
  }) async {
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordCheckUpdateBtn(userId: userId, clickTime: clickTime);

    await UpdateManager().checkUpdateVersion(isLaunch: false).then((result) {
      if (result.message != null && result.message != '') {
        PGDialog.showToast(result.message!);
      }
      if (result.isUpdateVersion != null &&
          result.isUpdateVersion == true &&
          context.mounted) {
        VersionUpdateDialog.show(context);
      }
    });
  }

  /// 处理版本介绍点击事件
  static Future<void> handleVersionIntroduceClick({
    required BuildContext context,
  }) async {
    bool isLoading = false;
    final versionIntroRepository = context.read<VersionIntroRepository>();
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? '';
    final clickTime = DateTimeUtil.getCurrentTimestampSec().toString();
    recordReleaseNotes(userId: userId, clickTime: clickTime);

    try {
      // 显示加载状态
      if (PGDialog.isDialogVisible(DialogTags.loading)) {
        PGDialog.dismiss(tag: DialogTags.loading);
      }

      PGDialog.showLoading();
      isLoading = true;

      // 直接调用数据源获取版本介绍数据
      final data = await versionIntroRepository.getVersionIntroInfo();

      // 确保在弹起弹窗前隐藏loading
      await PGDialog.dismiss(tag: DialogTags.loading);
      isLoading = false;

      if (data.isEmpty) {
        PGLog.d('没有可显示的版本介绍内容');
        PGDialog.showToast('暂无版本介绍信息');
        return;
      }

      if (context.mounted) {
        if (data.length > 1) {
          VersionIntroDialog.show(data);
        } else {
          PGDialog.showToast('版本介绍配置不正确，至少有标题和一个条目');
        }
      }
    } catch (e) {
      PGLog.e('显示版本介绍弹窗失败: $e');
      if (isLoading) {
        await PGDialog.dismiss(tag: DialogTags.loading);
      }
      PGDialog.showToast('获取版本介绍失败');
    }
  }
}
