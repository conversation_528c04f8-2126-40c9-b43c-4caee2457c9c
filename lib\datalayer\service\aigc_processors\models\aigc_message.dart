// ==================== AIGC 特定的消息类型 ====================
///
/// 消息创建使用示例：
/// ```dart
/// // 创建任务消息
/// final taskMessage = AigcTaskMessage(
///   taskId: 'task_001',
///   processorKey: 'thumbnail_processor',
///   payload: task,
/// );
///
/// // 使用从父类继承的通用方法
/// final startMessage = taskMessage.createStartMessage();
/// final progressMessage = taskMessage.createInProgressMessage(
///   currentStep: '正在生成缩略图',
///   progress: 0.5,
/// );
///
/// // 使用通用的成功/失败消息方法
/// final successMessage = taskMessage.createSuccessMessage<AigcTaskResult>(
///   resultData: result,
///   processingTime: Duration(seconds: 10),
/// );
///
/// final failureMessage = taskMessage.createFailureMessage<AigcTaskResult>(
///   errorMessage: '处理失败: 文件格式不支持',
///   processingTime: Duration(seconds: 5),
/// );
///
/// // 使用 AIGC 特定的类型化方法
/// final typedSuccessMessage = taskMessage.createTypedSuccessMessage(
///   processingTime: Duration(seconds: 10),
///   previewPath: '/path/to/preview.jpg',
///   thumbnailPath: '/path/to/thumbnail.jpg',
/// );
/// ```
library;

import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_message.dart';

/// AIGC任务消息
class AigcTaskMessage extends GenericTaskMessage<AigcTask, AigcTaskResult> {
  AigcTaskMessage({
    required super.taskId,
    required super.processorKey,
    required super.payload,
  });

  // ==================== 实现父类抽象方法 ====================
  @override
  AigcTaskResultMessage createResultMessage({
    required bool success,
    AigcTaskResult? resultData,
    String? errorMessage,
    required Duration processingTime,
  }) {
    return AigcTaskResultMessage(
      taskId: taskId,
      processorKey: processorKey,
      success: success,
      resultData: resultData,
      errorMessage: errorMessage,
      processingTime: processingTime,
      payload: payload,
    );
  }

  @override
  AigcTaskProgressMessage createProgressMessage({
    required String status,
    required double progress,
  }) {
    return AigcTaskProgressMessage(
      taskId: taskId,
      processorKey: processorKey,
      status: status,
      progress: progress,
      payload: payload,
    );
  }

  // ==================== AIGC 特定的类型化便捷方法 ====================

  /// 根据任务类型创建带进度的常用状态消息
  AigcTaskProgressMessage createTypedProgressMessage({
    required double progress,
    String? customStatus,
  }) {
    final taskType = payload.taskType;
    final status =
        customStatus ?? _getDefaultProgressStatus(taskType, progress);

    return createProgressMessage(
      status: status,
      progress: progress,
    );
  }

  /// 获取默认的进度状态文本
  String _getDefaultProgressStatus(AigcTaskType taskType, double progress) {
    final typeName = taskType.displayName;

    if (progress == 0.0) {
      return '开始$typeName';
    } else if (progress < 0.3) {
      return '正在预处理...';
    } else if (progress < 0.7) {
      return '正在进行$typeName...';
    } else if (progress < 1.0) {
      return '正在后处理...';
    } else {
      return '$typeName完成';
    }
  }
}

/// AIGC任务结果消息
class AigcTaskResultMessage
    extends GenericTaskResultMessage<AigcTask, AigcTaskResult> {
  AigcTaskResultMessage({
    required super.taskId,
    required super.processorKey,
    required super.success,
    super.resultData,
    super.errorMessage,
    required super.processingTime,
    required super.payload,
  });
}

/// AIGC任务进度消息
class AigcTaskProgressMessage extends GenericTaskProgressMessage<AigcTask> {
  AigcTaskProgressMessage({
    required super.taskId,
    required super.processorKey,
    required super.status,
    required super.progress,
    required super.payload,
  });
}
